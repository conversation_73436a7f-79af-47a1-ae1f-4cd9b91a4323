﻿using Autodesk.Revit.DB;
using MEP.MaxDemand.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Form = System.Windows.Forms.Form;

namespace MEP.MaxDemand.UI.ModelessRevitForm
{
    public partial class FrmProjectInfoSettings : Form
    {
        public ProjectInfo ProjInfo { get; set; }

        public string Engineer { get; set; }
        public string Verifier { get; set; }
        public string ReferenceDrawing { get; set; }
        public string Revision { get; set; }
        public double SiteDiversity { get; set; }
        public double SiteSpareCapacity { get; set; }
        public bool ParameterValueChanged { get; set; }
        
        public FrmProjectInfoSettings(MD_DataModel data)
        {
            InitializeComponent();

            ProjInfo = data.ProjInfo;
            Engineer = data.Engineer;
            Verifier = data.Verifier;
            Revision = data.Revision;
            ReferenceDrawing = data.ReferenceDrawing; // Where to save this? check TB // in settings for now

            SiteSpareCapacity = data.SiteSpareCapacity;
            SiteDiversity = data.SiteDiversity;

            ParameterValueChanged = false;

            PopulateTextBoxes(ProjInfo);
        }

        private void PopulateTextBoxes(ProjectInfo projInfo)
        {
            tb_JobName.Text = projInfo.get_Parameter(BuiltInParameter.PROJECT_NAME)?.AsString() ?? string.Empty; 
            tb_JobNumber.Text = projInfo.get_Parameter(BuiltInParameter.PROJECT_NUMBER)?.AsString() ?? string.Empty;
            tb_Date.Text = DateTime.Now.ToString("dd/MM/yy"); 
            tb_Engineer.Text = Engineer;
            tb_Verifier.Text = Verifier;
            tb_ReferenceDrawing.Text = ReferenceDrawing;
            tb_Revision.Text = Revision;
            cb_SiteDiversity.Text = ConvertToPercentageString(SiteDiversity);
            cb_SiteSpareCapacity.Text = ConvertToPercentageString(SiteSpareCapacity);
        }

        private void btn_Save_Click(object sender, EventArgs e)
        {
            if (ParameterValueIsDifferent())
            {
                // Set the properties if the parameter value is different
                Engineer = tb_Engineer.Text;
                Verifier = tb_Verifier.Text;
                Revision = tb_Revision.Text;
                ReferenceDrawing = tb_ReferenceDrawing.Text;

                SiteDiversity = ConvertComboBoxTextToDouble(cb_SiteDiversity);
                SiteSpareCapacity = ConvertComboBoxTextToDouble(cb_SiteSpareCapacity);

                Properties.Settings.Default.Revision = tb_Revision.Text;
                Properties.Settings.Default.ReferenceDrawing = tb_ReferenceDrawing.Text;
                Properties.Settings.Default.Save();

                ParameterValueChanged = true;

                this.Close();
            }
            else
            {
                MessageBox.Show("There are no changes. Nothing will be saved");

                this.Close();
            }
        }

        private double ConvertComboBoxTextToDouble(ComboBox cb)
        {
            return double.TryParse(
                (cb.SelectedItem?.ToString() ?? cb.Text)?.TrimEnd('%'), out double percentage) 
                ? percentage / 100 : 0.0;
        }

        private void btn_Close_Click(object sender, EventArgs e)
        {
            // Check if parameter value changes, if true confirm saving or not
            if (ParameterValueIsDifferent())
            {
                DialogResult result = MessageBox.Show("There are unsaved changes. Do you want to save before closing?", "Confirm", MessageBoxButtons.YesNo);
                if (result == DialogResult.Yes)
                {
                    Engineer = tb_Engineer.Text;
                    Verifier = tb_Verifier.Text;
                    ReferenceDrawing = tb_ReferenceDrawing.Text;
                    Revision = tb_Revision.Text;
                    ParameterValueChanged = true;  
                }
                else if (result == DialogResult.No)
                {
                    this.Close(); 
                }
            }
            else
            {
                this.Close(); 
            }
        }

        private bool ParameterValueIsDifferent() 
        {
            return cb_SiteDiversity.Text != ConvertToPercentageString(SiteDiversity) ||
               cb_SiteSpareCapacity.Text != ConvertToPercentageString(SiteSpareCapacity) || 
               tb_Engineer.Text != Engineer ||
               tb_Verifier.Text != Verifier ||
               tb_Revision.Text != Revision ||
               tb_ReferenceDrawing.Text != ReferenceDrawing;
        }

        private string ConvertToPercentageString(double value)
        {
            return (value * 100) + "%";
        }
    }
}
