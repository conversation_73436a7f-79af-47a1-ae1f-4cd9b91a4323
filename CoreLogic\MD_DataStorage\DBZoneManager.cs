using Autodesk.Revit.DB.ExtensibleStorage;
using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows;
using MessageBox = System.Windows.MessageBox;
using MEP.MaxDemand.Models;
using Egor92.MvvmNavigation;

namespace MEP.MaxDemand.CoreLogic.MD_DataStorage
{
    public static class DBZoneManager
    {
        public const string ZoneSchemaName = "MD_Zone_Schema";
        public const string DataStorageName = "MD_Zone_DataStorage";

        public const string SpaceSchemaName = "MD_Space_Schema";
        public const string SpaceEntityFieldName = "EquipmentData";

        public static Schema GetSchemaByName(string name)
        {
            // schema
            Schema schema = null;
            // list of schema in memory
            IList<Schema> schemas = Schema.ListSchemas();

            if (schemas != null && schemas.Count > 0)
            {
                // iterate schema list
                foreach (Schema s in schemas)
                {
                    if (s.SchemaName == name)
                    {
                        schema = s;
                        break;
                    }
                }
            }
            return schema;
        }

        public static DataStorage GetDBZoneDataStorageByName(Document doc, string dataStorageName)
        {
            return new FilteredElementCollector(doc).OfClass(typeof(DataStorage)).ToList()
                .Find(dataStorage => dataStorage.Name.Equals(dataStorageName)) as DataStorage;
        }

        public static Entity GetDBZoneEntityByZoneName(Document doc, DataStorage dataStorage, string zoneName)
        {
            if (string.IsNullOrEmpty(zoneName))
            {
                return null;
            }

            if (dataStorage != null)
            {
                var dBZoneSchema = GetSchemaByName(ZoneSchemaName);

                // Retrieve the entity associated with the schema from the DataStorage
                Entity entity = dataStorage.GetEntity(dBZoneSchema);

                // Check if the entity is valid and matches the schema
                if (entity.IsValid() && entity.Schema.GUID == dBZoneSchema.GUID && entity.Get<string>(nameof(DBZoneEntity.Name)).Equals(zoneName))
                {
                    return entity;
                }
            }

            return null;
        }

        public static void UpdateDBZones(Document doc, List<DBZoneEntity> updatedDBZones, Schema dBZoneSchema)
        {
            using (var trans = new Transaction(doc, "Update DB Zones"))
            {
                trans.Start();

                try
                {
                    FilteredElementCollector dataStorages = new FilteredElementCollector(doc);
                    dataStorages.OfClass(typeof(DataStorage));

                    // Dictionary to map existing DBZone Names to their DataStorage ElementIds
                    Dictionary<string, ElementId> existingDBZones = new Dictionary<string, ElementId>();

                    // Collect all existing DataStorage elements with the DBZone schema
                    foreach (DataStorage dataStorage in dataStorages)
                    {
                        Entity entity = dataStorage.GetEntity(dBZoneSchema);
                        if (entity.IsValid() && entity.Schema.GUID == dBZoneSchema.GUID)
                        {
                            string existingName = entity.Get<string>(nameof(DBZoneEntity.Name));
                            existingDBZones[existingName] = dataStorage.Id;
                        }
                    }
                    
                    // Iterate through the updated list of DBZone objects
                    foreach (var dbZone in updatedDBZones)
                    {
                        if (existingDBZones.TryGetValue(dbZone.Name, out ElementId existingElementId))
                        {
                            // Entity exists, so update the existing entity
                            DataStorage dataStorage = doc.GetElement(existingElementId) as DataStorage;
                            if (dataStorage != null)
                            {
                                Entity entity = dataStorage.GetEntity(dBZoneSchema);

                                // Update the fields with the new values
                                entity.Set<double>(nameof(DBZoneEntity.ZoneArea), DBZoneManager.ToDouble(dbZone.ZoneArea));
                                entity.Set<double>(nameof(DBZoneEntity.HVACTopologyLoadFactor), DBZoneManager.ToDouble(dbZone.HVACTopologyLoadFactor)); // This is updated to store DBZoneHVAC
                                entity.Set<int>(nameof(DBZoneEntity.GeneratorSmallPower), dbZone.GeneratorSmallPower);
                                entity.Set<int>(nameof(DBZoneEntity.GeneratorLighting), dbZone.GeneratorLighting);
                                entity.Set<int>(nameof(DBZoneEntity.GeneratorMechanical), dbZone.GeneratorMechanical);
                                entity.Set<int>(nameof(DBZoneEntity.GeneratorLump), dbZone.GeneratorLump);
                                entity.Set<int>(nameof(DBZoneEntity.UPSSmallPower), dbZone.UPSSmallPower);
                                entity.Set<int>(nameof(DBZoneEntity.UPSLighting), dbZone.UPSLighting);
                                entity.Set<int>(nameof(DBZoneEntity.UPSMechanical), dbZone.UPSMechanical);
                                entity.Set<int>(nameof(DBZoneEntity.UPSLump), dbZone.UPSLump);
                                entity.Set<double>(nameof(DBZoneEntity.TotalLightingLoad), dbZone.TotalLightingLoad);
                                entity.Set<double>(nameof(DBZoneEntity.TotalMechLoad), dbZone.TotalMechLoad);
                                entity.Set<double>(nameof(DBZoneEntity.TotalPowerLoad), dbZone.TotalPowerLoad);
                                entity.Set<double>(nameof(DBZoneEntity.TotalLoad), dbZone.TotalLoad);

                                // Update lists of ElementId
                                entity.Set(nameof(DBZoneEntity.DBs), dbZone.DBs.ToArray());
                                entity.Set(nameof(DBZoneEntity.Spaces), dbZone.Spaces.ToArray());

                                // Store the updated entity back in the DataStorage
                                dataStorage.SetEntity(entity);
                            }
                        }
                        else
                        {
                            // Entity does not exist, so create a new one
                            Entity entity = new Entity(dBZoneSchema);

                            // Populate the entity fields with the values from the DBZone object
                            entity.Set<string>(nameof(DBZoneEntity.Name), dbZone.Name);
                            entity.Set<double>(nameof(DBZoneEntity.ZoneArea), DBZoneManager.ToDouble(dbZone.ZoneArea));
                            entity.Set<double>(nameof(DBZoneEntity.HVACTopologyLoadFactor), DBZoneManager.ToDouble(dbZone.HVACTopologyLoadFactor)); // This is updated to store DBZoneHVAC
                            entity.Set<int>(nameof(DBZoneEntity.GeneratorSmallPower), dbZone.GeneratorSmallPower);
                            entity.Set<int>(nameof(DBZoneEntity.GeneratorLighting), dbZone.GeneratorLighting);
                            entity.Set<int>(nameof(DBZoneEntity.GeneratorMechanical), dbZone.GeneratorMechanical);
                            entity.Set<int>(nameof(DBZoneEntity.GeneratorLump), dbZone.GeneratorLump);
                            entity.Set<int>(nameof(DBZoneEntity.UPSSmallPower), dbZone.UPSSmallPower);
                            entity.Set<int>(nameof(DBZoneEntity.UPSLighting), dbZone.UPSLighting);
                            entity.Set<int>(nameof(DBZoneEntity.UPSMechanical), dbZone.UPSMechanical);
                            entity.Set<int>(nameof(DBZoneEntity.UPSLump), dbZone.UPSLump);
                            entity.Set<double>(nameof(DBZoneEntity.TotalLightingLoad), dbZone.TotalLightingLoad);
                            entity.Set<double>(nameof(DBZoneEntity.TotalMechLoad), dbZone.TotalMechLoad);
                            entity.Set<double>(nameof(DBZoneEntity.TotalPowerLoad), dbZone.TotalPowerLoad);
                            entity.Set<double>(nameof(DBZoneEntity.TotalLoad), dbZone.TotalLoad);

                            // For lists of ElementId, use the array method
                            entity.Set(nameof(DBZoneEntity.DBs), dbZone.DBs.ToArray());
                            entity.Set(nameof(DBZoneEntity.Spaces), dbZone.Spaces.ToArray());

                            // Create a new DataStorage element to store the entity
                            DataStorage dataStorage = DataStorage.Create(doc);

                            // Assign the entity to the DataStorage element
                            dataStorage.SetEntity(entity);
                        }
                    }

                    // Optional: Remove entities that are not in the updated list
                    foreach (var existingName in existingDBZones.Keys)
                    {
                        if (!updatedDBZones.Any(d => d.Name == existingName))
                        {
                            ElementId dataStorageId = existingDBZones[existingName];
                            doc.Delete(dataStorageId); // Delete the DataStorage element
                        }
                    }
                }
                catch (Exception e)
                {
                    MessageBox.Show(e.ToString(), "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    trans.Dispose();
                }

                trans.Commit();
            }
        }

        private static bool IsNameUnique(Document doc, string name, Schema dBZoneSchema)
        {
            // Define a filter to find DataStorage elements
            FilteredElementCollector collector = new FilteredElementCollector(doc);
            collector.OfClass(typeof(DataStorage));

            // Iterate through all DataStorage elements in the document
            foreach (DataStorage dataStorage in collector)
            {
                // Try to get the Entity from the DataStorage element
                Entity entity = dataStorage.GetEntity(dBZoneSchema);

                // Check if the entity is valid and belongs to the desired schema
                if (entity.IsValid() && entity.Schema.GUID == dBZoneSchema.GUID)
                {
                    string entityName = entity.Get<string>(nameof(DBZoneEntity.Name));
                    if (entityName == name)
                    {
                        return false; // Name is not unique
                    }
                }
            }

            return true; // Name is unique
        }

        public static double ToDouble(object zoneAreaValue)
        {
            try
            {
                return Convert.ToDouble(zoneAreaValue);
            }
            catch (Exception ex)
            {
                return 0.0;
            }
        }

        public static DataStorage CreateZoneDataStorage(Document doc, string zoneNameInput, MD_ZoneModel mD_DBZone)
        {
            try
            {
                DataStorage dataStorage = null;
                using (Transaction trans = new Transaction(doc, "Create DataStorage"))
                {
                    trans.Start();
                    dataStorage = DataStorage.Create(doc);
                    dataStorage.Name = zoneNameInput;

                    Entity entity = new Entity(DBZoneManager.GetSchemaByName(DBZoneManager.ZoneSchemaName));

                    //double areaInInternalUnits = UnitUtils.Convert(mD_DBZone.ZoneArea, UnitTypeId.SquareMeters, UnitTypeId.SquareMeters);

                    entity.Set<string>(FieldNames.Name.ToString(), zoneNameInput);
                    entity.Set<double>(FieldNames.HVACTopologyLoadFactor.ToString(), 1.0, UnitTypeId.General);
                    entity.Set<int>(FieldNames.HVACMethodology.ToString(), 0);
                    entity.Set<int>(FieldNames.HVACTopology.ToString(), 0);
                    entity.Set<int>(FieldNames.GeneratorLighting.ToString(), 0);
                    entity.Set<int>(FieldNames.GeneratorLump.ToString(), 0);
                    entity.Set<int>(FieldNames.GeneratorMechanical.ToString(), 0);
                    entity.Set<int>(FieldNames.GeneratorSmallPower.ToString(), 0);
                    entity.Set<int>(FieldNames.UPSLump.ToString(), 0);
                    entity.Set<int>(FieldNames.UPSMechanical.ToString(), 0);
                    entity.Set<int>(FieldNames.UPSLighting.ToString(), 0);
                    entity.Set<int>(FieldNames.UPSSmallPower.ToString(), 0);

                    dataStorage.SetEntity(entity);

                    mD_DBZone.DBZoneDataStorage = dataStorage;

                    trans.Commit();
                }

                return dataStorage;
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
        }
    }

}
