﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;

namespace MEP.MaxDemand.Converters
{
    public class StringToTextDecorationsConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // Check if the text matches the condition exactly
            string inputText = value as string;
            if (!string.IsNullOrEmpty(inputText) && inputText == "HVAC Topology - Using Modified PD Values (%) (Recommended at the concept or preliminary design stage only)")
            {
                return TextDecorations.Strikethrough; // Apply strikethrough
            }

            return null; // Default (no decorations)
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
