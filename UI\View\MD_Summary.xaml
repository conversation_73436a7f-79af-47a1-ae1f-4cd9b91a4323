﻿<Page
    x:Class="MEP.MaxDemand.UI.View.MD_Summary"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.MaxDemand.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.MaxDemand.UI.View"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="MD_Summary"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="White"
    mc:Ignorable="d">

    <!--  Resources  -->
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converters:PercentageConverter x:Key="PercentageConverter" />
            <converters:RoundingConverter x:Key="RoundingConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="25,0,25,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width=".4*" />
        </Grid.ColumnDefinitions>

        <!--  Header  -->
        <TextBlock
            Margin="15,15,0,10"
            HorizontalAlignment="Center"
            Style="{StaticResource MaterialDesignHeadline4TextBlock}"
            Text="DB SUMMARY" />

        <TextBlock
            Grid.Column="1"
            Margin="15,15,0,10"
            HorizontalAlignment="Center"
            Style="{StaticResource MaterialDesignHeadline4TextBlock}"
            Text="SITE SUMMARY" />

        <Separator
            Grid.ColumnSpan="2"
            Margin="0,55,0,0"
            Background="#FFCE00">
            <Separator.LayoutTransform>
                <ScaleTransform ScaleY="3.5" />
            </Separator.LayoutTransform>
        </Separator>

        <!--  Data Grid  -->
        <ScrollViewer Grid.Row="1" Margin="25,0,25,0">
            <materialDesign:Card Grid.Row="1" Margin="20">
                <StackPanel>
                    <!--  Main Supply Summary  -->
                    <TextBlock
                        Grid.Row="0"
                        Margin="0,10,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                        Text="Main Supply" />
                    <DataGrid
                        Margin="15"
                        AlternatingRowBackground="WhiteSmoke"
                        AutoGenerateColumns="False"
                        BorderBrush="LightGray"
                        BorderThickness="1"
                        CanUserAddRows="False"
                        GridLinesVisibility="All"
                        ItemsSource="{Binding Data.Summary_MainSupplyDBs}"
                        ScrollViewer.HorizontalScrollBarVisibility="Auto"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        SelectionMode="Extended"
                        SelectionUnit="FullRow">
                        <DataGrid.Columns>

                            <!--  Distribution Board  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Distribution Board" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding DBName}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Diversified Per Phase Current (A)  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Diversified Per Phase Current (A)" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding DiversifiedPerPhaseCurrent_A, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Total Diversified Load (kVA)  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Total Diversified Load (kVA)" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding TotaldiversifiedLoad_kVA, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Average Power Factor  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Average Power Factor" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding AveragePowerFactor, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Total Diversified Load (kW)  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Total Diversified Load (kW)" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding TotalDiversifiedLoad_kW, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                        </DataGrid.Columns>

                    </DataGrid>

                    <!--  Essential Supply Summary  -->
                    <TextBlock
                        Grid.Row="2"
                        Margin="0,10,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                        Text="Essential Supply" />
                    <DataGrid
                        Margin="15"
                        AlternatingRowBackground="WhiteSmoke"
                        AutoGenerateColumns="False"
                        BorderBrush="LightGray"
                        BorderThickness="1"
                        CanUserAddRows="False"
                        GridLinesVisibility="All"
                        ItemsSource="{Binding Data.Summary_EssentialDBs}"
                        ScrollViewer.HorizontalScrollBarVisibility="Auto"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        SelectionMode="Extended"
                        SelectionUnit="FullRow">
                        <DataGrid.Columns>

                            <!--  Distribution Board  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Distribution Board" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding DBName}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Diversified Per Phase Current (A)  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Diversified Per Phase Current (A)" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding DiversifiedPerPhaseCurrent_A, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Total Diversified Load (kVA)  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Total Diversified Load (kVA)" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding TotaldiversifiedLoad_kVA, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Average Power Factor  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Average Power Factor" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding AveragePowerFactor, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Total Diversified Load (kW)  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Total Diversified Load (kW)" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding TotalDiversifiedLoad_kW, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                        </DataGrid.Columns>

                    </DataGrid>

                    <!--  Uninteruptable Summary  -->
                    <TextBlock
                        Grid.Row="4"
                        Margin="0,10,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                        Text="Uninteruptable Supply" />
                    <DataGrid
                        Margin="15"
                        AlternatingRowBackground="WhiteSmoke"
                        AutoGenerateColumns="False"
                        BorderBrush="LightGray"
                        BorderThickness="1"
                        CanUserAddRows="False"
                        GridLinesVisibility="All"
                        ItemsSource="{Binding Data.Summary_UninteruptableDBs}"
                        ScrollViewer.HorizontalScrollBarVisibility="Auto"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        SelectionMode="Extended"
                        SelectionUnit="FullRow">
                        <DataGrid.Columns>
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Distribution Board" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding DBName}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Diversified Per Phase Current (A)  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Diversified Per Phase Current (A)" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding DiversifiedPerPhaseCurrent_A, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Total Diversified Load (kVA)  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Total Diversified Load (kVA)" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding TotaldiversifiedLoad_kVA, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Average Power Factor  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Average Power Factor" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding AveragePowerFactor, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Total Diversified Load (kW)  -->
                            <DataGridTemplateColumn Width="*" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Total Diversified Load (kW)" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding TotalDiversifiedLoad_kW, Converter={StaticResource RoundingConverter}, ConverterParameter=2}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Normal" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsTotal}" Value="True">
                                                            <Setter Property="FontWeight" Value="Bold" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </materialDesign:Card>
        </ScrollViewer>

        <!--  Site Summary  -->
        <Grid
            Grid.Row="1"
            Grid.Column="1"
            Background="White">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--<Border
                Grid.Row="0"
                Padding="10"
                Background="#12A8B2">
                <TextBlock
                    HorizontalAlignment="Center"
                    FontSize="16"
                    FontWeight="Bold"
                    Foreground="White"
                    Text="SUMMARY OF RESULTS" />
            </Border>-->

            <!--  Site Inputs  -->
            <materialDesign:Card Margin="0,20,0,5">
                <Grid Grid.Row="1" Background="LightBlue">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Border Grid.ColumnSpan="2" Background="#12A8B2">
                        <TextBlock
                            Grid.ColumnSpan="2"
                            HorizontalAlignment="Center"
                            FontWeight="Bold"
                            Foreground="White"
                            Text="Site Inputs" />
                    </Border>
                    <TextBlock
                        Grid.Row="1"
                        Margin="10,0,0,0"
                        Text="Site Diversity:" />
                    <TextBlock Grid.Row="1" Grid.Column="1">
                        <TextBlock.Text>
                            <Binding
                                Converter="{StaticResource PercentageConverter}"
                                Path="Data.SiteDiversity"
                                UpdateSourceTrigger="PropertyChanged"
                                ValidatesOnDataErrors="True" />
                        </TextBlock.Text>
                    </TextBlock>
                    <TextBlock
                        Grid.Row="2"
                        Margin="10,0,0,0"
                        Text="Site Spare Capacity:" />
                    <TextBlock Grid.Row="2" Grid.Column="1">
                        <TextBlock.Text>
                            <Binding
                                Converter="{StaticResource PercentageConverter}"
                                Path="Data.SiteSpareCapacity"
                                UpdateSourceTrigger="PropertyChanged"
                                ValidatesOnDataErrors="True" />
                        </TextBlock.Text>
                    </TextBlock>
                </Grid>
            </materialDesign:Card>


            <!--  Summary Values  -->
            <materialDesign:Card Grid.Row="1" Margin="0,5,0,5">
                <Grid Grid.Row="2" Background="LightYellow">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Border Grid.ColumnSpan="2" Background="#12A8B2">
                        <TextBlock
                            Grid.ColumnSpan="2"
                            HorizontalAlignment="Center"
                            FontWeight="Bold"
                            Foreground="White"
                            Text="Summary Values" />
                    </Border>
                    <TextBlock
                        Grid.Row="1"
                        Margin="10,0,0,0"
                        Text="Transformer Size (kVA):" />
                    <TextBlock
                        Grid.Row="1"
                        Grid.Column="1"
                        Text="{Binding Data.TransformerSizekVA}" />
                    <TextBlock
                        Grid.Row="2"
                        Margin="10,0,0,0"
                        Text="Generator Size (kVA):" />
                    <TextBlock
                        Grid.Row="2"
                        Grid.Column="1"
                        Text="{Binding Data.GeneratorSizekVA}" />
                    <TextBlock
                        Grid.Row="3"
                        Margin="10,0,0,0"
                        Text="UPS Size (kVA):" />
                    <TextBlock
                        Grid.Row="3"
                        Grid.Column="1"
                        Text="{Binding Data.UPSSizekVA}" />
                    <TextBlock
                        Grid.Row="4"
                        Margin="10,0,0,0"
                        Text="Average Power Factor:" />
                    <TextBlock
                        Grid.Row="4"
                        Grid.Column="1"
                        Text="{Binding Data.AveragePowerFactor, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                    <TextBlock
                        Grid.Row="5"
                        Margin="10,0,0,0"
                        Text="Total Area (m²):" />
                    <TextBlock
                        Grid.Row="5"
                        Grid.Column="1"
                        Text="{Binding Data.TotalArea, Converter={StaticResource RoundingConverter}, ConverterParameter=0}" />
                    <TextBlock
                        Grid.Row="6"
                        Margin="10,0,0,0"
                        Text="Average Power Density (W/m²):" />
                    <TextBlock
                        Grid.Row="6"
                        Grid.Column="1"
                        Text="{Binding Data.AveragePowerDensityWm2, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                </Grid>
            </materialDesign:Card>


            <!--  Overall Supply  -->
            <materialDesign:Card Grid.Row="2" Margin="0,5,0,5">
                <Grid Grid.Row="3" Background="WhiteSmoke">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Border Grid.ColumnSpan="2" Background="#12A8B2">
                        <TextBlock
                            Grid.ColumnSpan="2"
                            HorizontalAlignment="Center"
                            FontWeight="Bold"
                            Foreground="White"
                            Text="Overall Supply" />
                    </Border>
                    <TextBlock
                        Grid.Row="1"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kVA):" />
                    <TextBlock
                        Grid.Row="1"
                        Grid.Column="1"
                        Text="{Binding Data.OverallSupplyTotalSiteDiversifiedLoadkVA, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="2"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kW):" />
                    <TextBlock
                        Grid.Row="2"
                        Grid.Column="1"
                        Text="{Binding Data.OverallSupplyTotalSiteDiversifiedLoadkW, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="3"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kVAR):" />
                    <TextBlock
                        Grid.Row="3"
                        Grid.Column="1"
                        Text="{Binding Data.OverallSupplyTotalSiteDiversifiedLoadkVAR, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="4"
                        Margin="10,0,0,0"
                        Text="Total Diversified Per Phase Load (A):" />
                    <TextBlock
                        Grid.Row="4"
                        Grid.Column="1"
                        Text="{Binding Data.OverallSupplyTotalDiversifiedPerPhaseLoadA, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                </Grid>
            </materialDesign:Card>

            <!--  Mains Supply  -->
            <materialDesign:Card Grid.Row="3" Margin="0,5,0,5">
                <Grid Grid.Row="4" Background="WhiteSmoke">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Border Grid.ColumnSpan="2" Background="#12A8B2">
                        <TextBlock
                            Grid.ColumnSpan="2"
                            HorizontalAlignment="Center"
                            FontWeight="Bold"
                            Foreground="White"
                            Text="Mains Supply" />
                    </Border>
                    <TextBlock
                        Grid.Row="1"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kVA):" />
                    <TextBlock
                        Grid.Row="1"
                        Grid.Column="1"
                        Text="{Binding Data.MainsSupplyTotalSiteDiversifiedLoadkVA, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="2"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kW):" />
                    <TextBlock
                        Grid.Row="2"
                        Grid.Column="1"
                        Text="{Binding Data.MainsSupplyTotalSiteDiversifiedLoadkW, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="3"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kVAR):" />
                    <TextBlock
                        Grid.Row="3"
                        Grid.Column="1"
                        Text="{Binding Data.MainsSupplyTotalSiteDiversifiedLoadkVAR, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="4"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (A):" />
                    <TextBlock
                        Grid.Row="4"
                        Grid.Column="1"
                        Text="{Binding Data.MainsSupplyTotalDiversifiedPerPhaseLoadA, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                </Grid>
            </materialDesign:Card>


            <!--  Generator Supply  -->
            <materialDesign:Card Grid.Row="4" Margin="0,5,0,5">
                <Grid Grid.Row="5" Background="WhiteSmoke">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Border Grid.ColumnSpan="2" Background="#12A8B2">
                        <TextBlock
                            Grid.ColumnSpan="2"
                            HorizontalAlignment="Center"
                            FontWeight="Bold"
                            Foreground="White"
                            Text="Generator Supply" />
                    </Border>
                    <TextBlock
                        Grid.Row="1"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kVA):" />
                    <TextBlock
                        Grid.Row="1"
                        Grid.Column="1"
                        Text="{Binding Data.GeneratorSupplyTotalSiteDiversifiedLoadkVA, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="2"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kW):" />
                    <TextBlock
                        Grid.Row="2"
                        Grid.Column="1"
                        Text="{Binding Data.GeneratorSupplyTotalSiteDiversifiedLoadkW, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="3"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kVAR):" />
                    <TextBlock
                        Grid.Row="3"
                        Grid.Column="1"
                        Text="{Binding Data.GeneratorSupplyTotalSiteDiversifiedLoadkVAR, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="4"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (A):" />
                    <TextBlock
                        Grid.Row="4"
                        Grid.Column="1"
                        Text="{Binding Data.GeneratorSupplyTotalDiversifiedPerPhaseLoadA, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                </Grid>
            </materialDesign:Card>

            <!--  UPS Supply  -->
            <materialDesign:Card Grid.Row="5" Margin="0,5,0,5">
                <Grid Grid.Row="6" Background="WhiteSmoke">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Border Grid.ColumnSpan="2" Background="#12A8B2">
                        <TextBlock
                            HorizontalAlignment="Center"
                            FontWeight="Bold"
                            Foreground="White"
                            Text="UPS Supply" />
                    </Border>
                    <TextBlock
                        Grid.Row="1"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kVA):" />
                    <TextBlock
                        Grid.Row="1"
                        Grid.Column="1"
                        Text="{Binding Data.UPSSupplyTotalSiteDiversifiedLoadkVA, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="2"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kW):" />
                    <TextBlock
                        Grid.Row="2"
                        Grid.Column="1"
                        Text="{Binding Data.UPSSupplyTotalSiteDiversifiedLoadkW, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="3"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (kVAR):" />
                    <TextBlock
                        Grid.Row="3"
                        Grid.Column="1"
                        Text="{Binding Data.UPSSupplyTotalSiteDiversifiedLoadkVAR, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                    <TextBlock
                        Grid.Row="4"
                        Margin="10,0,0,0"
                        Text="Total Site Diversified Load (A):" />
                    <TextBlock
                        Grid.Row="4"
                        Grid.Column="1"
                        Text="{Binding Data.UPSSupplyTotalDiversifiedPerPhaseLoadA, Converter={StaticResource RoundingConverter}, ConverterParameter=1}" />
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!--  Under Button  -->
        <Button
            Grid.Row="2"
            Grid.ColumnSpan="2"
            Width="100"
            Margin="0,20,0,15"
            HorizontalAlignment="Center"
            Background="#12A8B2"
            BorderBrush="#12A8B2"
            Click="BackToZones_Click"
            Content="Back"
            Foreground="White" />

        <!--  Footer  -->
        <Grid Grid.Row="3" Grid.ColumnSpan="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Image
                Height="25"
                Margin="20,5,0,10"
                HorizontalAlignment="Left"
                Source="/MEP.MaxDemand;component/Resources/BecaLogoBlack.png" />
            <TextBlock
                Grid.Column="1"
                Margin="0,10,20,10"
                HorizontalAlignment="Right"
                Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                Text="Make Everyday Better" />
        </Grid>
    </Grid>


</Page>
