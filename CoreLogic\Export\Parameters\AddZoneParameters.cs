﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.MaxDemand.CoreLogic.Export.Parameters
{
    public class AddZoneParameters : MacroParameters
    {
        public int TargetRow { get; set; }
        public string ZoneName { get; set; } = "";
        public double EssentialPower { get; set; } = 0;
        public double EssentialLighting { get; set; } = 0;
        public double EssentialMech { get; set; } = 0;
        public double EssentialLump { get; set; } = 0;
        public double UninterruptablePower { get; set; } = 0;
        public double UninterruptableLighting { get; set; } = 0;
        public double UninterruptableMech { get; set; } = 0;
        public double UninterruptableLump { get; set; } = 0;

        public override object[] Parameters => new object[] { TargetRow, ZoneName, EssentialPower, EssentialLighting, EssentialMech, EssentialLump, UninterruptablePower, UninterruptableLighting, UninterruptableMech, UninterruptableLump };
    }
}
