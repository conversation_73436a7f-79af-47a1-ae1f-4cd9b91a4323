﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.MaxDemand.CoreLogic.Export.Parameters
{
    public class AddDBParameters : MacroParameters
    {
        public int TargetRow { get; set; }
        public string DbName { get; set; } = "";
        public string DbClass { get; set; } = "G";
        public bool DbPower { get; set; } = false;
        public bool DbLighting { get; set; } = false;
        public bool DbMech { get; set; } = false;
        public bool DbLump { get; set; } = false;
        public double DbCableLength { get; set; } = 1;
        public string DbParent { get; set; } = "";

        public override object[] Parameters => new object[] { TargetRow, DbName, DbClass, DbPower, DbLighting, DbMech, DbLump, DbCableLength, DbParent };
    }
}
