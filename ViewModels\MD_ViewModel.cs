using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.MaxDemand.CoreLogic;
using MEP.MaxDemand.CoreLogic.Calculation;
using MEP.MaxDemand.Models;
using MEP.MaxDemand.UI.View.ViewHandlers;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using Application = System.Windows.Application;
using MessageBox = System.Windows.MessageBox;
using System.Windows.Controls;
using Frame = System.Windows.Controls.Frame;
using Autodesk.Revit.UI.Selection;
using BecaRevitUtilities.SelectionFilters;
using Autodesk.Revit.DB.Mechanical;
using BecaRevitUtilities.ElementUtilities;
using BecaRevitUtilities;
using MEP.MaxDemand.CoreLogic.MD_DataStorage;
using MEP.MaxDemand.CoreLogic.Export;
using System.IO;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using CommunityToolkit.Mvvm.Messaging;
using MEP.MaxDemand.UI;
using System.Security.Policy;
using System.Diagnostics;

namespace MEP.MaxDemand.ViewModels
{
    public partial class MD_ViewModel : ObservableObject
    {
        RequestHandler _handler;
        ExternalEvent _externalEvent;
        BecaActivityLoggerData _logger;
        UIApplication _uiapp;

        // Flag to prevent multiple recalc for a single input change 
        private bool _isUpdating = false;

        // Debounce mechanism to avoid excessive method calls when property changes occurs rapidly
        private readonly System.Timers.Timer _debounceTimer = new System.Timers.Timer(300); // 300ms debounce

        // Main Data
        [ObservableProperty]
        private MD_DataModel data;

        // Properties for assigning Spatial Function
        [ObservableProperty]
        private List<PDLoadRow> pDLookupData;
        [ObservableProperty]
        private PDLoadRow selectedPDLoadRow;
        [ObservableProperty]
        private IList<MD_SpaceModel> selectedSpaces = new List<MD_SpaceModel>();
        [ObservableProperty]
        private MD_SpaceModel selectedParentSpace;
        [ObservableProperty]
        private bool hasUnsavedChanges = false;

        [ObservableProperty]
        private MD_ZoneModel selectedZone;
        [ObservableProperty]
        private string selectedHVACTopology;
        [ObservableProperty]
        private MD_DBModel selectedOrphanedDB;
        [ObservableProperty]
        private string zoneNameInput;
        [ObservableProperty]
        private MD_DBModel selectedDB;
        [ObservableProperty]
        private string popupMessage;
        [ObservableProperty]
        private bool isPopupVisible;

        public List<string> HVACTopologyOptions { get; } = new MD_ZoneModel().HVACTopologyOptions;
        public ObservableCollection<MD_ZoneModel> ObservableZones => Data?.Zones;
        public ObservableCollection<MD_DBModel> ObservableOrphanedDBs => Data?.OrphanedDBs;
        public ObservableCollection<MD_SpaceModel> ObservableOrphanedSpaces => Data?.OrphanedSpaces;

        #region Testing HVAC MAPPING
        // Card 1
        [ObservableProperty] private string selectedItem1; // selectedDBZoneHVACMethodology
        [ObservableProperty] private string card1TitleText;
        [ObservableProperty] private string card1ContentText;

        // Card 2
        [ObservableProperty] private string selectedItem2; // selectedDBZoneHVACTopology
        [ObservableProperty] private string card2CentralisedLoadText; 
        [ObservableProperty] private string card2KeyConsiderationText;
        [ObservableProperty] private string card2SideNoteText;

        // Card 3
        [ObservableProperty] private string card3ProrartionFactorText;
        [ObservableProperty] private string card3ProrartionFactorDescriptionText;

        [ObservableProperty] private bool isGrid3AVisible;
        [ObservableProperty] private bool isGrid3BVisible;
        [ObservableProperty] private bool isCard3Visible;

        [ObservableProperty] private bool isFanVisible;
        [ObservableProperty] private bool isElecHtgVisible;
        [ObservableProperty] private bool isOtherNotableVisible;

        [ObservableProperty] private double dBZoneHVACNumericalInput;
        [ObservableProperty] private int selectedDBZoneHVACMethodology;
        [ObservableProperty] private int selectedDBZoneHVACTopology;

        // Mapping data
        public Dictionary<string, string> Card1TitleMap { get; set; }
        public Dictionary<int, string> Card1HVACMethodologyMap { get; set; }
        public Dictionary<int, string> Card2HVACToplogyMap { get; set; }
        public Dictionary<int, string> Card2KeyConsiderationdMap { get; set; }
        public Dictionary<int, bool> Card2IsFanVisibleMap { get; set; }
        public Dictionary<int, bool> Card2IsElecHtgVisibleMap { get; set; }
        public Dictionary<int, bool> Card2IsOtherNotableVisibleMap { get; set; }
        public Dictionary<int, string> Card2SideNoteMap { get; set; }
        public Dictionary<int, string> Card3ProrationFactorMap { get; set; }
        public Dictionary<int, string> Card3ProrationFactorDescriptionMap { get; set; }

        // Helper properties for UI binding - these provide the display strings for ComboBoxes
        public Dictionary<int, string> HVACMethodologyDisplayNames { get; set; }
        public Dictionary<int, string> HVACTopologyDisplayNames { get; set; }
        #endregion

        #region Constructor
        public MD_ViewModel(ExternalEvent exEvent, RequestHandler handler, BecaActivityLoggerData logger, UIApplication uiapp, MD_DataModel data)
        {
            //InitializeDebounceTimer();

            _handler = handler;
            _externalEvent = exEvent;
            _logger = logger;

            _uiapp = uiapp;

            Data = data;
            // Initialize with default values - will be updated when a zone is selected
            SelectedDBZoneHVACMethodology = 0;
            SelectedDBZoneHVACTopology = 0;

            PDLookupData = Data.PDLookupData;

            SubscribeToPropertyChanges(Data);

            // Trigger Initial Calculation
            RecalculateAll();

            // Register for messages
            WeakReferenceMessenger.Default.Register<ShowPopupMessage>(this, async (r, msg) =>
            {
                await ShowSuccessPopup(msg.Message);
            });

            // HVAC Mapping
            InitializeHVACMapping();
        }
        #endregion

        #region Relay Commands
        [RelayCommand]
        private void RecalculateAll()
        {
            if (_isUpdating) return; // Prevent cascading effect

            _isUpdating = true;

            try
            {
                if (SelectedZone != null)
                {
                    foreach (var s in SelectedZone.Spaces)
                    {
                        MD_CoreCalculation.UpdateSpaceProperties(Data, SelectedZone, s);
                    }

                    MD_CoreCalculation.UpdateSpaceZoneProperties(SelectedZone);
                    MD_CoreCalculation.UpdateDBAndDBZoneProperties(Data, SelectedZone);

                    if (Data != null)
                    {
                        MD_CoreCalculation.UpdateDataSummaryProperties(Data);
                        OnPropertyChanged(nameof(Data));
                    }

                    // Mark as having unsaved changes for any property change
                    HasUnsavedChanges = true;
                }
                else if (Data != null)
                {
                    MD_CoreCalculation.RecalculateData(Data);
                    OnPropertyChanged(nameof(Data));

                    // Mark as having unsaved changes for any property change
                    HasUnsavedChanges = true;
                }
            }
            finally
            {
                _isUpdating = false; // Reset flag
            }
        }

        /// <summary>
        /// Handles spatial function updates and triggers recalculations.
        /// </summary>
        [RelayCommand]
        private void RecalculateAndUpdateSpatialFunctions()
        {
            // This is when Spatial Function input changed
            if (SelectedPDLoadRow == null || SelectedSpaces == null || !SelectedSpaces.Any())
                return;

            foreach (var selectedSpace in SelectedSpaces)
            {
                var pDLoad = PDLookupData.Find(p => p.SpatialFunction == SelectedPDLoadRow.SpatialFunction);
                if (pDLoad == null)
                    continue;

                // Update Spatial Function
                selectedSpace.SpatialFunction = SelectedPDLoadRow.SpatialFunction;

                // Update Load Densities
                selectedSpace.LightingLoadDensity = pDLoad.LightingDensity;
                selectedSpace.SmallPowerLoadDensity = pDLoad.PowerDensity;
                selectedSpace.MechLoadDensity = pDLoad.MechanicalDensity;

                MD_CoreCalculation.UpdateSpaceProperties(Data, SelectedZone, selectedSpace);
            }

            if (SelectedZone != null)
            {
                MD_CoreCalculation.UpdateSpaceZoneProperties(SelectedZone);
                MD_CoreCalculation.UpdateDBAndDBZoneProperties(Data, SelectedZone);
            }
            MD_CoreCalculation.UpdateDataSummaryProperties(Data);

            // Notify DataGrid to refresh UI
            OnPropertyChanged(nameof(SelectedZone.Spaces));
        }

        [RelayCommand]
        private void AddZoneToData()
        {
            if (Data.Zones.Select(z => z.ZoneName).Any(zoneName => zoneName == ZoneNameInput))
            {
                MessageBox.Show("This DB Zone already exists.\nPlease create a different name.", 
                    "Warning", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (Data != null && !string.IsNullOrWhiteSpace(ZoneNameInput))
            {
                Data.Zones.Add(new MD_ZoneModel
                {
                    ZoneName = ZoneNameInput,
                    DBZoneHVAC = 0,
                    CurrentOwners = Data.ProjInfo.Document.Application.Username,
                    HVACMethodology = (int)HVACMethodology.GenericBecaPD, // Default to Generic Beca PD
                    HVACTopology = (int)HVACTopology.PleaseSelect // Default to Please Select
                });

                if (DBZoneManager.GetDBZoneDataStorageByName(_uiapp.ActiveUIDocument.Document, ZoneNameInput) == null)
                {
                    MakeRequest(RequestId.CreateDataStorage);
                }
            }

            // Sort Zones
            Data.Zones = new ObservableCollection<MD_ZoneModel>(Data.Zones.OrderBy(z => z.ZoneName));
        }

        [RelayCommand]
        private void SaveDataPage()
        {
            MakeRequest(RequestId.SaveDataInMainForm);
        }

        [RelayCommand]
        private void SaveZoneEditPage()
        {
            MakeRequest(RequestId.SaveSpaceDBParametersZoneEntities);
        }

        [RelayCommand]
        private void Export()
        {
            MD_FileExport.ExportData(_uiapp, _logger, Data);
        }

        [RelayCommand]
        private void RenameParentNode(object parameter)
        {
            if (parameter is MD_ZoneModel zone)
            {
                string newName = Microsoft.VisualBasic.Interaction.InputBox(
                    "Enter new name for the zone:", "Rename Zone", zone.ZoneName);
                if (!string.IsNullOrWhiteSpace(newName))
                {
                    zone.ZoneName = newName;

                    SelectedZone = zone;

                    MakeRequest(RequestId.RenameDBZoneInDBsAndSpaces);
                }
            }
        }

        [RelayCommand]
        private void SaveProjectInfo()
        {
            MakeRequest(RequestId.SaveProjectInfo);
        }

        [RelayCommand]
        private void RemoveParentNode(object parameter)
        {
            if (parameter is MD_ZoneModel zone)
            {
                if (zone.IsLocked)
                {
                    MessageBox.Show("Locked zone cannot be edited.", "Zone Locked", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (MessageBox.Show($"Are you sure you want to delete zone '{zone.ZoneName}'?",
                    "Confirm Deletion", MessageBoxButton.YesNo, MessageBoxImage.Warning) == MessageBoxResult.Yes)
                {
                    SelectedZone = zone;

                    Data.Zones.Remove(zone);

                    // Clear MD_ZoneName parameter in the zone's DBs and Spaces
                    MakeRequest(RequestId.ClearDBZoneInDBsAndSpaces);
                }
            }
        }

        [RelayCommand]
        private void AddOrphanedDBToZone(MD_DBModel dbToRemove)
        {
            if (dbToRemove.IsLocked)
            {
                MessageBox.Show($"This DB cannot be added.\nLocked by: {dbToRemove.DB.ElementOwner(dbToRemove.DB.Document)}\n", "Unable to add DB", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (SelectedZone != null && dbToRemove != null)
            {
                var selectedOrphanedDBName = dbToRemove.DBName;

                // Add the OrphanedDB to the selected zone's DBs
                SelectedZone.DBs ??= new ObservableCollection<MD_DBModel>();
                SelectedZone.DBs.Add(dbToRemove);

                // Notify that the Dbs property has changed
                OnPropertyChanged(nameof(SelectedZone.DBs));

                // Remove it from the OrphanedDBs list
                Data.OrphanedDBs.Remove(dbToRemove);

            }
            else
            {
                MessageBox.Show("Please select a Zone and an Orphaned DB before adding.", "Selection Required", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        [RelayCommand]
        private void RemoveDBFromZone(MD_DBModel dbToRemove)
        {
            if (dbToRemove == null)
            {
                MessageBox.Show("Please select a DB to remove.", "Select DB", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Find the Zone that contains this DB
            var parentZone = Data.Zones.FirstOrDefault(zone => zone.DBs.Contains(dbToRemove));

            if (parentZone != null)
            {
                SelectedDB = dbToRemove;

                // Remove the DB from the Zone
                parentZone.DBs.Remove(dbToRemove);

                // Add the DB to the OrphanedDBs list
                Data.OrphanedDBs.Add(dbToRemove);

                //MessageBox.Show($"Successfully removed '{dbToRemove.DBName}' from Zone '{parentZone.ZoneName}' and added it to the Orphaned DB list!",
                //    "DB removed", MessageBoxButton.OK, MessageBoxImage.Information);

                // Update DB parameter
                MakeRequest(RequestId.ClearZonePrameterInDB);
            }
            else
            {
                MessageBox.Show("The selected DB is not associated with any zone.", "Waring", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        [RelayCommand]
        private void AddSpacesToZone()
        {
            if (ModelessMainWindowHandler.MinimizeWindow())
            {
                var spaceExistsCount = 0;
                var zoneSpaces = new List<ZoneSpace>();
                var sbLockedSpaces = new StringBuilder();
                var addedSpaces = new StringBuilder();
                var messages = new StringBuilder();

                try
                {
                    // Pick spaces from Revit.
                    var uidoc = _uiapp.ActiveUIDocument;
                    var references = uidoc.Selection.PickObjects(ObjectType.Element, new OnlySpacesSelectionFilter(), $"Select spaces to add to {SelectedZone.ZoneName}");
                    var doc = uidoc.Document;
                    foreach (var reference in references)
                    {
                        var flag = false;
                        var selectedSpace = doc.GetElement(reference.ElementId) as Space;

                        if (selectedSpace?.Area > 0)
                        {
                            // Check if it's locked
                            var spaceIsLocked = selectedSpace.IsLocked(doc);
                            if (spaceIsLocked)
                            {
                                sbLockedSpaces.AppendLine($"{selectedSpace.Number} : {selectedSpace.Name}. Locked by: {selectedSpace.ElementOwner(doc)}");
                                continue;
                            }

                            // Check if the space is already used
                            foreach (var zone in Data.Zones)
                            {
                                foreach (var space in zone.Spaces)
                                {
                                    if (space.Space.Name == selectedSpace.Name)
                                    {
                                        zoneSpaces.Add(new ZoneSpace() { SpaceName = space.Space.Name, ZoneName = zone.ZoneName });
                                        spaceExistsCount++;
                                        flag = true;
                                        continue;
                                    }
                                }
                            }
                            if (flag)
                                continue;

                            var mD_SpaceModel = Data.OrphanedSpaces.FirstOrDefault(s => s.Id == selectedSpace.Id);

                            if (mD_SpaceModel != null)
                            {
                                // Add space to db zone
                                SelectedZone.Spaces.Add(mD_SpaceModel);

                                // Remove space from orphaned spaces
                                Data.OrphanedSpaces.Remove(mD_SpaceModel);

                                addedSpaces.AppendLine($"Name: {mD_SpaceModel.SpaceName}. Number: {mD_SpaceModel.SpaceNumber}");
                            }
                        }
                    }

                    AddSpacesResultMessage(spaceExistsCount, zoneSpaces, sbLockedSpaces, addedSpaces, messages);

                    RecalculateAll();

                    // Bring the WPF window back to the front
                    ModelessMainWindowHandler.ShowForm(_uiapp, _logger, Data);
                    ModelessMainWindowHandler.FrmModelessMainWindow.WindowState = WindowState.Normal;

                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                // Bring the WPF window back to the front
                ModelessMainWindowHandler.ShowForm(_uiapp, _logger, Data);
            }
        }

        private void AddSpacesResultMessage(int spaceExistsCount, List<ZoneSpace> zoneSpaces, StringBuilder sbLockedSpaces, StringBuilder addedSpaces, StringBuilder messages)
        {
            if (sbLockedSpaces.Length > 0)
            {
                messages.AppendLine("These spaces are locked and will not be added to the selected zone:\n");
                messages.AppendLine(sbLockedSpaces.ToString());
                messages.AppendLine();
            }
            if (spaceExistsCount > 0)
            {
                var usedSpacesMessage = string.Join(Environment.NewLine,
                                        zoneSpaces.OrderBy(x => x.ZoneName)
                                                  .Select(x => $"DBZone: {x.ZoneName}. Space: {x.SpaceName}."));
                messages.AppendLine($"{spaceExistsCount} space(s) already being used and will not be added to the selected zone:\n");
                messages.AppendLine(usedSpacesMessage);
                messages.AppendLine();
            }
            if (addedSpaces.Length > 0)
            {
                messages.AppendLine($"Successfully added these spaces to {SelectedZone.ZoneName}:\n");
                messages.AppendLine(addedSpaces.ToString());
                messages.AppendLine();
            }

            if (messages.Length > 0)
            {
                MessageBox.Show(messages.ToString(), "Add Spaces Result", MessageBoxButton.OK,
                                MessageBoxImage.Information);
            }
        }

        [RelayCommand]
        private void AddUnsassignedSpaces()
        {
            var sb = new StringBuilder();
            foreach (var s in SelectedSpaces)
            {
                if (s.SpaceStatus == "Placed" || s.Area > 0 || !s.IsLocked)
                {
                    // Add space to db zone
                    SelectedZone.Spaces.Add(s);
                    //Remove space from orphaned spaces
                    Data.OrphanedSpaces.Remove(s);
                }
                else
                {
                    sb.AppendLine($"Number: {s.SpaceNumber}, " +
                        $"Name: {s.SpaceName}, " +
                        $"Status: {s.SpaceStatus}, " +
                        $"Owner: {s.Space.ElementOwner(s.Space.Document)}, " +
                        $"Area: {Math.Round(RevitUnitConvertor.InternalToSquareMeters(s.Area), 2) + " m²"}");
                }
            }

            MessageBox.Show($"{SelectedSpaces.Count} spaces added to {SelectedZone.ZoneName}", "Info", MessageBoxButton.OK, MessageBoxImage.Information);

            if (sb.Length > 0)
            {
                MessageBox.Show($"The following spaces will not be added to the list due to their status or area:\n\n{sb}", 
                    "Info", MessageBoxButton.OK, MessageBoxImage.Information);
            }

            RecalculateAll();
        }

        [RelayCommand]
        private void RemoveSpacesFromZone()
        {
            try
            {
                if (!SelectedSpaces.Any())
                { 
                    MessageBox.Show("Select spaces to remove.", "Warning", MessageBoxButton.OK, MessageBoxImage.Warning); 
                    return;
                }

                var dbZone = SelectedZone;
                foreach (var space in SelectedSpaces)
                {
                    dbZone.Spaces.Remove(space);
                    Data.OrphanedSpaces.Add(space);
                    // Clear parameter
                    space.Space.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(String.Empty);
                }

                RecalculateAll();

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void DeleteSpatialEquipment(object equipment)
        {
            if (equipment != null && equipment is MD_SpatialEquipment && SelectedParentSpace != null)
            {
                var eq = (MD_SpatialEquipment)equipment;
                if (eq != null)
                {
                    SelectedParentSpace.SpatialEquipments.Remove(eq);
                }
            }
        }

        [RelayCommand]
        private void AddSpatialEquipment()
        {
            if (SelectedParentSpace != null)
            {
                if (SelectedParentSpace.SpatialEquipments == null)
                {
                    SelectedParentSpace.SpatialEquipments = new ObservableCollection<MD_SpatialEquipment>();
                }

                SelectedParentSpace.SpatialEquipments.Add(new MD_SpatialEquipment 
                {
                    EquipmentName = "",
                    LumpLoadPowerFactor = 0.9,
                    LumpLoadDiversity = 0.5,
                    SinglePhaseLumpLoad = 0,
                    ThreePhaseLumpLoad = 0
                });

                // Subscribe to Spatial Equipments within the Space
                foreach (var equipment in SelectedParentSpace.SpatialEquipments)
                {
                    equipment.PropertyChanged += (s, e) =>
                    {
                        // Notify UI of Space-specific changes
                        OnPropertyChanged(e.PropertyName);
                        RecalculateAll();
                    };
                }

                SelectedParentSpace.HasEquipments = true;
            }
        }

        [RelayCommand]
        private void OpenDocumentation()
        {
            MessageUtility.OpenWebLink("https://becagroup.sharepoint.com/sites/BIMBrilliance/SitePages/Electrical-Maximum-Demand-Calculator.aspx");
        }
        #endregion

        #region Methods
        /// <summary>
        ///   A private helper method to make a request
        /// </summary>
        private void MakeRequest(RequestId request)
        {
            _handler.Request.Make(request);
            _externalEvent.Raise();
        }

        private void InitializeDebounceTimer()
        {
            _debounceTimer.Elapsed += (s, e) =>
            {
                if (Application.Current != null)
                {
                    Application.Current.Dispatcher.Invoke(RecalculateAll);
                }
            };
            _debounceTimer.AutoReset = false;
        }

        //private void UpdateHVACTopologyLoadFactor()
        //{
        //    if (!string.IsNullOrEmpty(SelectedHVACTopology) &&
        //        MD_Constants.HVACTopologyDictionary.TryGetValue(SelectedHVACTopology, out double loadFactor))
        //    {
        //        SelectedZone.HVACTopologyLoadFactor = loadFactor;
        //        RecalculateAll();
        //    }
        //}

        /// <summary>
        /// Subscribes to property changes at the Zone level.
        /// </summary>
        private void SubscribeToPropertyChanges(MD_DataModel data)
        {
            Data.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(MD_DataModel.Zones))
                {
                    OnPropertyChanged(nameof(ObservableZones));
                    RecalculateAll();
                }
                else if (e.PropertyName == nameof(MD_DataModel.OrphanedDBs))
                {
                    OnPropertyChanged(nameof(ObservableOrphanedDBs));
                    RecalculateAll();
                }
                else if (e.PropertyName == nameof(MD_DataModel.OrphanedSpaces))
                {
                    OnPropertyChanged(nameof(ObservableOrphanedSpaces));
                    RecalculateAll();
                }
                else
                {
                    OnPropertyChanged(e.PropertyName);
                    RecalculateAll();
                }
                //_debounceTimer.Start();
            };

            foreach (var zone in data.Zones)
            {
                SubscribeToZone(zone);
            }
        }

        /// <summary>
        /// Subscribes to a single Zone and its children.
        /// </summary>
        private void SubscribeToZone(MD_ZoneModel zone)
        {
            // Subscribe to the ZoneModel's PropertyChanged event
            zone.PropertyChanged += (s, e) =>
            {
                // Notify UI of zone-specific changes
                OnPropertyChanged(e.PropertyName);
                RecalculateAll();
            };

            // Subscribe to DBs within the zone
            foreach (var db in zone.DBs)
            {
                db.PropertyChanged += (s, e) =>
                {
                    // Notify UI of DB-specific changes
                    OnPropertyChanged(e.PropertyName);
                    RecalculateAll();
                };
            }

            // Subscribe to Spaces within the zone
            foreach (var space in zone.Spaces)
            {
                space.PropertyChanged += (s, e) =>
                {
                    // Notify UI of Space-specific changes
                    OnPropertyChanged(e.PropertyName);
                    RecalculateAll();
                };

                if (space.SpatialEquipments == null)
                    continue;

                // Subscribe to Spatial Equipments within the Space
                foreach (var equipment in space.SpatialEquipments)
                {
                    equipment.PropertyChanged += (s, e) =>
                    {
                        // Notify UI of Space-specific changes
                        OnPropertyChanged(e.PropertyName);
                        RecalculateAll();
                    };
                }
            }
        }

        /// <summary>
        /// Handles when the SelectedPDLoadRow changes.
        /// </summary>
        partial void OnSelectedPDLoadRowChanged(PDLoadRow value)
        {
            RecalculateAndUpdateSpatialFunctions();
        }

        /// <summary>
        /// Handles when the SelectedSpaces change.
        /// </summary>
        partial void OnSelectedSpacesChanged(IList<MD_SpaceModel> value)
        {
            RecalculateAndUpdateSpatialFunctions();
        }

        /// <summary>
        /// Handles when the SelectedZone changes - updates HVAC properties from zone
        /// </summary>
        partial void OnSelectedZoneChanged(MD_ZoneModel value)
        {
            if (value != null)
            {
                // Update ViewModel HVAC properties from the selected zone
                SelectedDBZoneHVACMethodology = value.HVACMethodology;
                SelectedDBZoneHVACTopology = value.HVACTopology;
                DBZoneHVACNumericalInput = value.DBZoneHVAC;

                // Update the UI display items
                if (HVACMethodologyDisplayNames?.ContainsKey(value.HVACMethodology) == true)
                    SelectedItem1 = HVACMethodologyDisplayNames[value.HVACMethodology];

                if (HVACTopologyDisplayNames?.ContainsKey(value.HVACTopology) == true)
                    SelectedItem2 = HVACTopologyDisplayNames[value.HVACTopology];
            }
        }

        //partial void OnSelectedHVACTopologyChanged(string value)
        //{
        //    UpdateHVACTopologyLoadFactor();
        //}

        private async Task ShowSuccessPopup(string message)
        {
            PopupMessage = message;
            IsPopupVisible = true;

            // Wait for 3 seconds
            await Task.Delay(900);

            // Fade-out effect (optional)
            IsPopupVisible = false;
        }

        #region HVAC Mapping 
        private void InitializeHVACMapping()
        {
            // Initialize display name mappings
            HVACMethodologyDisplayNames = new()
            {
                { (int)HVACMethodology.GenericBecaPD, "Generic - Using Beca PD Dataset(rec. Concept)" },
                { (int)HVACMethodology.HVACTopologyModifiedPD, "HVAC Topology - Using Modified PD Values (%) (Recommended at the concept or preliminary design stage only)" },
                { (int)HVACMethodology.UserSpecifiedVAPerM2, "User Specified (VA/m²) (Rec. prelim/developed)" },
                { (int)HVACMethodology.UserSpecifiedLumpLoads, "User Specified Lump Loads (Expected at Detailed Design or possibly earlier)" },
                { (int)HVACMethodology.NoHVAC, "None (No HVAC)" }
            };

            HVACTopologyDisplayNames = new()
            {
                { (int)HVACTopology.PleaseSelect, "Please select" },
                { (int)HVACTopology.FourPipeFanCoilsLTHW, "4-pipe fan coils (LTHW heating)" },
                { (int)HVACTopology.TwoPipeFanCoilsElectricHeating, "2-pipe fan coils with electric heating" },
                { (int)HVACTopology.LocalElecHeating, "Local Elec Heating" },
                { (int)HVACTopology.VRF_HVRF, "VRF / HVRF" },
                { (int)HVACTopology.SplitSystem, "Split system" },
                { (int)HVACTopology.VAVElectricHeating, "VAV with electric heating" },
                { (int)HVACTopology.VAVLTHWHeating, "VAV with LTHW heating" }
            };

            // HVAC Estimation Methodology
            Card1HVACMethodologyMap = new()
            {
                { (int)HVACMethodology.GenericBecaPD, "This method uses the VA/m² values assigned in the Beca Power Density dataset in their entirety. \r\n\r\nNote 1: This method will distribute all the mechanical load to the DB zones. There is no allowance for central plant. This is only suitable for estimating the Transformer/Generator size, NOT for sizing DBs/MCCs/\r\n\r\nNote 2: This method is known to be conservative and recommended to be replaced by numbers from the Mechanical engineer as soon as possible" },
                { (int)HVACMethodology.HVACTopologyModifiedPD, "" },
                { (int)HVACMethodology.UserSpecifiedVAPerM2, "This method requires you to enter a designed VA/m² for that DB Zones. This number should be provided by the mechanical engineer. \r\n\r\nNote 1: This method should NOT be used to calculate the central plant loads. You will need to add central plant" },
                { (int)HVACMethodology.UserSpecifiedLumpLoads, "This method disables the W/m² calculation method for mechanical loads. \r\n\r\nUsers are expected to enter both the local mech loads (such as Fans, electrical heating, and FCUs) and the central mech loads in their respective DB Zones. This should be entered as a lump load in the tool. \r\n\r\nNote 1: All mechanical loads need to be considered, along with their location. \r\n\r\nNote 2: Loads should be diversified appropriately. \r\n\r\nNote 3: Please include the mechanical engineer's MCC worksheet for verification purposes." },
                { (int)HVACMethodology.NoHVAC, "This method disables the W/m² calculation method for mechanical loads, and should only be used on projects without mechanical / HVAC loads. \r\n\r\nUsers may enter  sundry mech loads in their respective DB Zones as a lump load in the tool. " }
            };

            // HVAC Topology Dropdown
            Card2HVACToplogyMap = new()
            {
                { (int)HVACTopology.PleaseSelect, "" },
                { (int)HVACTopology.FourPipeFanCoilsLTHW, "Chiller / heat pump, chilled water and heating water pumps, air handling (DOAS) plant." },
                { (int)HVACTopology.TwoPipeFanCoilsElectricHeating, "Typically chiller and chilled water pumps, air handling (DOAS) plant (which may include electric heating for air tempering)." },
                { (int)HVACTopology.LocalElecHeating, "Tempering of outdoor air (ventilation) likely still centralised." },
                { (int)HVACTopology.VRF_HVRF, "Typically outdoor condenser/compressor units. Possibly air handling (DOAS) plant (which may include electric heating for air tempering)." },
                { (int)HVACTopology.SplitSystem, "Possibly central air handling plant (which may include electric heating for air tempering)." },
                { (int)HVACTopology.VAVElectricHeating, "Chiller, chilled water pumps, air handling plant (which may include electric heating for air tempering). Possibly heat pump and heating water pumps if air handling plant has LTHW heating." },
                { (int)HVACTopology.VAVLTHWHeating, "Chiller / heat pump, chilled water and heating water pumps, air handling plant." }
            };

            Card2KeyConsiderationdMap = new()
            {
                { (int)HVACTopology.PleaseSelect, "" },
                { (int)HVACTopology.TwoPipeFanCoilsElectricHeating, "Peak local loads (winter electric heating) do not occur at the same time as peak central plant loads (summer cooling by chillers). Heating may not be provided to internal zones." },
                { (int)HVACTopology.FourPipeFanCoilsLTHW, "Peak central plant loads for heating and cooling should be considered separately." },
                { (int)HVACTopology.LocalElecHeating, "Would not be that common." },
                { (int)HVACTopology.VRF_HVRF, "Outdoor units, where only a small number and/or not all in a central location, more likely to be served from a local MCC. Size for peak winter or summer load, whichever is higher." },
                { (int)HVACTopology.SplitSystem, "Size for peak winter or summer load, whichever is higher." },
                { (int)HVACTopology.VAVElectricHeating, "Peak local loads (winter electric heating) do not occur at the same time as peak central plant loads (summer cooling by chillers). Heating may not be provided to internal zones." },
                { (int)HVACTopology.VAVLTHWHeating, "Peak central plant loads for heating and cooling should be considered separately." }
            };

            Card2IsFanVisibleMap = new()
            {
                { (int)HVACTopology.PleaseSelect, false },
                { (int)HVACTopology.TwoPipeFanCoilsElectricHeating, true },
                { (int)HVACTopology.FourPipeFanCoilsLTHW, true },
                { (int)HVACTopology.LocalElecHeating, false },
                { (int)HVACTopology.VRF_HVRF, true },
                { (int)HVACTopology.SplitSystem, true },
                { (int)HVACTopology.VAVElectricHeating, false },
                { (int)HVACTopology.VAVLTHWHeating, false }
            };

            Card2IsElecHtgVisibleMap = new()
            {
                { (int)HVACTopology.PleaseSelect, false },
                { (int)HVACTopology.TwoPipeFanCoilsElectricHeating, true },
                { (int)HVACTopology.FourPipeFanCoilsLTHW, false },
                { (int)HVACTopology.LocalElecHeating, true },
                { (int)HVACTopology.VRF_HVRF, false },
                { (int)HVACTopology.SplitSystem, false },
                { (int)HVACTopology.VAVElectricHeating, true },
                { (int)HVACTopology.VAVLTHWHeating, false }
            };

            Card2IsOtherNotableVisibleMap = new()
            {
                { (int)HVACTopology.PleaseSelect, false },
                { (int)HVACTopology.TwoPipeFanCoilsElectricHeating, false },
                { (int)HVACTopology.FourPipeFanCoilsLTHW, false },
                { (int)HVACTopology.LocalElecHeating, false },
                { (int)HVACTopology.VRF_HVRF, true },
                { (int)HVACTopology.SplitSystem, true },
                { (int)HVACTopology.VAVElectricHeating, false },
                { (int)HVACTopology.VAVLTHWHeating, false }
            };

            Card2SideNoteMap = new()
            {
                { (int)HVACTopology.PleaseSelect, "" },
                { (int)HVACTopology.TwoPipeFanCoilsElectricHeating, "Fan coil fans and electric heaters (potentially a relatively high local load depending on climate). " },
                { (int)HVACTopology.FourPipeFanCoilsLTHW, "Fan coil fans only (relatively low load)" },
                { (int)HVACTopology.LocalElecHeating, "High local load from elec htg (depending on climate) - less common" },
                { (int)HVACTopology.VRF_HVRF, "Typically indoor unit (fan coil) fans and branch controllers (relatively low load)." },
                { (int)HVACTopology.SplitSystem, "Outdoor condenser/compressor unit. Some split systems require a power supply to the outdoor unit only, with the indoor unit being fed from the outdoor unit." },
                { (int)HVACTopology.VAVElectricHeating, "Electric heating coils." },
                { (int)HVACTopology.VAVLTHWHeating, "None (unless fan-assisted VAV box)." }
            };

            Card3ProrationFactorMap = new()
            {
                { (int)HVACTopology.TwoPipeFanCoilsElectricHeating, "Typically 0.5-1.0" },
                { (int)HVACTopology.FourPipeFanCoilsLTHW, "Typically Low (<0.1?)" },
                { (int)HVACTopology.LocalElecHeating, "Likely near 0.75-1" },
                { (int)HVACTopology.VRF_HVRF, "Typically Similar to 4 pipe FCU" },
                { (int)HVACTopology.SplitSystem, "" },
                { (int)HVACTopology.VAVElectricHeating, "Typically Similar to 2 Pipe FCU" },
                { (int)HVACTopology.VAVLTHWHeating, "Typically Similar to 4 Pipe FCU" }
            };

            Card3ProrationFactorDescriptionMap = new()
            {
                { (int)HVACTopology.TwoPipeFanCoilsElectricHeating, "Depending if htg driven." },
                { (int)HVACTopology.FourPipeFanCoilsLTHW, "Should just be the fan in the FCU." },
                { (int)HVACTopology.LocalElecHeating, "i.e. spaces that only have heating and no cooling, the HVAC number should be proportionate to what should be in the zone - drying room, etc." },
                { (int)HVACTopology.VRF_HVRF, "" },
                { (int)HVACTopology.SplitSystem, "" },
                { (int)HVACTopology.VAVElectricHeating, "Less likely to have a fan." },
                { (int)HVACTopology.VAVLTHWHeating, "Less likely to have a fan." }
            };

            // Set initial selected items using display names
            if (HVACMethodologyDisplayNames.ContainsKey(SelectedDBZoneHVACMethodology))
                SelectedItem1 = HVACMethodologyDisplayNames[SelectedDBZoneHVACMethodology];
            else
                SelectedItem1 = HVACMethodologyDisplayNames.Values.FirstOrDefault();

            if (HVACTopologyDisplayNames.ContainsKey(SelectedDBZoneHVACTopology))
                SelectedItem2 = HVACTopologyDisplayNames[SelectedDBZoneHVACTopology];
            else
                SelectedItem2 = HVACTopologyDisplayNames.Values.FirstOrDefault();

        }
        // Handle Card 1 selection change
        partial void OnSelectedItem1Changed(string value)
        {
            Card1TitleText = value;

            // Find the enum value from the display name
            var enumValue = HVACMethodologyDisplayNames.FirstOrDefault(x => x.Value == value).Key;

            if (Card1HVACMethodologyMap.TryGetValue(enumValue, out var mappedContent))
                Card1ContentText = mappedContent;

            // Update the selected zone's HVAC methodology
            if (SelectedZone != null)
            {
                SelectedZone.HVACMethodology = enumValue;
                SelectedDBZoneHVACMethodology = enumValue;
            }

            // Visibility Logic for Card 3
            if (enumValue == (int)HVACMethodology.UserSpecifiedLumpLoads)
            {
                IsCard3Visible = false;
                return;
            }

            if (enumValue == (int)HVACMethodology.NoHVAC)
            {
                IsCard3Visible = false;
                return;
            }

            if (enumValue == (int)HVACMethodology.HVACTopologyModifiedPD)
            {
                IsGrid3AVisible = true;
                IsGrid3BVisible = false;
                IsCard3Visible = true;
            }
            else if (enumValue == (int)HVACMethodology.UserSpecifiedVAPerM2)
            {
                IsGrid3AVisible = false;
                IsGrid3BVisible = true;
                IsCard3Visible = true;
            }
            else
            {
                IsCard3Visible = false;
            }

            if (enumValue == (int)HVACMethodology.UserSpecifiedVAPerM2) // "User Specified (VA/m²) (Rec. prelim/developed)"
            {
                DBZoneHVACNumericalInput = SelectedZone?.DBZoneHVAC ?? 0;
            }
            else
            {
                DBZoneHVACNumericalInput = 0;
            }

            // Trigger recalculation since HVAC methodology affects calculations
            RecalculateAll();
        }

        // Handle Card 2 selection change
        partial void OnSelectedItem2Changed(string value)
        {
            // Find the enum value from the display name
            var enumValue = HVACTopologyDisplayNames.FirstOrDefault(x => x.Value == value).Key;

            // Update the selected zone's HVAC topology
            if (SelectedZone != null)
            {
                SelectedZone.HVACTopology = enumValue;
                SelectedDBZoneHVACTopology = enumValue;
            }

            if (Card2HVACToplogyMap.TryGetValue(enumValue, out var card2MappedCentralisedLoad))
            {
                Card2CentralisedLoadText = card2MappedCentralisedLoad;
            }

            if (Card2KeyConsiderationdMap.TryGetValue(enumValue, out var card2MappedKeyConsideration))
            {
                Card2KeyConsiderationText = card2MappedKeyConsideration;
            }

            if (Card2SideNoteMap.TryGetValue(enumValue, out var card2MappedSideNote))
            {
                Card2SideNoteText = card2MappedSideNote;
            }

            if (Card2IsFanVisibleMap.TryGetValue(enumValue, out var MappedFan))
            {
                IsFanVisible = MappedFan;
            }

            if (Card2IsElecHtgVisibleMap.TryGetValue(enumValue, out var MappedIsElecHtgVisible))
            {
                IsElecHtgVisible = MappedIsElecHtgVisible;
            }

            if (Card2IsOtherNotableVisibleMap.TryGetValue(enumValue, out var MappedOtherNotable))
            {
                isOtherNotableVisible = MappedOtherNotable;
            }

            if (Card3ProrationFactorMap.TryGetValue(enumValue, out var MappedProrationFactor))
            {
                Card3ProrartionFactorText = MappedProrationFactor;
            }

            if (Card3ProrationFactorDescriptionMap.TryGetValue(enumValue, out var MappedProrationFactorDescription))
            {
                Card3ProrartionFactorDescriptionText = MappedProrationFactorDescription;
            }

            // Trigger recalculation since HVAC topology affects calculations
            RecalculateAll();
        }

        partial void OnDBZoneHVACNumericalInputChanged(double value)
        {
            // Update the SelectedZone's DB HVAC based on the numerical input
            if (SelectedZone != null)
            {
                SelectedZone.DBZoneHVAC = value;
                RecalculateAll();
            }
        }

        #endregion

        #endregion
    }

    class ZoneSpace
    {
        public string ZoneName { get; set; }
        public string SpaceName { get; set; }
    }
}
