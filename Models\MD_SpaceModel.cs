﻿using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MEP.MaxDemand.CoreLogic.Calculation;
using BecaRevitUtilities;
using BecaRevitUtilities.ElementUtilities;
using MEP.MaxDemand.CoreLogic;
using System.Collections.ObjectModel;
using Autodesk.Revit.DB.ExtensibleStorage;
using Newtonsoft.Json;
using MEP.MaxDemand.CoreLogic.MD_DataStorage;

namespace MEP.MaxDemand.Models
{
    public enum RoomSpaceState
    {
        Unknown,
        Placed,
        Unplaced,
        Unbounded,
        Redundant
    }

    public partial class MD_SpaceModel : ObservableObject
    {
        #region Fields
        List<PDLoadRow> _pDLookupData;
        #endregion

        #region Properties

        // Multiple Lump Loads
        [ObservableProperty]
        private ObservableCollection<MD_SpatialEquipment> spatialEquipments;
        [ObservableProperty]
        private int numberOfEquipments;
        [ObservableProperty]
        private bool hasEquipments;

        // Space Input: Space Information (From Revit)
        [ObservableProperty]
        private Space space;
        [ObservableProperty]
        private ElementId id;
        [ObservableProperty]
        private string spaceName;
        [ObservableProperty]
        private string spaceNumber;
        [ObservableProperty]
        private double area;
        [ObservableProperty]
        private string notes;
        [ObservableProperty]
        private Level level;
        [ObservableProperty]
        private string levelName;
        [ObservableProperty]
        private bool isLocked;
        [ObservableProperty]
        private string currentOwner;
        [ObservableProperty]
        private string spaceStatus;

        // Space Input: Load Densities (Updated from PDLookup)
        [ObservableProperty]
        private string spatialFunction;
        [ObservableProperty]
        private double smallPowerLoadDensity;
        [ObservableProperty]
        private double lightingLoadDensity;
        [ObservableProperty]
        private double mechLoadDensity;
        [ObservableProperty]
        private double adjustedMechLoadDensity;

        // Space Input: Space Lump Load (User input)
        [ObservableProperty]
        private double singlePhaseLumpLoad;
        [ObservableProperty]
        private double threePhaseLumpLoad;
        [ObservableProperty]
        private double lumpLoadDiversity;
        [ObservableProperty]
        private double lumpLoadPowerFactor;

        // Space Calculations: Load Type Totals (Calculated on property change)
        [ObservableProperty]
        private double smallPowerLoad;
        [ObservableProperty]
        private double lightingLoad;
        [ObservableProperty]
        private double mechLoad;
        [ObservableProperty]
        private double lumpLoad;

        // Space Calculations: Space Load Calculations (Calculated on property change)
        [ObservableProperty]
        private double calculatedDiversity;
        [ObservableProperty]
        private double diversifiedLoad_kVA;
        [ObservableProperty]
        private double calculatedPowerFactor;
        [ObservableProperty]
        private double diversifiedLoad_kW;
        [ObservableProperty]
        private double diversifiedCurrent_A;

        // Sum Products (calculated on property change)
        [ObservableProperty]
        private double powerLoadDensitySumProduct;
        [ObservableProperty]
        private double lightingLoadDensitySumProduct;
        [ObservableProperty]
        private double lumpLoadPowerFactorSumProduct;
        [ObservableProperty]
        private double mechDensitySumProduct;
        [ObservableProperty]
        private double lumpLoadDiversitySumProduct;
        [ObservableProperty]
        private double zonePowerFactorSumProduct;
        [ObservableProperty]
        private double zoneDiversitySumProduct;

        // Totals
        [ObservableProperty]
        private double subTotals;
        [ObservableProperty]
        private double totalSpaceLoad;
        #endregion

        #region Constructor
        public MD_SpaceModel(Space space, List<PDLoadRow> pDLookupData)
        {
            // Revit Space
            Space = space;
            Id = Space.Id;
            SpaceName = space.Name;
            SpaceNumber = space.Number;
            Area = RevitUnitConvertor.InternalToSquareMeters(space.Area);
            Level = space.Document.GetElement(space.LevelId) as Level;
            LevelName = Level.Name;
            SpaceStatus = MD_Helper.GetSpaceStatus(space).ToString();

            _pDLookupData = pDLookupData;

            var doc = Space.Document;
            IsLocked = Space.IsLocked(doc);
            CurrentOwner = Space.ElementOwner(doc);

            // Read only from Load Density database
            SpatialFunction = GetSpatialFunction(space);

            // Set Lighting, Power, Mech load densities
            SetDensities(space, SpatialFunction, _pDLookupData);

            // User input in UI
            SinglePhaseLumpLoad = space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_1Phase)?.AsDouble() ?? 0;
            ThreePhaseLumpLoad = space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_3Phase)?.AsDouble() ?? 0;
            double lumpLoadDiversityValue = space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_Diversity)?.AsDouble() ?? 0.5;
            double lumpLoadPowerFactorValue = space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_Power_Factor)?.AsDouble() ?? 0.9;
            LumpLoadDiversity = lumpLoadDiversityValue == 0 ? 0.5 : lumpLoadDiversityValue;
            LumpLoadPowerFactor = lumpLoadPowerFactorValue == 0 ? 0.9 : lumpLoadPowerFactorValue;
            Notes = space.LookupParameter("Beca Comments").AsValueString();

            // Retrieve and deserialize SpatialEquipments json data from Space Entity
            SpatialEquipments = GetSpatialEquipments(space);
            NumberOfEquipments = SpatialEquipments?.Count ?? 0;
            HasEquipments = NumberOfEquipments > 0;
        }
        #endregion

        #region Methods
        private string GetSpatialFunction(Space s)
        {
            // Attempt to derive the spatial function from the SpaceTypeId (expected to be set by LPD)
            var spaceType = s.Document.GetElement(s.SpaceTypeId);
            string spatialFunction = spaceType?.Name?
                .Replace(MD_Constants.SpacePrefixAu, "")
                .Replace(MD_Constants.SpacePrefixNZ, "") ?? string.Empty;

            // If spaceType name didn't provide a value, fall back to the MD_SpatialFunction parameter
            if (string.IsNullOrEmpty(spatialFunction))
            {
                var param = s.LookupParameter(MD_Constants.SpaceParameterName_Spatial_Function);
                spatialFunction = param?.AsString() ?? string.Empty;
            }

            return spatialFunction;
        }

        private ObservableCollection<MD_SpatialEquipment> GetSpatialEquipments(Space s)
        {
            var spaceEntity = s.GetEntity(DBZoneManager.GetSchemaByName(DBZoneManager.SpaceSchemaName));
            if (spaceEntity != null && spaceEntity.IsValid())
            {
                string storedJsonData = spaceEntity.Get<string>(DBZoneManager.SpaceEntityFieldName);
                return JsonConvert.DeserializeObject<ObservableCollection<MD_SpatialEquipment>>(storedJsonData);
            }
            else
            {
                return null;
            }
        }

        private void SetDensities(Space space, string spatialFunction, List<PDLoadRow> pDLookupData)
        {
            // Set Lighting Load Density
            LightingLoadDensity = GetLoadDensity(
                space,
                MD_Constants.SpaceParameterName_Lighting_Power_Density,
                spatialFunction,
                pDLookupData,
                pDLoad => pDLoad.LightingDensity
            );

            // Set Small Power Load Density
            SmallPowerLoadDensity = GetLoadDensity(
                space,
                MD_Constants.SpaceParameterName_Small_Power_Density,
                spatialFunction,
                pDLookupData,
                pDLoad => pDLoad.PowerDensity
            );

            // Set Mechanical Load Density
            MechLoadDensity = GetLoadDensity(
                space,
                MD_Constants.SpaceParameterName_Mech_Power_Density,
                spatialFunction,
                pDLookupData,
                pDLoad => pDLoad.MechanicalDensity
            );
        }

        private double GetLoadDensity(Space space, string parameterName, string spatialFunction, List<PDLoadRow> pDLookupData, Func<PDLoadRow, double> defaultDensitySelector)
        {
            // Attempt to retrieve the value from the space parameter
            var density = Math.Round(space.LookupParameter(parameterName)?.AsDouble() ?? 0, 2);

            // If retrieved density is 0, fall back to the lookup data
            if (density == 0 && !string.IsNullOrEmpty(spatialFunction))
            {
                var pDLoad = pDLookupData.Find(p => p.SpatialFunction == spatialFunction);
                if (pDLoad != null)
                    density = defaultDensitySelector(pDLoad);
            }

            return density;
        }
        #endregion
    }

    public partial class MD_SpatialEquipment : ObservableObject
    {
        [ObservableProperty]
        private string equipmentName;
        [ObservableProperty]
        private double singlePhaseLumpLoad;
        [ObservableProperty]
        private double threePhaseLumpLoad;
        [ObservableProperty]
        private double lumpLoadPowerFactor;
        [ObservableProperty]
        private double lumpLoadDiversity;
    }
}
