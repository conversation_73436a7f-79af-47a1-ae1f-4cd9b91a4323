﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.MaxDemand.UI
{
    public static class UIHelper
    {
        public static void AlignTableLayoutPanelWithDataGridView(TableLayoutPanel tableLayoutPanel, DataGridView dataGridView)
        {
            if (tableLayoutPanel.ColumnCount != dataGridView.Columns.Count)
                return;

            // Update TableLayoutPanel column widths to match DataGridView
            for (int i = 0; i < dataGridView.Columns.Count; i++)
            {
                tableLayoutPanel.ColumnStyles[i].Width = dataGridView.Columns[i].Width;
            }

            tableLayoutPanel.PerformLayout();
        }

        public static void InitializeTableLayoutPanel(TableLayoutPanel tableLayoutPanel, DataGridView dataGridView)
        {
            // Clear existing columns in TableLayoutPanel
            //tableLayoutPanel.ColumnStyles.Clear();
            tableLayoutPanel.ColumnCount = dataGridView.Columns.Count;

            // Add columns to TableLayoutPanel corresponding to DataGridView
            for (int i = 0; i < dataGridView.Columns.Count; i++)
            {
                tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, dataGridView.Columns[i].Width));
            }

        }
    }
}
