using Autodesk.Revit.DB;
using Autodesk.Revit.DB.ExtensibleStorage;
using Autodesk.Revit.DB.Mechanical;
using DocumentFormat.OpenXml.Spreadsheet;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.MaxDemand.Models
{
    /// <summary>
    /// HVAC Estimation Methodology options
    /// </summary>
    public enum HVACMethodology
    {
        GenericBecaPD = 0,
        HVACTopologyModifiedPD = 1,
        UserSpecifiedVAPerM2 = 2,
        UserSpecifiedLumpLoads = 3,
        NoHVAC = 4
    }

    /// <summary>
    /// HVAC Topology options
    /// </summary>
    public enum HVACTopology
    {
        PleaseSelect = 0,
        FourPipeFanCoilsLTHW = 1,
        TwoPipeFanCoilsElectricHeating = 2,
        LocalElecHeating = 3,
        VRF_HVRF = 4,
        SplitSystem = 5,
        VAVElectricHeating = 6,
        VAVLTHWHeating = 7
    }
    public partial class MD_ZoneModel : ObservableObject
    {
        #region Properties
        // Zone entity properties storage 
        public DataStorage DBZoneDataStorage { get; set; }

        // Default selection for HVAC Topology dropdown control
        public List<string> HVACTopologyOptions { get; } = new List<string> { "Generic / TBC", "VAV", "VFR", "2 Pipe", "4 Pipe", "Electric Heating", "None" };

        // Available Spaces and DBs in this Zone
        [ObservableProperty]
        private ObservableCollection<MD_DBModel> dBs = new ObservableCollection<MD_DBModel>();
        [ObservableProperty]
        private ObservableCollection<MD_SpaceModel> spaces = new ObservableCollection<MD_SpaceModel>();

        // Main MD Zone Properties (From Revit)
        [ObservableProperty]
        private string zoneName;
        [ObservableProperty]
        private bool isLocked;
        [ObservableProperty]
        private string currentOwners;
        [ObservableProperty]
        private double totalArea;

        // DB Input: Distribution Board Information (Calculated on property change)
        [ObservableProperty]
        private double diversifiedPerPhaseCurrent;

        // Space Input: Load Densities (Calculated on property change)
        [ObservableProperty]
        private double lightingLoadDensity;
        [ObservableProperty]
        private double smallPowerLoadDensity;
        [ObservableProperty]
        private double mechLoadDensity;

        // DB Input: Space Lump Load (Calculated on property change) 
        [ObservableProperty]
        private double singlePhaseLumpLoadSum;
        [ObservableProperty]
        private double threePhaseLumpLoadSum;
        [ObservableProperty]
        private double lumpLoadDiversitySum;
        [ObservableProperty]
        private double lumpLoadPowerFactorSum;

        // HVAC Topology Load Factor (User selection)
        [ObservableProperty]
        private double hVACTopologyLoadFactor; // This is not used from 04/08/2025 dev, updated to store DBZoneHVAC

        // Selected HVAC Methodology from dropdown
        [ObservableProperty]
        private int hVACMethodology;
        // Selected HVAC Topology from dropdown
        [ObservableProperty]
        private int hVACTopology;
        // DB Zone HVAC in HVAC Input tab (User input)
        [ObservableProperty]
        private double dBZoneHVAC;

        // DB Zone Input : Essential Load (User input)
        [ObservableProperty]
        private int essentialSmallPowerLoadPercentage;
        [ObservableProperty]
        private int essentialLightingLoadPercentage;
        [ObservableProperty]
        private int essentialMechanicalLoadPercentage;
        [ObservableProperty]
        private int essentialLumpLoadPercentage;

        // DB Zone Input : Uninteruptable Load (User input)
        [ObservableProperty]
        private int uninteruptableSmallPowerLoadPercentage;
        [ObservableProperty]
        private int uninteruptableLightingLoadPercentage;
        [ObservableProperty]
        private int uninteruptableMechanicalLoadPercentage;
        [ObservableProperty]
        private int uninteruptableLumpLoadPercentage;

        // Space Calculations: Load Type Totals (Calculated on property change)
        [ObservableProperty]
        private double smallPowerLoadSum;
        [ObservableProperty]
        private double lightingLoadSum;
        [ObservableProperty]
        private double mechLoadSum;
        [ObservableProperty]
        private double lumpLoadSum;

        // Space Calculations: Space Load Calculations (Calculated on property change)
        [ObservableProperty]
        private double calculatedDiversity;
        [ObservableProperty]
        private double diversifiedLoad_kVA;
        [ObservableProperty]
        private double calculatedPowerFactor;
        [ObservableProperty]
        private double diversifiedLoad_kW;
        [ObservableProperty]
        private double diversifiedCurrent_A;

        // Diversified Load Values: General Load (Calculated on property change)
        [ObservableProperty]
        private double generalSmallPowerLoad;
        [ObservableProperty]
        private double generalLightingLoad;
        [ObservableProperty]
        private double generalMechanicalLoad;
        [ObservableProperty]
        private double generalLumpLoad;

        // Diversified Load Values: Essential Load (Calculated on property change)
        [ObservableProperty]
        private double essentialSmallPowerLoad;
        [ObservableProperty]
        private double essentialLightingLoad;
        [ObservableProperty]
        private double essentialMechanicalLoad;
        [ObservableProperty]
        private double essentialLumpLoad;

        // Diversified Load Values: Uninteruptable Load(Calculated on property change)
        [ObservableProperty]
        private double uninteruptableSmallPowerLoad;
        [ObservableProperty]
        private double uninteruptableLightingLoad;
        [ObservableProperty]
        private double uninteruptableMechanicalLoad;
        [ObservableProperty]
        private double uninteruptableLumpLoad;

        // Load Allocation Counts: General Load (Calculated on property change)
        [ObservableProperty]
        private int generalSmallPowerLoadCount;
        [ObservableProperty]
        private int generalLightingLoadCount;
        [ObservableProperty]
        private int generalMechanicalLoadCount;
        [ObservableProperty]
        private int generalLumpLoadCount;

        // Load Allocation Counts: Essential Load (Calculated on property change)
        [ObservableProperty]
        private int essentialSmallPowerLoadCount;
        [ObservableProperty]
        private int essentialLightingLoadCount;
        [ObservableProperty]
        private int essentialMechanicalLoadCount;
        [ObservableProperty]
        private int essentialLumpLoadCount;

        // Load Allocation Counts: Uninteruptable Load (Calculated on property change)
        [ObservableProperty]
        private int uninteruptableSmallPowerLoadCount;
        [ObservableProperty]
        private int uninteruptableLightingLoadCount;
        [ObservableProperty]
        private int uninteruptableMechanicalLoadCount;
        [ObservableProperty]
        private int uninteruptableLumpLoadCount;
        #endregion

    }
}