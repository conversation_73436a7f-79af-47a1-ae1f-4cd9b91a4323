﻿namespace MEP.MaxDemand.UI.Forms
{
    partial class FrmSummary
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.eadgv_DbSummary = new Common.UI.Controls.ExtendedAdvancedDataGridView();
            this.DB = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DiversifiedPerPhaseCurrentA = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.TotalDiversifiedLoad_kVA = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.AveragePowerFactor = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.TotalDiversifiedLoad_kW = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.label69 = new System.Windows.Forms.Label();
            this.tableLayoutPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.eadgv_DbSummary)).BeginInit();
            this.SuspendLayout();
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 1;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 946F));
            this.tableLayoutPanel2.Controls.Add(this.eadgv_DbSummary, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.label69, 0, 0);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 2;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(946, 465);
            this.tableLayoutPanel2.TabIndex = 296;
            // 
            // eadgv_DbSummary
            // 
            this.eadgv_DbSummary.AllowDrop = true;
            this.eadgv_DbSummary.AllowUserToAddRows = false;
            this.eadgv_DbSummary.AllowUserToDeleteRows = false;
            this.eadgv_DbSummary.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.eadgv_DbSummary.BackgroundColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.eadgv_DbSummary.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.eadgv_DbSummary.ColumnHeadersHeight = 60;
            this.eadgv_DbSummary.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.DB,
            this.DiversifiedPerPhaseCurrentA,
            this.TotalDiversifiedLoad_kVA,
            this.AveragePowerFactor,
            this.TotalDiversifiedLoad_kW});
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Arial Narrow", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.eadgv_DbSummary.DefaultCellStyle = dataGridViewCellStyle2;
            this.eadgv_DbSummary.Dock = System.Windows.Forms.DockStyle.Fill;
            this.eadgv_DbSummary.EnableHeadersVisualStyles = false;
            this.eadgv_DbSummary.FilterAndSortEnabled = true;
            this.eadgv_DbSummary.FilterStringChangedInvokeBeforeDatasourceUpdate = true;
            this.eadgv_DbSummary.Location = new System.Drawing.Point(0, 38);
            this.eadgv_DbSummary.Margin = new System.Windows.Forms.Padding(0, 3, 6, 3);
            this.eadgv_DbSummary.Name = "eadgv_DbSummary";
            this.eadgv_DbSummary.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.eadgv_DbSummary.RowHeadersVisible = false;
            this.eadgv_DbSummary.RowHeadersWidth = 51;
            this.eadgv_DbSummary.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect;
            this.eadgv_DbSummary.Size = new System.Drawing.Size(940, 424);
            this.eadgv_DbSummary.SortStringChangedInvokeBeforeDatasourceUpdate = true;
            this.eadgv_DbSummary.TabIndex = 196;
            // 
            // DB
            // 
            this.DB.HeaderText = "Distribution Board";
            this.DB.MinimumWidth = 22;
            this.DB.Name = "DB";
            this.DB.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            // 
            // DiversifiedPerPhaseCurrentA
            // 
            this.DiversifiedPerPhaseCurrentA.HeaderText = "Diversified Per Phase Current (A)";
            this.DiversifiedPerPhaseCurrentA.MinimumWidth = 22;
            this.DiversifiedPerPhaseCurrentA.Name = "DiversifiedPerPhaseCurrentA";
            this.DiversifiedPerPhaseCurrentA.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.DiversifiedPerPhaseCurrentA.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            // 
            // TotalDiversifiedLoad_kVA
            // 
            this.TotalDiversifiedLoad_kVA.HeaderText = "Total Diversified Load (kVA)";
            this.TotalDiversifiedLoad_kVA.MinimumWidth = 22;
            this.TotalDiversifiedLoad_kVA.Name = "TotalDiversifiedLoad_kVA";
            this.TotalDiversifiedLoad_kVA.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            // 
            // AveragePowerFactor
            // 
            this.AveragePowerFactor.HeaderText = "Average Power Factor";
            this.AveragePowerFactor.MinimumWidth = 22;
            this.AveragePowerFactor.Name = "AveragePowerFactor";
            this.AveragePowerFactor.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            // 
            // TotalDiversifiedLoad_kW
            // 
            this.TotalDiversifiedLoad_kW.HeaderText = "Total Diversified Load (kW)";
            this.TotalDiversifiedLoad_kW.MinimumWidth = 22;
            this.TotalDiversifiedLoad_kW.Name = "TotalDiversifiedLoad_kW";
            this.TotalDiversifiedLoad_kW.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            // 
            // label69
            // 
            this.label69.AutoSize = true;
            this.label69.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            this.label69.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label69.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label69.ForeColor = System.Drawing.Color.White;
            this.label69.Location = new System.Drawing.Point(1, 1);
            this.label69.Margin = new System.Windows.Forms.Padding(1, 1, 7, 1);
            this.label69.Name = "label69";
            this.label69.Size = new System.Drawing.Size(938, 33);
            this.label69.TabIndex = 231;
            this.label69.Text = "SUMMARY OF RESULTS BY DISTRIBUTION BOARD";
            this.label69.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // FrmSummary
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(946, 465);
            this.Controls.Add(this.tableLayoutPanel2);
            this.Name = "FrmSummary";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Summary";
            this.tableLayoutPanel2.ResumeLayout(false);
            this.tableLayoutPanel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.eadgv_DbSummary)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private TableLayoutPanel tableLayoutPanel2;
        private Common.UI.Controls.ExtendedAdvancedDataGridView eadgv_DbSummary;
        private Label label69;
        private DataGridViewTextBoxColumn DB;
        private DataGridViewTextBoxColumn DiversifiedPerPhaseCurrentA;
        private DataGridViewTextBoxColumn TotalDiversifiedLoad_kVA;
        private DataGridViewTextBoxColumn AveragePowerFactor;
        private DataGridViewTextBoxColumn TotalDiversifiedLoad_kW;
    }
}