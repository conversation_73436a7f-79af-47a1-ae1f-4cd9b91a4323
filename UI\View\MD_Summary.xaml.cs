﻿using ClosedXML.Excel;
using Common.OpenXML.Macros.Projects.MaxDemand;
using MEP.MaxDemand.ViewModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace MEP.MaxDemand.UI.View
{
    /// <summary>
    /// Interaction logic for MD_Summary.xaml
    /// </summary>
    public partial class MD_Summary : Page
    {
        private MD_ViewModel _viewModel;

        public MD_Summary(MD_ViewModel viewModel)
        {
            InitializeComponent();

            _viewModel = viewModel;
            CheckSizesFromLookup(_viewModel);

            DataContext = _viewModel;
        }

        private void CheckSizesFromLookup(MD_ViewModel viewModel)
        {
            string filePath = MEP.MaxDemand.CoreLogic.MD_Constants.Default_MaxDemandTemplatePath;

            // Read size arrays from Excel
            int[] transformerSizes = ReadIntegersFromExcel(filePath, 6); // Column G
            int[] generatorSizes = ReadIntegersFromExcel(filePath, 9);  // Column I
            int[] uPSSizes = ReadIntegersFromExcel(filePath, 10);       // Column J

            // Update the corresponding properties in the view model
            UpdateSize(viewModel, transformerSizes, value => viewModel.Data.TransformerSizekVA = value);
            UpdateSize(viewModel, generatorSizes, value => viewModel.Data.GeneratorSizekVA = value);
            UpdateSize(viewModel, uPSSizes, value => viewModel.Data.UPSSizekVA = value);
        }

        /// <summary>
        /// Updates the view model's property based on the given sizes array and a matching condition.
        /// </summary>
        /// <param name="viewModel">The ViewModel containing data to update.</param>
        /// <param name="sizesArray">The array of sizes read from Excel.</param>
        /// <param name="updateAction">An action defining how to set the matching value in the ViewModel.</param>
        private void UpdateSize(MD_ViewModel viewModel, int[] sizesArray, Action<int> updateAction)
        {
            if (sizesArray.Length > 0) // Ensure sizes array is not empty
            {
                var requiredSize = sizesArray.FirstOrDefault(size =>
                    size >= viewModel.Data.OverallSupplyTotalSiteDiversifiedLoadkVA);

                if (requiredSize > 0) // Only set if a valid match is found
                {
                    updateAction(requiredSize);
                }
            }
        }

        public static int[] ReadIntegersFromExcel(string filePath, int columnNumber)
        {
            try
            {
                // Validate the file path
                if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                {
                    Console.WriteLine($"File not found: {filePath}");
                    return Array.Empty<int>(); // Return an empty array
                }

                List<int> numbers = new List<int>();

                // Open the Excel file using ClosedXML
                using (var workbook = new XLWorkbook(filePath))
                {
                    // Get the worksheet named "Data"
                    var worksheet = workbook.Worksheet("Data");
                    if (worksheet == null)
                    {
                        Console.WriteLine("Sheet 'Data' not found in Excel file.");
                        return Array.Empty<int>(); // Return an empty array
                    }

                    // Start reading from row 2 (skip header row)
                    int startRow = 2;

                    while (true)
                    {
                        var cellValue = worksheet.Cell(startRow, columnNumber).Value;

                        if (string.IsNullOrWhiteSpace(cellValue.ToString()))
                        {
                            // Exit loop if a blank cell is encountered
                            break;
                        }

                        if (int.TryParse(cellValue.ToString(), out int number))
                        {
                            numbers.Add(number);
                        }
                        else
                        {
                            Console.WriteLine($"Invalid data at row {startRow}, column {columnNumber}. Expected a number.");
                            return Array.Empty<int>(); // Return an empty array on invalid data
                        }

                        startRow++;
                    }
                }

                return numbers.ToArray();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while reading Excel file: {ex.Message}");
                return Array.Empty<int>(); // Return an empty array on any error
            }
        }

        private void BackToZones_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.GoBack();
        }

    }
}
