using System;
using System.Linq;
using MEP.MaxDemand.Models;
using MEP.MaxDemand.ViewModels;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace MEP.MaxDemand.Tests
{
    [TestClass]
    public class HVACMappingTest
    {
        [TestMethod]
        public void TestEnumValues()
        {
            // Test that enum values match expected integers
            Assert.AreEqual(0, (int)HVACMethodology.GenericBecaPD);
            Assert.AreEqual(1, (int)HVACMethodology.HVACTopologyModifiedPD);
            Assert.AreEqual(2, (int)HVACMethodology.UserSpecifiedVAPerM2);
            Assert.AreEqual(3, (int)HVACMethodology.UserSpecifiedLumpLoads);
            Assert.AreEqual(4, (int)HVACMethodology.NoHVAC);

            Assert.AreEqual(0, (int)HVACTopology.PleaseSelect);
            Assert.AreEqual(1, (int)HVACTopology.FourPipeFanCoilsLTHW);
            Assert.AreEqual(2, (int)HVACTopology.TwoPipeFanCoilsElectricHeating);
            Assert.AreEqual(3, (int)HVACTopology.LocalElecHeating);
            Assert.AreEqual(4, (int)HVACTopology.VRF_HVRF);
            Assert.AreEqual(5, (int)HVACTopology.SplitSystem);
            Assert.AreEqual(6, (int)HVACTopology.VAVElectricHeating);
            Assert.AreEqual(7, (int)HVACTopology.VAVLTHWHeating);
        }

        [TestMethod]
        public void TestDictionaryKeysAreEnumValues()
        {
            // Create a mock ViewModel to test the dictionary initialization
            // Note: This would need to be adapted based on your actual constructor requirements
            // var viewModel = new MD_ViewModel(null, null, null, null, null);
            
            // For now, just test that the enum values can be used as dictionary keys
            var testDict = new Dictionary<int, string>
            {
                { (int)HVACMethodology.GenericBecaPD, "Test Value 1" },
                { (int)HVACMethodology.HVACTopologyModifiedPD, "Test Value 2" },
                { (int)HVACTopology.PleaseSelect, "Test Topology 1" },
                { (int)HVACTopology.FourPipeFanCoilsLTHW, "Test Topology 2" }
            };

            Assert.IsTrue(testDict.ContainsKey((int)HVACMethodology.GenericBecaPD));
            Assert.IsTrue(testDict.ContainsKey((int)HVACTopology.PleaseSelect));
            Assert.AreEqual("Test Value 1", testDict[(int)HVACMethodology.GenericBecaPD]);
            Assert.AreEqual("Test Topology 1", testDict[(int)HVACTopology.PleaseSelect]);
        }

        [TestMethod]
        public void TestDisplayNameMapping()
        {
            // Test that display names can be mapped to enum values
            var methodologyDisplayNames = new Dictionary<int, string>
            {
                { (int)HVACMethodology.GenericBecaPD, "Generic - Using Beca PD Dataset(rec. Concept)" },
                { (int)HVACMethodology.HVACTopologyModifiedPD, "HVAC Topology - Using Modified PD Values (%) (Recommended at the concept or preliminary design stage only)" },
                { (int)HVACMethodology.UserSpecifiedVAPerM2, "User Specified (VA/m²) (Rec. prelim/developed)" },
                { (int)HVACMethodology.UserSpecifiedLumpLoads, "User Specified Lump Loads (Expected at Detailed Design or possibly earlier)" },
                { (int)HVACMethodology.NoHVAC, "None (No HVAC)" }
            };

            var topologyDisplayNames = new Dictionary<int, string>
            {
                { (int)HVACTopology.PleaseSelect, "Please select" },
                { (int)HVACTopology.FourPipeFanCoilsLTHW, "4-pipe fan coils (LTHW heating)" },
                { (int)HVACTopology.TwoPipeFanCoilsElectricHeating, "2-pipe fan coils with electric heating" },
                { (int)HVACTopology.LocalElecHeating, "Local Elec Heating" },
                { (int)HVACTopology.VRF_HVRF, "VRF / HVRF" },
                { (int)HVACTopology.SplitSystem, "Split system" },
                { (int)HVACTopology.VAVElectricHeating, "VAV with electric heating" },
                { (int)HVACTopology.VAVLTHWHeating, "VAV with LTHW heating" }
            };

            // Test that we can find enum values from display names
            var displayName = "Generic - Using Beca PD Dataset(rec. Concept)";
            var enumValue = methodologyDisplayNames.FirstOrDefault(x => x.Value == displayName).Key;
            Assert.AreEqual((int)HVACMethodology.GenericBecaPD, enumValue);

            displayName = "4-pipe fan coils (LTHW heating)";
            enumValue = topologyDisplayNames.FirstOrDefault(x => x.Value == displayName).Key;
            Assert.AreEqual((int)HVACTopology.FourPipeFanCoilsLTHW, enumValue);
        }
    }
}
