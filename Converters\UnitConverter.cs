﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace MEP.MaxDemand.Converters
{
    public class UnitConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double numericValue && parameter is string unit)
            {
                // Format the numeric value with the unit
                return $"{numericValue:F2} {unit}"; // :F2 two decimal places
            }
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue && parameter is string unit)
            {
                // Remove the unit and convert back to double
                stringValue = stringValue.Replace(unit, "").Trim();
                if (double.TryParse(stringValue, out double numericValue))
                {
                    return numericValue;
                }
            }
            return value;
        }
    }
}
