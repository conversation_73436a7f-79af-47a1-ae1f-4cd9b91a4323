﻿<Page
    x:Class="MEP.MaxDemand.UI.View.MD_ZoneEdit"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.MaxDemand.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.MaxDemand.UI.View"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:validations="clr-namespace:MEP.MaxDemand.Validations"
    Title="MD_ZoneEdit"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="White"
    mc:Ignorable="d">

    <!--  Resources  -->
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <!--  Page-specific resources  -->
            <converters:PercentageConverter x:Key="PercentageConverter" />
            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
            <converters:NullToInverseVisibilityConverter x:Key="NullToInverseVisibilityConverter" />
            <converters:RoundingConverter x:Key="RoundingConverter" />
            <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
            <converters:IndexToStrikethroughConverter x:Key="IndexToStrikethroughConverter" />
            <converters:StringToTextDecorationsConverter x:Key="StringToTextDecorationsConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="446*" />
            <ColumnDefinition Width="355*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <StackPanel Grid.ColumnSpan="2" Orientation="Horizontal">
            <TextBlock
                Margin="15,5,0,10"
                Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                Text="LOAD PLANNING - " />
            <TextBlock
                Margin="15,5,0,10"
                Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                Text="{Binding SelectedZone.ZoneName}" />
        </StackPanel>
        <Button
            Grid.Column="1"
            Height="45"
            Margin="292,0,0,0"
            HorizontalAlignment="Right"
            VerticalAlignment="Center"
            Background="Transparent"
            BorderBrush="Transparent"
            Command="{Binding OpenDocumentationCommand}"
            Content="{materialDesign:PackIcon Kind=HelpCircleOutline,
                                              Size=30}"
            Foreground="#12A8B2" />
        <Separator
            Grid.ColumnSpan="2"
            Margin="10,40,15,0"
            Background="#FFCE00">
            <Separator.LayoutTransform>
                <ScaleTransform ScaleY="3.5" />
            </Separator.LayoutTransform>
        </Separator>

        <!--  Tab Control  -->
        <materialDesign:Card
            Grid.Row="1"
            Grid.ColumnSpan="2"
            Margin="20,10,20,10">
            <TabControl Margin="10">
                <!--  Spaces Input Tab  -->
                <TabItem Background="#12A8B2">
                    <TabItem.Header>
                        <TextBlock
                            FontSize="18"
                            FontWeight="DemiBold"
                            Foreground="#12A8B2"
                            Text="Space Input" />
                    </TabItem.Header>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition />
                        </Grid.RowDefinitions>

                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="0"
                            Margin="10,10,0,0"
                            Style="{StaticResource MaterialDesignBody2TextBlock}"
                            Text="Select spaces from floor plan to add or remove from the zone" />
                        <Button
                            Grid.Row="1"
                            Grid.Column="0"
                            Width="50"
                            Margin="10,10,0,10"
                            HorizontalAlignment="Left"
                            Background="Transparent"
                            BorderThickness="0"
                            Command="{Binding AddSpacesToZoneCommand}"
                            Content="{materialDesign:PackIcon Kind=Plus,
                                                              Size=23}"
                            Foreground="#12A8B2"
                            ToolTip="Add spaces from Revit" />
                        <Button
                            Grid.Row="1"
                            Grid.Column="0"
                            Width="50"
                            Margin="60,10,0,10"
                            HorizontalAlignment="Left"
                            Background="Transparent"
                            BorderThickness="0"
                            Click="RemoveSpacesButton_Click"
                            Command="{Binding RemoveSpacesFromZoneCommand}"
                            Content="{materialDesign:PackIcon Kind=Close,
                                                              Size=20}"
                            Foreground="Red"
                            ToolTip="Remove selected spaces from this zone" />
                        <Button
                            Grid.Row="1"
                            Grid.Column="0"
                            Width="170"
                            Margin="0,0,15,0"
                            HorizontalAlignment="Right"
                            Background="#12A8B2"
                            BorderBrush="#12A8B2"
                            Click="UnassignedSpaces_Click"
                            Content="Unassigned Spaces"
                            Foreground="White"
                            ToolTip="List of all unassigned spaces" />

                        <!--  Space Calculations  -->
                        <DataGrid
                            x:Name="dataGrid_Spaces"
                            Grid.Row="2"
                            Margin="0,0,15,0"
                            AlternatingRowBackground="WhiteSmoke"
                            AutoGenerateColumns="False"
                            BorderBrush="LightGray"
                            BorderThickness="1"
                            CanUserAddRows="False"
                            FrozenColumnCount="4"
                            GridLinesVisibility="All"
                            ItemsSource="{Binding SelectedZone.Spaces}"
                            RowDetailsVisibilityMode="Visible"
                            RowHeight="28"
                            ScrollViewer.CanContentScroll="False"
                            ScrollViewer.HorizontalScrollBarVisibility="Auto"
                            ScrollViewer.VerticalScrollBarVisibility="Auto"
                            SelectionChanged="dataGrid_Spaces_SelectionChanged"
                            SelectionMode="Extended"
                            SelectionUnit="FullRow">

                            <!--  Remove unnecessary paddings and gaps  -->
                            <DataGrid.RowStyle>
                                <Style TargetType="DataGridRow">
                                    <Setter Property="Padding" Value="0" />
                                    <Setter Property="Margin" Value="0" />
                                </Style>
                            </DataGrid.RowStyle>
                            <DataGrid.CellStyle>
                                <Style TargetType="DataGridCell">
                                    <Setter Property="Padding" Value="0" />
                                    <Setter Property="Margin" Value="0" />
                                </Style>
                            </DataGrid.CellStyle>
                            <DataGrid.Resources>
                                <Style TargetType="{x:Type DataGridRow}">
                                    <Style.Triggers>
                                        <!--  Trigger for when the row is selected  -->
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="AliceBlue" />
                                        </Trigger>
                                        <!--  Optional: Trigger for mouse hover effect  -->
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="LightGray" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.Resources>


                            <DataGrid.Columns>
                                <!--  Space Name Column with Grouped Header  -->
                                <DataGridTemplateColumn Width="200" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Space Name&#x0a;" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.ZoneName, RelativeSource={RelativeSource AncestorType=Page}}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                SnapsToDevicePixels="True"
                                                Text="{Binding SpaceName}"
                                                TextOptions.TextFormattingMode="Display"
                                                TextOptions.TextRenderingMode="ClearType"
                                                UseLayoutRounding="True" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Associated Level  -->
                                <DataGridTemplateColumn Width="200" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Associated&#x0a;Level" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                SnapsToDevicePixels="True"
                                                Text="{Binding LevelName}"
                                                TextOptions.TextFormattingMode="Display"
                                                TextOptions.TextRenderingMode="ClearType"
                                                UseLayoutRounding="True" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Area  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Area&#x0a;" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( m² )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.TotalArea, RelativeSource={RelativeSource AncestorType=Page}}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                SnapsToDevicePixels="True"
                                                Text="{Binding Area, Converter={StaticResource RoundingConverter}, ConverterParameter=1}"
                                                TextOptions.TextFormattingMode="Display"
                                                TextOptions.TextRenderingMode="ClearType"
                                                UseLayoutRounding="True" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Add Remove Spatial Equipments  -->
                                <DataGridTemplateColumn Width="115" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Add/Remove&#x0a;Equipment" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="LightSlateGray"
                                                    Text="" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button
                                                Margin="0,0,0,0"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Command="{Binding DataContext.AddSpatialEquipmentCommand, RelativeSource={RelativeSource AncestorType=Page}}"
                                                Content="{materialDesign:PackIcon Kind=Plus,
                                                                                  Size=18}"
                                                Foreground="Green" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Spatial Function  -->
                                <DataGridTemplateColumn Width="200" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Spatial Function&#x0a;" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    Text="Selection"
                                                    TextAlignment="Center" />
                                                <TextBlock HorizontalAlignment="Right" Text="" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                SnapsToDevicePixels="True"
                                                Text="{Binding SpatialFunction}"
                                                TextOptions.TextFormattingMode="Display"
                                                TextOptions.TextRenderingMode="ClearType"
                                                UseLayoutRounding="True" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Small Power Load Density  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Text="Small PowerLoad&#x0a;Density"
                                                    TextAlignment="Center" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( VA/m² )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="LightSlateGray"
                                                    Text="{Binding DataContext.SelectedZone.SmallPowerLoadDensity, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=0}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Foreground="DarkGray"
                                                SnapsToDevicePixels="True"
                                                Text="{Binding SmallPowerLoadDensity}"
                                                TextOptions.TextFormattingMode="Display"
                                                TextOptions.TextRenderingMode="ClearType"
                                                UseLayoutRounding="True" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Lighting Load Density  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Text="Lighting Load&#x0a;Density"
                                                    TextAlignment="Center" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( VA/m² )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="LightSlateGray"
                                                    Text="{Binding DataContext.SelectedZone.LightingLoadDensity, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=0}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Foreground="DarkGray"
                                                SnapsToDevicePixels="True"
                                                Text="{Binding LightingLoadDensity}"
                                                TextOptions.TextFormattingMode="Display"
                                                TextOptions.TextRenderingMode="ClearType"
                                                UseLayoutRounding="True" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Mech Load Density  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Mech Load&#x0a;Density" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( VA/m² )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="LightSlateGray"
                                                    Text="{Binding DataContext.SelectedZone.MechLoadDensity, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=0}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Foreground="DarkGray"
                                                SnapsToDevicePixels="True"
                                                Text="{Binding AdjustedMechLoadDensity}"
                                                TextOptions.TextFormattingMode="Display"
                                                TextOptions.TextRenderingMode="ClearType"
                                                UseLayoutRounding="True" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Single Phase Lump Load Input  -->
                                <DataGridTemplateColumn Width="100">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Single Phase&#x0a;LumpLoad" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="LightSlateGray"
                                                    Text="{Binding DataContext.SelectedZone.SinglePhaseLumpLoadSum, RelativeSource={RelativeSource AncestorType=Page}}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox
                                                Width="60"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                IsReadOnly="{Binding HasEquipments}"
                                                SnapsToDevicePixels="True"
                                                Text="{Binding SinglePhaseLumpLoad, UpdateSourceTrigger=PropertyChanged}"
                                                TextAlignment="Center"
                                                TextOptions.TextFormattingMode="Display"
                                                TextOptions.TextRenderingMode="ClearType"
                                                UseLayoutRounding="True" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Three Phase Lump Load Input  -->
                                <DataGridTemplateColumn Width="100">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Three Phase&#x0a;LumpLoad" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="LightSlateGray"
                                                    Text="{Binding DataContext.SelectedZone.ThreePhaseLumpLoadSum, RelativeSource={RelativeSource AncestorType=Page}}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox
                                                Width="60"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                IsReadOnly="{Binding HasEquipments}"
                                                SnapsToDevicePixels="True"
                                                Text="{Binding ThreePhaseLumpLoad, UpdateSourceTrigger=PropertyChanged}"
                                                TextAlignment="Center"
                                                TextOptions.TextFormattingMode="Display"
                                                TextOptions.TextRenderingMode="ClearType"
                                                UseLayoutRounding="True" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Lump Load Diversity  -->
                                <DataGridTemplateColumn Width="100">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Lump Load&#x0a;Diversity" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( % )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.LumpLoadDiversitySum, RelativeSource={RelativeSource AncestorType=Page}}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox
                                                Width="60"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                IsReadOnly="{Binding HasEquipments}"
                                                SnapsToDevicePixels="True"
                                                TextAlignment="Center"
                                                TextOptions.TextFormattingMode="Display"
                                                TextOptions.TextRenderingMode="ClearType"
                                                UseLayoutRounding="True">
                                                <TextBox.Text>
                                                    <Binding
                                                        Converter="{StaticResource PercentageConverter}"
                                                        Path="LumpLoadDiversity"
                                                        UpdateSourceTrigger="LostFocus"
                                                        ValidatesOnDataErrors="True">
                                                        <Binding.ValidationRules>
                                                            <validations:PercentageRangeValidationRule />
                                                        </Binding.ValidationRules>
                                                    </Binding>
                                                </TextBox.Text>
                                            </TextBox>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Lump Load Power Factor  -->
                                <DataGridTemplateColumn Width="100">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Lump Load&#x0a;Power Factor" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.LumpLoadPowerFactorSum, RelativeSource={RelativeSource AncestorType=Page}}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox
                                                Width="60"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                IsReadOnly="{Binding HasEquipments}"
                                                SnapsToDevicePixels="True"
                                                TextAlignment="Center"
                                                TextOptions.TextFormattingMode="Display"
                                                TextOptions.TextRenderingMode="ClearType"
                                                UseLayoutRounding="True">
                                                <TextBox.Text>
                                                    <Binding
                                                        Path="LumpLoadPowerFactor"
                                                        UpdateSourceTrigger="LostFocus"
                                                        ValidatesOnDataErrors="True">
                                                        <Binding.ValidationRules>
                                                            <validations:PowerFactorValidationRule />
                                                        </Binding.ValidationRules>
                                                    </Binding>
                                                </TextBox.Text>
                                            </TextBox>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Small Power Load kVA  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Small Power&#x0a;Load" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="LightSlateGray"
                                                    Text="{Binding DataContext.SelectedZone.SmallPowerLoadSum, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding SmallPowerLoad, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Lighting Load kVA  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Lighting Load&#x0a;" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="LightSlateGray"
                                                    Text="{Binding DataContext.SelectedZone.LightingLoadSum, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding LightingLoad, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Mech Load kVA  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Mech Load&#x0a;" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="LightSlateGray"
                                                    Text="{Binding DataContext.SelectedZone.MechLoadSum, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding MechLoad, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Lump Load kVA  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Lump Load&#x0a;" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="LightSlateGray"
                                                    Text="{Binding DataContext.SelectedZone.LumpLoadSum, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding LumpLoad, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Calculated Diversity  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Calculated&#x0a;Diversity" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( % )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="Gold"
                                                    Text="{Binding DataContext.SelectedZone.CalculatedDiversity, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource PercentageConverter}}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding DataContext.SelectedZone.CalculatedDiversity, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource PercentageConverter}}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Diversified Load  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Diversified&#x0a;Load" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="Gold"
                                                    Text="{Binding DataContext.SelectedZone.DiversifiedLoad_kVA, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding DiversifiedLoad_kVA, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Calculated Power Factor  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Calculated&#x0a;Power Factor" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="Gold"
                                                    Text="{Binding DataContext.SelectedZone.CalculatedPowerFactor, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding CalculatedPowerFactor, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Diversified Load kW  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Diversified&#x0a;Load" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kW )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="Gold"
                                                    Text="{Binding DataContext.SelectedZone.DiversifiedLoad_kW, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding DiversifiedLoad_kW, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Diversified Current  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Diversified&#x0a;Current" />
                                                <TextBlock
                                                    Margin="0,5,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( A )" />
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    Foreground="Gold"
                                                    Text="{Binding DataContext.SelectedZone.DiversifiedCurrent_A, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=0}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding DiversifiedCurrent_A, Converter={StaticResource RoundingConverter}, ConverterParameter=0}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Power Load Density Sum Product  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Power Load Density&#x0a;Sum Product" />
                                                <TextBlock Margin="0,5,0,10" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding PowerLoadDensitySumProduct, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Lighting Load Density Sum Product  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Lighting Load Density&#x0a;Sum Product" />
                                                <TextBlock Margin="0,5,0,10" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding LightingLoadDensitySumProduct, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Mech Density Sum Product  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Mech Density&#x0a;Sum Product" />
                                                <TextBlock Margin="0,5,0,10" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding MechDensitySumProduct, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Lump Load Diversity Sum Product  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Lump Load Diversity&#x0a;Sum Product" />
                                                <TextBlock Margin="0,5,0,10" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding LumpLoadDiversitySumProduct, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Zone Power Factor Sum Product  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Zone Power Factor&#x0a;Sum Product" />
                                                <TextBlock Margin="0,5,0,10" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding ZonePowerFactorSumProduct, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Zone Diversity Sum Product  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Zone Diversity&#x0a;Sum Product" />
                                                <TextBlock Margin="0,5,0,10" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="DarkGray"
                                                Text="{Binding ZoneDiversitySumProduct, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Notes  -->
                                <DataGridTemplateColumn Width="200" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Notes" />
                                                <TextBlock Margin="0,5,0,10" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox
                                                MaxWidth="300"
                                                HorizontalAlignment="Left"
                                                VerticalAlignment="Stretch"
                                                materialDesign:HintAssist.Hint="Add notes here"
                                                Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                                TextWrapping="Wrap" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                            </DataGrid.Columns>

                            <!--  RowDetailsTemplate (nested rows) for displaying the equipment list  -->
                            <DataGrid.RowDetailsTemplate>
                                <DataTemplate>
                                    <!--  Nested DataGrid for the Spatial Equipments  -->
                                    <DataGrid
                                        Margin="50,5,10,10"
                                        AlternatingRowBackground="WhiteSmoke"
                                        AutoGenerateColumns="False"
                                        BorderBrush="LightGray"
                                        BorderThickness="1"
                                        CanUserAddRows="False"
                                        CanUserDeleteRows="False"
                                        GridLinesVisibility="None"
                                        HeadersVisibility="Column"
                                        ItemsSource="{Binding SpatialEquipments}"
                                        RowHeight="28">
                                        <DataGrid.Style>
                                            <Style TargetType="DataGrid">
                                                <Setter Property="Visibility" Value="Visible" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding SpatialEquipments.Count}" Value="0">
                                                        <Setter Property="Visibility" Value="Collapsed" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding SpatialEquipments}" Value="{x:Null}">
                                                        <Setter Property="Visibility" Value="Collapsed" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </DataGrid.Style>
                                        <!--  Remove unnecessary paddings and gaps  -->
                                        <DataGrid.RowStyle>
                                            <Style TargetType="DataGridRow">
                                                <Setter Property="Padding" Value="0" />
                                                <Setter Property="Margin" Value="0" />
                                            </Style>
                                        </DataGrid.RowStyle>
                                        <DataGrid.CellStyle>
                                            <Style TargetType="DataGridCell">
                                                <Setter Property="Padding" Value="0" />
                                                <Setter Property="Margin" Value="0" />
                                            </Style>
                                        </DataGrid.CellStyle>

                                        <DataGrid.Columns>
                                            <!--  Equipment Name  -->
                                            <DataGridTemplateColumn Header="Equipment Name&#x0a;&#x0a;">
                                                <DataGridTemplateColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBox
                                                            Width="60"
                                                            HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            SnapsToDevicePixels="True"
                                                            Text="{Binding EquipmentName, UpdateSourceTrigger=PropertyChanged}"
                                                            TextAlignment="Center"
                                                            TextOptions.TextFormattingMode="Display"
                                                            TextOptions.TextRenderingMode="ClearType"
                                                            UseLayoutRounding="True" />
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellTemplate>
                                            </DataGridTemplateColumn>
                                            <!--  Single Phase Lump Load  -->
                                            <DataGridTemplateColumn Header="Single Phase&#x0a;Lump Load&#x0a;(kVA)">
                                                <DataGridTemplateColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBox
                                                            Width="60"
                                                            HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            SnapsToDevicePixels="True"
                                                            Text="{Binding SinglePhaseLumpLoad, UpdateSourceTrigger=PropertyChanged}"
                                                            TextAlignment="Center"
                                                            TextOptions.TextFormattingMode="Display"
                                                            TextOptions.TextRenderingMode="ClearType"
                                                            UseLayoutRounding="True" />
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellTemplate>
                                            </DataGridTemplateColumn>
                                            <!--  Three Phase Lump Load  -->
                                            <DataGridTemplateColumn Header="Three Phase&#x0a;Lump Load&#x0a;(kVA)">
                                                <DataGridTemplateColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBox
                                                            Width="60"
                                                            HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            SnapsToDevicePixels="True"
                                                            Text="{Binding ThreePhaseLumpLoad, UpdateSourceTrigger=PropertyChanged}"
                                                            TextAlignment="Center"
                                                            TextOptions.TextFormattingMode="Display"
                                                            TextOptions.TextRenderingMode="ClearType"
                                                            UseLayoutRounding="True" />
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellTemplate>
                                            </DataGridTemplateColumn>
                                            <!--  Lump Load Power Factor  -->
                                            <DataGridTemplateColumn Header="Lump Load&#x0a;Power Factor&#x0a;">
                                                <DataGridTemplateColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBox
                                                            Width="60"
                                                            HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            SnapsToDevicePixels="True"
                                                            TextAlignment="Center"
                                                            TextOptions.TextFormattingMode="Display"
                                                            TextOptions.TextRenderingMode="ClearType"
                                                            UseLayoutRounding="True">
                                                            <TextBox.Text>
                                                                <Binding
                                                                    Path="LumpLoadPowerFactor"
                                                                    UpdateSourceTrigger="LostFocus"
                                                                    ValidatesOnDataErrors="True">
                                                                    <Binding.ValidationRules>
                                                                        <validations:PowerFactorValidationRule />
                                                                    </Binding.ValidationRules>
                                                                </Binding>
                                                            </TextBox.Text>
                                                        </TextBox>
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellTemplate>
                                            </DataGridTemplateColumn>
                                            <!--  Lump Load Diversity  -->
                                            <DataGridTemplateColumn Header="Lump Load&#x0a;Diversity&#x0a;(%)">
                                                <DataGridTemplateColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBox
                                                            Width="60"
                                                            HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            SnapsToDevicePixels="True"
                                                            TextAlignment="Center"
                                                            TextOptions.TextFormattingMode="Display"
                                                            TextOptions.TextRenderingMode="ClearType"
                                                            UseLayoutRounding="True">
                                                            <TextBox.Text>
                                                                <Binding
                                                                    Converter="{StaticResource PercentageConverter}"
                                                                    Path="LumpLoadDiversity"
                                                                    UpdateSourceTrigger="LostFocus"
                                                                    ValidatesOnDataErrors="True">
                                                                    <Binding.ValidationRules>
                                                                        <validations:PercentageRangeValidationRule />
                                                                    </Binding.ValidationRules>
                                                                </Binding>
                                                            </TextBox.Text>
                                                        </TextBox>
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellTemplate>
                                            </DataGridTemplateColumn>
                                            <!--  Remove  -->
                                            <DataGridTemplateColumn Header="Remove&#x0a;&#x0a;">
                                                <DataGridTemplateColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <Button
                                                            Background="Transparent"
                                                            BorderThickness="0"
                                                            Command="{Binding DataContext.DeleteSpatialEquipmentCommand, RelativeSource={RelativeSource AncestorType=Page}}"
                                                            CommandParameter="{Binding}"
                                                            Content="{materialDesign:PackIcon Kind=Close,
                                                                                              Size=18}"
                                                            Foreground="Red" />
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellTemplate>
                                            </DataGridTemplateColumn>
                                        </DataGrid.Columns>
                                    </DataGrid>
                                </DataTemplate>
                            </DataGrid.RowDetailsTemplate>

                        </DataGrid>

                        <!--  Spatial Function - PD Lookup Data  -->
                        <Expander
                            Grid.Row="2"
                            Grid.Column="1"
                            Margin="10,0,0,0"
                            BorderBrush="#12A8B2"
                            BorderThickness="2"
                            ExpandDirection="Right">
                            <Expander.Header>
                                <Border>
                                    <TextBlock
                                        FontSize="15"
                                        RenderTransformOrigin=".5,.5"
                                        Text="Assign Spatial Function">
                                        <TextBlock.LayoutTransform>
                                            <RotateTransform Angle="90" />
                                        </TextBlock.LayoutTransform>
                                    </TextBlock>
                                </Border>
                            </Expander.Header>
                            <ScrollViewer
                                Grid.Row="3"
                                Grid.Column="1"
                                Margin="0,0,10,10"
                                HorizontalScrollBarVisibility="Auto">
                                <ListBox
                                    BorderBrush="LightGray"
                                    BorderThickness="1"
                                    DisplayMemberPath="SpatialFunction"
                                    ItemsSource="{Binding PDLookupData}"
                                    PreviewMouseLeftButtonDown="ListBox_PreviewMouseLeftButtonDown"
                                    SelectedItem="{Binding SelectedPDLoadRow, Mode=TwoWay}"
                                    SelectionMode="Single" />
                            </ScrollViewer>
                        </Expander>
                    </Grid>
                </TabItem>

                <!--  DB Input Tab  -->
                <TabItem Background="#12A8B2">
                    <TabItem.Header>
                        <TextBlock
                            FontSize="18"
                            FontWeight="DemiBold"
                            Foreground="#12A8B2"
                            Text="DB Input" />
                    </TabItem.Header>
                    <DataGrid
                        Margin="10,20,10,10"
                        AlternatingRowBackground="WhiteSmoke"
                        AutoGenerateColumns="False"
                        BorderBrush="LightGray"
                        BorderThickness="1"
                        CanUserAddRows="False"
                        FrozenColumnCount="6"
                        GridLinesVisibility="All"
                        ItemsSource="{Binding SelectedZone.DBs}"
                        ScrollViewer.HorizontalScrollBarVisibility="Auto"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        SelectionMode="Extended"
                        SelectionUnit="FullRow">
                        <DataGrid.Columns>

                            <!--  DB Name  -->
                            <DataGridTemplateColumn Width="150" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Distribution Board Name" />
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                HorizontalAlignment="Left"
                                                Text="" />
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.ZoneName, RelativeSource={RelativeSource AncestorType=Page}}" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding DBName}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Power Supply Class  -->
                            <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="General/Essential/UPS" />
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                HorizontalAlignment="Center"
                                                Text="G/E/U" />
                                            <TextBlock Text="" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <ComboBox
                                            Background="Azure"
                                            ItemsSource="{Binding PowerSupplyOptions}"
                                            SelectedItem="{Binding PowerSupplyClass, UpdateSourceTrigger=PropertyChanged}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Is Small Power Checkbox  -->
                            <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Small Power" />
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                HorizontalAlignment="Left"
                                                Text="" />
                                            <TextBlock Text="" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <CheckBox
                                            HorizontalAlignment="Center"
                                            materialDesign:CheckBoxAssist.CheckBoxSize="30"
                                            IsChecked="{Binding IsSmallPower, UpdateSourceTrigger=PropertyChanged}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Is Lighting Power Checkbox  -->
                            <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Lighting" />
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                HorizontalAlignment="Left"
                                                Text="" />
                                            <TextBlock Text="" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <CheckBox
                                            HorizontalAlignment="Center"
                                            materialDesign:CheckBoxAssist.CheckBoxSize="30"
                                            IsChecked="{Binding IsLighting, UpdateSourceTrigger=PropertyChanged}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Is Mech Power Checkbox  -->
                            <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Mech" />
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                HorizontalAlignment="Left"
                                                Text="" />
                                            <TextBlock Text="" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <CheckBox
                                            HorizontalAlignment="Center"
                                            materialDesign:CheckBoxAssist.CheckBoxSize="30"
                                            IsChecked="{Binding IsMech, UpdateSourceTrigger=PropertyChanged}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Is Lump Power Checkbox  -->
                            <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Lump" />
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                HorizontalAlignment="Left"
                                                Text="" />
                                            <TextBlock Text="" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <CheckBox
                                            HorizontalAlignment="Center"
                                            materialDesign:CheckBoxAssist.CheckBoxSize="30"
                                            IsChecked="{Binding IsLump, UpdateSourceTrigger=PropertyChanged}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Power Factor  -->
                            <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Power Factor" />
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                HorizontalAlignment="Left"
                                                Text="" />
                                            <TextBlock Text="" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            Foreground="Gold"
                                            Text="{Binding PowerFactor, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Diversified Per Phase Current  -->
                            <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Diversified Per Phase Current" />
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                HorizontalAlignment="Center"
                                                Text="( A )" />
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Foreground="Gold"
                                                Text="{Binding DataContext.SelectedZone.DiversifiedPerPhaseCurrent, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            Foreground="Gold"
                                            Text="{Binding DiversifiedPerPhaseCurrent, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  DB Cable Length  -->
                            <DataGridTemplateColumn Width="100" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="DB Cable Length" />
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                HorizontalAlignment="Center"
                                                Text="" />
                                            <TextBlock Text="" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock HorizontalAlignment="Center" Text="{Binding DBCableLength, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Parent DB  -->
                            <DataGridTemplateColumn Width="150" IsReadOnly="True">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Parent DB" />
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                HorizontalAlignment="Center"
                                                Text="" />
                                            <TextBlock Text="" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                            <!--  Show ParentDB if it exists  -->
                                            <TextBlock Text="{Binding ParentDBName}" Visibility="{Binding ParentDB, Converter={StaticResource NullToVisibilityConverter}}" />

                                            <!--  Show Icon with Tooltip when ParentDB is null  -->
                                            <TextBlock
                                                FontSize="16"
                                                Foreground="Orange"
                                                Text="⚠"
                                                Visibility="{Binding ParentDB, Converter={StaticResource NullToInverseVisibilityConverter}}">
                                                <TextBlock.ToolTip>
                                                    <ToolTip Content="No parent DB found. Please set one up in Revit." />
                                                </TextBlock.ToolTip>
                                            </TextBlock>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Notes  -->
                            <DataGridTemplateColumn Width="200" IsReadOnly="False">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock HorizontalAlignment="Center" Text="Notes" />
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                HorizontalAlignment="Center"
                                                Text="" />
                                            <TextBlock Text="" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBox
                                            MaxWidth="300"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Stretch"
                                            materialDesign:HintAssist.Hint="Add notes here"
                                            Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                            TextWrapping="Wrap" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </TabItem>

                <!--  DB Zone Input (Diversified Load Values)  -->
                <TabItem Background="#12A8B2">
                    <TabItem.Header>
                        <TextBlock
                            FontSize="18"
                            FontWeight="DemiBold"
                            Foreground="#12A8B2"
                            Text="DB Zone Input" />
                    </TabItem.Header>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!--  HVAC Topology  THIS IS HIDDEN CHECK WITH THE CALC WHEN COMPLETELY REMOVE from 04/08/2025 dev updated to store DBZoneHVAC  -->
                        <!--<materialDesign:Card Margin="0,20,0,10" Visibility="Hidden">
                            <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                <TextBlock
                                    Margin="10,10,5,0"
                                    FontSize="17"
                                    FontWeight="SemiBold"
                                    Text="HVAC Topology:" />
                                <ComboBox
                                    Width="150"
                                    Margin="5,10,0,5"
                                    Background="Azure"
                                    FontSize="15"
                                    ItemsSource="{Binding HVACTopologyOptions}"
                                    SelectedItem="{Binding SelectedHVACTopology, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                <TextBlock
                                    Margin="30,10,5,0"
                                    FontSize="17"
                                    FontWeight="SemiBold"
                                    Text="HVAC Topology Load Factor :" />
                                <TextBlock
                                    Margin="0,10,10,0"
                                    FontSize="17"
                                    FontWeight="Regular"
                                    Text="{Binding SelectedZone.HVACTopologyLoadFactor}" />
                            </StackPanel>
                        </materialDesign:Card>-->


                        <!--  DB Zone Input Essential  -->
                        <StackPanel
                            Grid.Row="1"
                            HorizontalAlignment="Center"
                            Orientation="Horizontal">
                            <materialDesign:Card Margin="0,25,5,0">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition />
                                        <RowDefinition />
                                        <RowDefinition />
                                        <RowDefinition />
                                    </Grid.RowDefinitions>

                                    <!--  Essential Load Header  -->
                                    <TextBlock
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="4"
                                        Margin="0,0,0,10"
                                        HorizontalAlignment="Center"
                                        FontWeight="SemiBold"
                                        Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                        Text="Essential Load" />
                                    <!--  Essential Load Small Power  -->
                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        Margin="12"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="Small Power" />
                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="0"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="%" />
                                    <TextBox
                                        Grid.Row="3"
                                        Grid.Column="0"
                                        Width="60"
                                        Margin=".5,0,.5,0"
                                        HorizontalAlignment="Center"
                                        Text="{Binding SelectedZone.EssentialSmallPowerLoadPercentage}"
                                        TextAlignment="Center" />
                                    <!--  Essential Load Lighting  -->
                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        Margin="12"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="Lighting" />
                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="1"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="%" />
                                    <TextBox
                                        Grid.Row="3"
                                        Grid.Column="1"
                                        Width="60"
                                        Margin=".5,0,.5,0"
                                        HorizontalAlignment="Center"
                                        Text="{Binding SelectedZone.EssentialLightingLoadPercentage}"
                                        TextAlignment="Center" />
                                    <!--  Essential Load Mechanical  -->
                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="2"
                                        Margin="12"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="Mechanical" />
                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="2"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="%" />
                                    <TextBox
                                        Grid.Row="3"
                                        Grid.Column="2"
                                        Width="60"
                                        Margin=".5,0,.5,0"
                                        HorizontalAlignment="Center"
                                        Text="{Binding SelectedZone.EssentialMechanicalLoadPercentage}"
                                        TextAlignment="Center" />
                                    <!--  Essential Load Lump  -->
                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="3"
                                        Margin="12"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="Lump" />
                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="3"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="%" />
                                    <TextBox
                                        Grid.Row="3"
                                        Grid.Column="3"
                                        Width="60"
                                        Margin=".5,0,.5,0"
                                        HorizontalAlignment="Center"
                                        Text="{Binding SelectedZone.EssentialLumpLoadPercentage}"
                                        TextAlignment="Center" />
                                </Grid>
                            </materialDesign:Card>

                            <!--  DB Zone Input Uninteruptable  -->
                            <materialDesign:Card Margin="5,25,0,0">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition />
                                        <RowDefinition />
                                        <RowDefinition />
                                        <RowDefinition />
                                    </Grid.RowDefinitions>

                                    <!--  Uninteruptable Load Header  -->
                                    <TextBlock
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="4"
                                        Margin="0,0,0,10"
                                        HorizontalAlignment="Center"
                                        FontSize="17"
                                        FontWeight="SemiBold"
                                        Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                        Text="Uninteruptable Load" />
                                    <!--  Uninteruptable Load Small Power  -->
                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        Margin="12"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="Small Power" />
                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="0"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="%" />
                                    <TextBox
                                        Grid.Row="3"
                                        Grid.Column="0"
                                        Width="60"
                                        Margin=".5,0,.5,0"
                                        HorizontalAlignment="Center"
                                        Text="{Binding SelectedZone.UninteruptableSmallPowerLoadPercentage}"
                                        TextAlignment="Center" />
                                    <!--  Uninteruptable Load Lighting  -->
                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        Margin="12"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="Lighting" />
                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="1"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="%" />
                                    <TextBox
                                        Grid.Row="3"
                                        Grid.Column="1"
                                        Width="60"
                                        Margin=".5,0,.5,0"
                                        HorizontalAlignment="Center"
                                        Text="{Binding SelectedZone.UninteruptableLightingLoadPercentage}"
                                        TextAlignment="Center" />
                                    <!--  Uninteruptable Load Mechanical  -->
                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="2"
                                        Margin="12"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="Mechanical" />
                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="2"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="%" />
                                    <TextBox
                                        Grid.Row="3"
                                        Grid.Column="2"
                                        Width="60"
                                        Margin=".5,0,.5,0"
                                        HorizontalAlignment="Center"
                                        Text="{Binding SelectedZone.UninteruptableMechanicalLoadPercentage}"
                                        TextAlignment="Center" />
                                    <!--  Uninteruptable Load Lump  -->
                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="3"
                                        Margin="12"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="Lump" />
                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="3"
                                        HorizontalAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Regular"
                                        Text="%" />
                                    <TextBox
                                        Grid.Row="3"
                                        Grid.Column="3"
                                        Width="60"
                                        Margin=".5,0,.5,0"
                                        HorizontalAlignment="Center"
                                        Text="{Binding SelectedZone.UninteruptableLumpLoadPercentage}"
                                        TextAlignment="Center" />
                                </Grid>
                            </materialDesign:Card>
                        </StackPanel>

                        <TextBlock
                            Grid.Row="2"
                            Grid.Column="0"
                            Margin="10,40,0,5"
                            HorizontalAlignment="Center"
                            FontWeight="SemiBold"
                            Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                            Text="Diversified Load Values" />

                        <!--  Diversified Load Values  -->
                        <DataGrid
                            Grid.Row="3"
                            Margin="5"
                            AlternatingRowBackground="WhiteSmoke"
                            AutoGenerateColumns="False"
                            BorderBrush="LightGray"
                            BorderThickness="1"
                            CanUserAddRows="False"
                            FrozenColumnCount="1"
                            GridLinesVisibility="All"
                            ItemsSource="{Binding SelectedZone.DBs}"
                            ScrollViewer.HorizontalScrollBarVisibility="Auto"
                            ScrollViewer.VerticalScrollBarVisibility="Auto"
                            SelectionMode="Extended"
                            SelectionUnit="FullRow">
                            <DataGrid.Columns>
                                <!--  DB Name Column with Grouped Header  -->
                                <DataGridTemplateColumn Width="150" IsReadOnly="True">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Zone Name" />
                                                <TextBlock HorizontalAlignment="Center" Text="Distribution Board Name" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Left"
                                                    Text="" />
                                                <TextBlock HorizontalAlignment="Right" Text="Count :" />
                                                <TextBlock HorizontalAlignment="Right" Text="" />
                                                <TextBlock HorizontalAlignment="Right" Text="{Binding DataContext.SelectedZone.ZoneName, RelativeSource={RelativeSource AncestorType=Page}}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding DBName}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  General Small PowerLoad  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="General" />
                                                <TextBlock HorizontalAlignment="Center" Text="Small Power Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.GeneralSmallPowerLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.GeneralSmallPowerLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding GeneralSmallPowerLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  General Lighting Load  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="General" />
                                                <TextBlock HorizontalAlignment="Center" Text="Lighting Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.GeneralLightingLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.GeneralLightingLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding GeneralLightingLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  General Mech Load  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="General" />
                                                <TextBlock HorizontalAlignment="Center" Text="Mech Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.GeneralMechanicalLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.GeneralMechanicalLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding GeneralMechanicalLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  General Lump Load  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="General" />
                                                <TextBlock HorizontalAlignment="Center" Text="Lump Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.GeneralLumpLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.GeneralLumpLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding GeneralLumpLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Essential Small PowerLoad  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Essential" />
                                                <TextBlock HorizontalAlignment="Center" Text="Small Power Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.EssentialSmallPowerLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.EssentialSmallPowerLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding EssentialSmallPowerLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Essential Lighting Load  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Essential" />
                                                <TextBlock HorizontalAlignment="Center" Text="Lighting Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.EssentialLightingLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.EssentialLightingLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding EssentialLightingLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Essential Mech Load  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Essential" />
                                                <TextBlock HorizontalAlignment="Center" Text="Mech Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.EssentialMechanicalLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.EssentialMechanicalLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding EssentialMechanicalLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Essential Lump Load  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Essential" />
                                                <TextBlock HorizontalAlignment="Center" Text="Lump Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.EssentialLumpLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.EssentialLumpLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding EssentialLumpLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Uninteruptable Small PowerLoad  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Uninteruptable" />
                                                <TextBlock HorizontalAlignment="Center" Text="Small Power Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.UninteruptableSmallPowerLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.UninteruptableSmallPowerLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding UninteruptableSmallPowerLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Uninteruptable Lighting Load  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Uninteruptable" />
                                                <TextBlock HorizontalAlignment="Center" Text="Lighting Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.UninteruptableLightingLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.UninteruptableLightingLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding UninteruptableLightingLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Uninteruptable Mech Load  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Uninteruptable" />
                                                <TextBlock HorizontalAlignment="Center" Text="Mech Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.UninteruptableMechanicalLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.UninteruptableMechanicalLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding UninteruptableMechanicalLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!--  Uninteruptable Lump Load  -->
                                <DataGridTemplateColumn Width="100" IsReadOnly="False">
                                    <DataGridTemplateColumn.HeaderTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock HorizontalAlignment="Center" Text="Uninteruptable" />
                                                <TextBlock HorizontalAlignment="Center" Text="Lump Load" />
                                                <TextBlock
                                                    Margin="0,0,0,10"
                                                    HorizontalAlignment="Center"
                                                    Text="( kVA )" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.UninteruptableLumpLoadCount, RelativeSource={RelativeSource AncestorType=Page}}" />
                                                <TextBlock HorizontalAlignment="Center" Text="" />
                                                <TextBlock HorizontalAlignment="Center" Text="{Binding DataContext.SelectedZone.UninteruptableLumpLoad, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.HeaderTemplate>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding UninteruptableLumpLoad, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource RoundingConverter}, ConverterParameter=2}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </TabItem>

                <!--  HVAC Input Tab  -->
                <TabItem Background="#12A8B2">
                    <TabItem.Header>
                        <TextBlock
                            FontSize="18"
                            FontWeight="DemiBold"
                            Foreground="#12A8B2"
                            Text="HVAC Input" />
                    </TabItem.Header>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width=".8*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width=".8*" />
                        </Grid.ColumnDefinitions>

                        <!--  HVAC Estimation Methodology  -->
                        <materialDesign:Card Margin="10,20,0,10">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <TextBlock
                                    Margin="10"
                                    HorizontalAlignment="Center"
                                    Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                    Text="HVAC Estimation Methodology" />
                                <ComboBox
                                    Grid.Row="1"
                                    Width="200"
                                    Margin="10"
                                    HorizontalAlignment="Left"
                                    AlternationCount="10"
                                    ItemsSource="{Binding HVACMethodologyDisplayNames.Values}"
                                    SelectedItem="{Binding SelectedItem1}">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding}" TextDecorations="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ComboBoxItem}, Path=(ItemsControl.AlternationIndex), Converter={StaticResource IndexToStrikethroughConverter}}" />
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>

                                <TextBlock
                                    Grid.Row="2"
                                    Margin="10,10,0,0"
                                    HorizontalAlignment="Left"
                                    FontWeight="SemiBold"
                                    Foreground="Red"
                                    Text="Engineering Info" />
                                <TextBlock
                                    Grid.Row="3"
                                    Margin="10"
                                    HorizontalAlignment="Left"
                                    FontWeight="SemiBold"
                                    Text="{Binding Card1TitleText}"
                                    TextDecorations="{Binding Card1TitleText, Converter={StaticResource StringToTextDecorationsConverter}}"
                                    TextWrapping="Wrap" />
                                <TextBlock
                                    Grid.Row="4"
                                    Margin="10"
                                    HorizontalAlignment="Left"
                                    Foreground="MediumPurple"
                                    Text="{Binding Card1ContentText}"
                                    TextWrapping="Wrap" />
                            </Grid>
                        </materialDesign:Card>

                        <!--  HVAC Topology  -->
                        <materialDesign:Card Grid.Column="1" Margin="10,20,10,10">
                            <Grid Margin="10,0,10,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width=".5*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <TextBlock
                                    Grid.ColumnSpan="2"
                                    Margin="10"
                                    HorizontalAlignment="Center"
                                    Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                    Text="HVAC Topology" />
                                <ComboBox
                                    Grid.Row="1"
                                    Grid.ColumnSpan="2"
                                    Width="200"
                                    Margin="10"
                                    HorizontalAlignment="Left"
                                    ItemsSource="{Binding HVACTopologyDisplayNames.Values}"
                                    SelectedItem="{Binding SelectedItem2}" />
                                <TextBlock
                                    Grid.Row="2"
                                    Grid.ColumnSpan="2"
                                    Margin="10,10,0,0"
                                    HorizontalAlignment="Left"
                                    FontWeight="SemiBold"
                                    Foreground="Red"
                                    Text="Engineering Info" />
                                <TextBlock
                                    Grid.Row="3"
                                    Grid.Column="0"
                                    Margin="10"
                                    FontWeight="SemiBold"
                                    Text="Local load:" />
                                <TextBlock
                                    Grid.Row="3"
                                    Grid.Column="1"
                                    Margin="10"
                                    HorizontalAlignment="Left"
                                    Foreground="MediumPurple"
                                    Text="{Binding Card2SideNoteText}"
                                    TextWrapping="Wrap" />
                                <TextBlock
                                    Grid.Row="4"
                                    Grid.Column="0"
                                    Margin="10"
                                    FontWeight="SemiBold"
                                    Text="Centralised load:" />
                                <TextBlock
                                    Grid.Row="4"
                                    Grid.Column="1"
                                    Margin="10"
                                    HorizontalAlignment="Left"
                                    Foreground="MediumPurple"
                                    Text="{Binding Card2CentralisedLoadText}"
                                    TextWrapping="Wrap" />
                                <TextBlock
                                    Grid.Row="5"
                                    Margin="10"
                                    FontWeight="SemiBold"
                                    Text="Key consideration:" />
                                <TextBlock
                                    Grid.Row="5"
                                    Grid.Column="1"
                                    Margin="10"
                                    HorizontalAlignment="Left"
                                    Foreground="MediumPurple"
                                    Text="{Binding Card2KeyConsiderationText}"
                                    TextWrapping="Wrap" />
                            </Grid>
                        </materialDesign:Card>

                        <!--  Numerical Input  -->
                        <Border
                            x:Name="card_3"
                            Grid.Column="2"
                            Margin="0,20,10,10"
                            Background="#2012A8B2"
                            Visibility="{Binding IsCard3Visible, Converter={StaticResource BoolToVisibilityConverter}}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <TextBlock
                                    Margin="10"
                                    HorizontalAlignment="Center"
                                    Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                    Text="Numerical Input" />

                                <!--  DB Zone HVAC Topology Proration Factor VA/m2  -->
                                <StackPanel
                                    Grid.Row="1"
                                    Orientation="Horizontal"
                                    Visibility="{Binding IsGrid3AVisible, Converter={StaticResource BoolToVisibilityConverter}}">
                                    <TextBlock
                                        Margin="10,0,10,0"
                                        Foreground="LightGray"
                                        IsEnabled="False"
                                        Text="DB Zone HVAC&#x0a;Topology Proration Factor" />
                                    <TextBox
                                        Width="30"
                                        VerticalAlignment="Bottom"
                                        IsEnabled="False"
                                        Text="{Binding SelectedZone.DBZoneHVAC, UpdateSourceTrigger=PropertyChanged}" />
                                    <TextBlock
                                        VerticalAlignment="Bottom"
                                        Foreground="LightGray"
                                        IsEnabled="False"
                                        Text="VA/m2" />
                                </StackPanel>

                                <!--  DB Zone HVAC %  -->
                                <StackPanel
                                    Grid.Row="1"
                                    Orientation="Horizontal"
                                    Visibility="{Binding IsGrid3BVisible, Converter={StaticResource BoolToVisibilityConverter}}">
                                    <TextBlock
                                        Margin="10,0,10,0"
                                        VerticalAlignment="Bottom"
                                        Text="DB Zone HVAC" />
                                    <TextBox
                                        Width="30"
                                        VerticalAlignment="Bottom"
                                        Text="{Binding SelectedZone.DBZoneHVAC, UpdateSourceTrigger=PropertyChanged}" />
                                    <TextBlock VerticalAlignment="Bottom" Text="VA/m2" />
                                </StackPanel>

                                <TextBlock
                                    Grid.Row="2"
                                    Margin="10,20,0,0"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Top"
                                    FontWeight="SemiBold"
                                    Foreground="Red"
                                    Text="Engineering Info" />
                                <TextBlock
                                    Grid.Row="3"
                                    Margin="10"
                                    Background="LightGray"
                                    FontStyle="Italic"
                                    Foreground="MediumVioletRed"
                                    Text="{Binding Card3ProrartionFactorText}"
                                    TextWrapping="Wrap" />
                                <TextBlock
                                    Grid.Row="4"
                                    Margin="10"
                                    Foreground="MediumPurple"
                                    Text="{Binding Card3ProrartionFactorDescriptionText}"
                                    TextWrapping="Wrap" />
                            </Grid>
                        </Border>

                    </Grid>
                </TabItem>
            </TabControl>
        </materialDesign:Card>

        <!--  Under Buttons  -->
        <StackPanel
            Grid.Row="2"
            Grid.ColumnSpan="2"
            HorizontalAlignment="Center"
            Orientation="Horizontal">
            <Button
                Width="100"
                Margin="0,20,0,20"
                HorizontalAlignment="Left"
                Background="#12A8B2"
                BorderBrush="#12A8B2"
                Click="BackToZones_Click"
                Content="Back"
                Foreground="White" />
            <Button
                x:Name="SaveButton"
                Width="100"
                Margin="20,20,0,20"
                HorizontalAlignment="Left"
                Background="#FFCE00"
                BorderBrush="#FFCE00"
                Command="{Binding SaveZoneEditPageCommand}"
                Content="Save"
                Foreground="Black" />
            <Popup
                x:Name="SuccessPopup"
                AllowsTransparency="True"
                HorizontalOffset="20"
                IsOpen="{Binding IsPopupVisible, Mode=TwoWay}"
                Placement="Right"
                PlacementTarget="{Binding ElementName=SaveButton}"
                StaysOpen="False">
                <Border
                    Width="180"
                    Height="40"
                    Padding="10"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Background="Gray"
                    BorderBrush="Gray"
                    BorderThickness="1"
                    CornerRadius="5">
                    <TextBlock
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="14"
                        Foreground="White"
                        Text="{Binding PopupMessage}"
                        TextWrapping="Wrap" />
                </Border>
            </Popup>
        </StackPanel>

        <!--  Footer  -->
        <TextBlock
            Grid.Row="3"
            Grid.Column="1"
            Margin="0,5,20,10"
            HorizontalAlignment="Right"
            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
            Text="Make Everyday Better" />
        <Image
            Grid.Row="3"
            Height="25"
            Margin="20,6,0,11"
            HorizontalAlignment="Left"
            Source="/MEP.MaxDemand;component/Resources/BecaLogoBlack.png" />
    </Grid>
</Page>
