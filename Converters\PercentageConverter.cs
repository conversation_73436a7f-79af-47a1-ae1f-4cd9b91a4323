﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace MEP.MaxDemand.Converters
{
    public class PercentageConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double doubleValue)
            {
                // Convert double to percentage string
                return Math.Round(doubleValue * 100).ToString() + "%";
                //return (doubleValue * 100).ToString() + "%";
            }
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue && stringValue.EndsWith("%"))
            {
                // Remove the '%' and convert back to double
                if (double.TryParse(stringValue.TrimEnd('%'), out double percentage))
                {
                    return percentage / 100;
                }
            }
            return value;
        }
    }
}
