﻿<Page
    x:Class="MEP.MaxDemand.UI.View.MD_UnassignedSpaces"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.MaxDemand.UI.View"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="MD_UnassignedSpaces"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="White"
    mc:Ignorable="d">

    <!--  Resources  -->
    <Page.Resources>
        <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
    </Page.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width=".3*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width=".3*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Border-->
        <Rectangle Fill="DimGray" Grid.Column="2" Grid.RowSpan="4"/>
        <Rectangle Fill="DimGray" Grid.RowSpan="4" />

        <!--  Header  -->
        <TextBlock
            Grid.Row="0"
            Grid.ColumnSpan="3"
            Margin="15,5,0,10"
            HorizontalAlignment="Center"
            Style="{StaticResource MaterialDesignHeadline4TextBlock}"
            Text="UNASSIGNED SPACES" />
        <Separator
            Grid.Column="1"
            Margin="0,40,0,0"
            Background="#FFCE00">
            <Separator.LayoutTransform>
                <ScaleTransform ScaleY="3.5" />
            </Separator.LayoutTransform>
        </Separator>

        <!--  Unassigned Spaces  -->
        <materialDesign:Card Grid.Row="1" Grid.Column="1" Margin="10,10,10,0">
            <DataGrid
                x:Name="UnassignedSpacesDataGrid"
                Margin="15"
                AlternatingRowBackground="WhiteSmoke"
                AutoGenerateColumns="False"
                BorderBrush="LightGray"
                BorderThickness="1"
                CanUserAddRows="False"
                GridLinesVisibility="All"
                ItemsSource="{Binding Data.OrphanedSpaces}"
                ScrollViewer.HorizontalScrollBarVisibility="Auto"
                ScrollViewer.VerticalScrollBarVisibility="Auto"
                SelectionMode="Extended"
                SelectionUnit="FullRow">
                <DataGrid.Columns>
                    <!--  Space ID  -->
                    <DataGridTemplateColumn Width="100" IsReadOnly="True">
                        <DataGridTemplateColumn.HeaderTemplate>
                            <DataTemplate>
                                <StackPanel>
                                    <TextBlock
                                        Margin="10"
                                        HorizontalAlignment="Center"
                                        Text="ID" />
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.HeaderTemplate>
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Id}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  Space Status  -->
                    <DataGridTemplateColumn Width="150" IsReadOnly="True">
                        <DataGridTemplateColumn.HeaderTemplate>
                            <DataTemplate>
                                <StackPanel>
                                    <TextBlock HorizontalAlignment="Center" Text="Space&#x0a;Status" />
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.HeaderTemplate>
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding SpaceStatus}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  Level  -->
                    <DataGridTemplateColumn Width="150" IsReadOnly="True">
                        <DataGridTemplateColumn.HeaderTemplate>
                            <DataTemplate>
                                <StackPanel>
                                    <TextBlock HorizontalAlignment="Center" Text="Level" />
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.HeaderTemplate>
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding LevelName}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  Space Number  -->
                    <DataGridTemplateColumn Width="100" IsReadOnly="True">
                        <DataGridTemplateColumn.HeaderTemplate>
                            <DataTemplate>
                                <StackPanel>
                                    <TextBlock HorizontalAlignment="Center" Text="Space&#x0a;Number" />
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.HeaderTemplate>
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding SpaceNumber}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  Space Name  -->
                    <DataGridTemplateColumn Width="200" IsReadOnly="True">
                        <DataGridTemplateColumn.HeaderTemplate>
                            <DataTemplate>
                                <StackPanel>
                                    <TextBlock HorizontalAlignment="Center" Text="Space&#x0a;Name" />
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.HeaderTemplate>
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding SpaceName}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  Area  -->
                    <DataGridTemplateColumn Width="100" IsReadOnly="True">
                        <DataGridTemplateColumn.HeaderTemplate>
                            <DataTemplate>
                                <StackPanel>
                                    <TextBlock HorizontalAlignment="Center" Text="Area" />
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.HeaderTemplate>
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Area}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                </DataGrid.Columns>

            </DataGrid>
        </materialDesign:Card>


        <!--  Under Button  -->
        <Button
            Grid.Row="2"
            Grid.ColumnSpan="3"
            Width="100"
            Margin="0,20,140,20"
            HorizontalAlignment="Center"
            Background="#12A8B2"
            BorderBrush="#12A8B2"
            Click="BackToZoneEdit_Click"
            Content="Back"
            Foreground="White" />
        <Button
            Grid.Row="2"
            Grid.ColumnSpan="3"
            Width="100"
            Margin="140,20,20,20"
            HorizontalAlignment="Center"
            Background="#FFCE00"
            BorderBrush="#FFCE00"
            Click="AddButton_Click"
            Command="{Binding AddUnsassignedSpacesCommand}"
            Content="Add"
            Foreground="Black" />

        <!--  Footer  -->
        <TextBlock
            Grid.Row="3"
            Grid.Column="1"
            Margin="0,0,10,10"
            HorizontalAlignment="Right"
            VerticalAlignment="Center"
            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
            Text="Make Everyday Better" />
        <TextBlock
            Grid.Row="3"
            Grid.Column="1"
            Margin="10,0,0,10"
            HorizontalAlignment="Left"
            VerticalAlignment="Center"
            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
            Text="Beca" />
    </Grid>
</Page>
