﻿<Page
    x:Class="MEP.MaxDemand.UI.View.MD_DataPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.MaxDemand.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.MaxDemand.UI.View"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:validations="clr-namespace:MEP.MaxDemand.Validations"
    Title="MD_DataPage"
    Width="Auto"
    Height="Auto"
    d:DesignHeight="450"
    Background="White"
    mc:Ignorable="d">

    <!--  Resources  -->
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <!--  Page-specific resources  -->
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
            <converters:PercentageConverter x:Key="PercentageConverter" />
            <converters:UnitConverter x:Key="UnitConverter" />
            <converters:RoundingConverter x:Key="RoundingConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="350" />
            <ColumnDefinition Width="250" />
            <ColumnDefinition Width="300" />
            <ColumnDefinition Width="320" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="50" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <TextBlock
            Grid.Row="0"
            Grid.ColumnSpan="5"
            Margin="15,5,0,10"
            Style="{StaticResource MaterialDesignHeadline4TextBlock}"
            Text="MAXIMUM DEMAND TOOL" />
        <Button
            Grid.Column="6"
            Height="45"
            HorizontalAlignment="Right"
            VerticalAlignment="Center"
            Background="Transparent"
            BorderBrush="Transparent"
            Command="{Binding OpenDocumentationCommand}"
            Content="{materialDesign:PackIcon Kind=HelpCircleOutline,
                                              Size=38}"
            Foreground="#12A8B2" />
        <Separator
            Grid.ColumnSpan="6"
            Margin="10,45,15,0"
            Background="#FFCE00">
            <Separator.LayoutTransform>
                <ScaleTransform ScaleY="3.5" />
            </Separator.LayoutTransform>
        </Separator>

        <!--  Zone And DB  -->
        <materialDesign:Card
            Grid.Row="1"
            Grid.RowSpan="3"
            Grid.Column="1"
            Margin="10,15,20,10"
            materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="47*" />
                    <RowDefinition Height="51*" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  TextBox for zone name input  -->
                <TextBlock
                    Grid.Row="0"
                    Margin="16,16,16,14"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Text="DB ZONES" />
                <TextBox
                    x:Name="txtNodeName"
                    Grid.Row="1"
                    Width="Auto"
                    Height="25"
                    Margin="15,0,15,0"
                    VerticalAlignment="Center"
                    PreviewKeyDown="txtNodeName_PreviewKeyDown"
                    Text="{Binding ZoneNameInput, UpdateSourceTrigger=PropertyChanged}" />

                <!--  Placeholder for zone name TextBlock  -->
                <TextBlock
                    Grid.Row="1"
                    Margin="15,0,0,0"
                    VerticalAlignment="Center"
                    Foreground="Gray"
                    IsHitTestVisible="False"
                    Text="Create new DB Zone"
                    Visibility="{Binding Text, ElementName=txtNodeName, Converter={StaticResource StringToVisibilityConverter}}" />

                <Button
                    Grid.Row="1"
                    Grid.Column="1"
                    Width="30"
                    Height="25"
                    Margin="0,0,15,0"
                    Padding="0,0,0,0"
                    VerticalAlignment="Center"
                    Background="Transparent"
                    BorderBrush="Transparent"
                    Command="{Binding AddZoneToDataCommand}"
                    Content="{materialDesign:PackIcon Kind=Plus,
                                                      Size=20}"
                    Foreground="Green"
                    ToolTip="Add Zone" />

                <!--  TreeView with Edit buttons  -->
                <TreeView
                    x:Name="treeViewZones"
                    Grid.Row="2"
                    Grid.RowSpan="2"
                    Grid.ColumnSpan="2"
                    Margin="5,15,15,15"
                    Background="Transparent"
                    ItemsSource="{Binding Data.Zones}"
                    ScrollViewer.HorizontalScrollBarVisibility="Auto"
                    ScrollViewer.VerticalScrollBarVisibility="Visible"
                    SelectedItemChanged="TreeViewZones_SelectedItemChanged">

                    <TreeView.ItemTemplate>
                        <HierarchicalDataTemplate ItemsSource="{Binding DBs}">
                            <StackPanel Orientation="Horizontal">
                                <!--  Zone Node Display  -->
                                <TextBlock
                                    Margin="0,0,5,0"
                                    VerticalAlignment="Center"
                                    FontWeight="Bold"
                                    Text="{Binding ZoneName}" />

                                <!--  Rename Button for Zone  -->
                                <Button
                                    Width="25"
                                    Height="25"
                                    Margin="5,0,0,0"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    Command="{Binding DataContext.RenameParentNodeCommand, RelativeSource={RelativeSource AncestorType=TreeView}}"
                                    CommandParameter="{Binding}"
                                    Content="✏️"
                                    Cursor="Hand"
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#00ADEE"
                                    ToolTip="Rename Zone">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Background" Value="Transparent" />
                                            <Setter Property="Foreground" Value="Black" />
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Foreground" Value="Blue" />
                                                    <Setter Property="Background" Value="#EEE" />
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>

                                <!--  Remove Button for Zone  -->
                                <Button
                                    Width="25"
                                    Height="25"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    Command="{Binding DataContext.RemoveParentNodeCommand, RelativeSource={RelativeSource AncestorType=TreeView}}"
                                    CommandParameter="{Binding}"
                                    Content="❌"
                                    Cursor="Hand"
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="Red"
                                    ToolTip="Remove Zone">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Background" Value="Transparent" />
                                            <Setter Property="Foreground" Value="Red" />
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Foreground" Value="DarkRed" />
                                                    <Setter Property="Background" Value="#FEE" />
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>
                            </StackPanel>

                            <!--  Child Nodes (DBs)  -->
                            <HierarchicalDataTemplate.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock
                                            Margin="0,0,5,0"
                                            VerticalAlignment="Center"
                                            Text="{Binding DBName}" />

                                        <!--  Remove Button for DB  -->
                                        <Button
                                            Width="25"
                                            Height="25"
                                            Margin="2"
                                            Background="Transparent"
                                            BorderBrush="Transparent"
                                            Command="{Binding DataContext.RemoveDBFromZoneCommand, RelativeSource={RelativeSource AncestorType=TreeView}}"
                                            CommandParameter="{Binding}"
                                            Content="❌"
                                            Cursor="Hand"
                                            FontSize="10"
                                            FontWeight="Bold"
                                            Foreground="Red"
                                            ToolTip="Remove DB">
                                            <Button.Style>
                                                <Style TargetType="Button">
                                                    <Setter Property="Background" Value="Transparent" />
                                                    <Setter Property="Foreground" Value="Red" />
                                                    <Style.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Foreground" Value="DarkRed" />
                                                            <Setter Property="Background" Value="#FEE" />
                                                        </Trigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Button.Style>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </HierarchicalDataTemplate.ItemTemplate>
                        </HierarchicalDataTemplate>
                    </TreeView.ItemTemplate>
                </TreeView>

            </Grid>
        </materialDesign:Card>

        <!--  Unassigned DBs  -->
        <Expander
            Grid.Row="1"
            Grid.RowSpan="3"
            Margin="15,15,0,10"
            BorderBrush="#12A8B2"
            BorderThickness="2"
            ExpandDirection="Right">
            <Expander.Header>
                <Border>
                    <TextBlock
                        FontSize="15"
                        RenderTransformOrigin=".5,.5"
                        Text="Assign Distribution Board">
                        <TextBlock.LayoutTransform>
                            <RotateTransform Angle="90" />
                        </TextBlock.LayoutTransform>
                    </TextBlock>
                </Border>
            </Expander.Header>
            <!--  Unassigned DBs  -->
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="60" />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    Grid.Row="0"
                    Margin="16,16,16,14"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Text="UNASSIGNED DISTRIBUTION BOARDS" />
                <ScrollViewer
                    Grid.Row="1"
                    Margin="5,5,15,15"
                    HorizontalScrollBarVisibility="Auto"
                    VerticalScrollBarVisibility="Auto">
                    <ListBox Margin="5" ItemsSource="{Binding Data.OrphanedDBs}">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <!--  Add Button  -->
                                    <Button
                                        Width="25"
                                        Height="25"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Command="{Binding DataContext.AddOrphanedDBToZoneCommand, RelativeSource={RelativeSource AncestorType=Page}}"
                                        CommandParameter="{Binding}"
                                        Content="{materialDesign:PackIcon Kind=Plus,
                                                                          Size=18}"
                                        Cursor="Hand"
                                        Foreground="Green"
                                        ToolTip="Add this DB to the selected zone">
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Background" Value="Transparent" />
                                                <Setter Property="Foreground" Value="Green" />
                                                <Style.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Foreground" Value="DarkGreen" />
                                                        <Setter Property="Background" Value="#EEFFEE" />
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Button.Style>
                                    </Button>

                                    <!--  Display DB Name  -->
                                    <TextBlock
                                        Margin="0,0,5,0"
                                        VerticalAlignment="Center"
                                        Text="{Binding DBName}" />

                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </ScrollViewer>
            </Grid>
        </Expander>

        <!--  Setup  -->
        <materialDesign:Card
            Grid.Row="1"
            Grid.Column="2"
            Margin="0,15,20,10"
            materialDesign:ElevationAssist.Elevation="Dp2">
            <StackPanel>
                <TextBlock
                    Grid.Row="0"
                    Margin="16,16,16,14"
                    Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                    Text="Setup" />
                <Button
                    Width="Auto"
                    Margin="20,0,20,0"
                    Background="#12A8B2"
                    BorderBrush="#12A8B2"
                    Click="GoToProjectInfo_Click"
                    Content="Project Info"
                    Foreground="White" />
                <TextBlock
                    Margin="20,20,20,0"
                    FontSize="12"
                    Text="Load Density Value" />
                <ComboBox
                    Margin="20,10,20,15"
                    ItemsSource="{Binding Data.PDLookupDataOptions}"
                    SelectedIndex="0" />
            </StackPanel>
        </materialDesign:Card>

        <!--  Diversities  -->
        <materialDesign:Card
            Grid.Row="1"
            Grid.Column="3"
            Margin="0,15,20,10"
            materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2.5*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Row="0"
                    Margin="16,16,16,14"
                    Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                    Text="Diversities" />

                <TextBlock
                    Grid.Row="1"
                    Grid.Column="0"
                    Margin="15,0,0,0"
                    VerticalAlignment="Center"
                    FontWeight="SemiBold"
                    Text="Diversity Lighting:" />
                <TextBox
                    Grid.Row="1"
                    Grid.Column="1"
                    Margin="0,0,10,0">
                    <TextBox.Text>
                        <Binding
                            Converter="{StaticResource PercentageConverter}"
                            Path="Data.DiversityLighting"
                            UpdateSourceTrigger="LostFocus"
                            ValidatesOnDataErrors="True">
                            <Binding.ValidationRules>
                                <validations:PercentageRangeValidationRule />
                            </Binding.ValidationRules>
                        </Binding>
                    </TextBox.Text>
                </TextBox>
                <TextBlock
                    Grid.Row="2"
                    Grid.Column="0"
                    Margin="15,0,0,0"
                    VerticalAlignment="Center"
                    FontWeight="SemiBold"
                    Text="Diversity Power:" />
                <TextBox
                    Grid.Row="2"
                    Grid.Column="1"
                    Margin="0,0,10,0">
                    <TextBox.Text>
                        <Binding
                            Converter="{StaticResource PercentageConverter}"
                            Path="Data.DiversityPower"
                            UpdateSourceTrigger="LostFocus"
                            ValidatesOnDataErrors="True">
                            <Binding.ValidationRules>
                                <validations:PercentageRangeValidationRule />
                            </Binding.ValidationRules>
                        </Binding>
                    </TextBox.Text>
                </TextBox>
                <TextBlock
                    Grid.Row="3"
                    Grid.Column="0"
                    Margin="15,0,0,0"
                    VerticalAlignment="Center"
                    FontWeight="SemiBold"
                    Text="Diversity Mech:" />
                <TextBox
                    Grid.Row="3"
                    Grid.Column="1"
                    Margin="0,0,10,0">
                    <TextBox.Text>
                        <Binding
                            Converter="{StaticResource PercentageConverter}"
                            Path="Data.DiversityMech"
                            UpdateSourceTrigger="LostFocus"
                            ValidatesOnDataErrors="True">
                            <Binding.ValidationRules>
                                <validations:PercentageRangeValidationRule />
                            </Binding.ValidationRules>
                        </Binding>
                    </TextBox.Text>
                </TextBox>
                <TextBlock
                    Grid.Row="4"
                    Grid.Column="0"
                    Margin="15,0,0,0"
                    VerticalAlignment="Center"
                    FontWeight="SemiBold"
                    Text="Default Lump Load Diversity:" />
                <TextBox
                    Grid.Row="4"
                    Grid.Column="1"
                    Margin="0,0,10,0">
                    <TextBox.Text>
                        <Binding
                            Converter="{StaticResource PercentageConverter}"
                            Path="Data.DiversityLumpLoadDefault"
                            UpdateSourceTrigger="LostFocus"
                            ValidatesOnDataErrors="True">
                            <Binding.ValidationRules>
                                <validations:PercentageRangeValidationRule />
                            </Binding.ValidationRules>
                        </Binding>
                    </TextBox.Text>
                </TextBox>
            </Grid>
        </materialDesign:Card>

        <!--  Power Factors  -->
        <materialDesign:Card
            Grid.Row="1"
            Grid.Column="4"
            Margin="0,15,20,10"
            materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2.5*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Row="0"
                    Grid.ColumnSpan="2"
                    Margin="16,16,16,14"
                    Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                    Text="Power Factors" />

                <TextBlock
                    Grid.Row="1"
                    Grid.Column="0"
                    Margin="15,0,0,0"
                    VerticalAlignment="Center"
                    FontWeight="SemiBold"
                    Text="Power Factor Lighting:" />
                <TextBox
                    Grid.Row="1"
                    Grid.Column="1"
                    Margin="0,0,10,0">
                    <TextBox.Text>
                        <Binding
                            Path="Data.PowerFactorLighting"
                            UpdateSourceTrigger="LostFocus"
                            ValidatesOnDataErrors="True">
                            <Binding.ValidationRules>
                                <validations:PowerFactorValidationRule />
                            </Binding.ValidationRules>
                        </Binding>
                    </TextBox.Text>
                </TextBox>
                <TextBlock
                    Grid.Row="2"
                    Grid.Column="0"
                    Margin="15,0,0,0"
                    VerticalAlignment="Center"
                    FontWeight="SemiBold"
                    Text="Power Factor Power:" />
                <TextBox
                    Grid.Row="2"
                    Grid.Column="1"
                    Margin="0,0,10,0">
                    <TextBox.Text>
                        <Binding
                            Path="Data.PowerFactorPower"
                            UpdateSourceTrigger="LostFocus"
                            ValidatesOnDataErrors="True">
                            <Binding.ValidationRules>
                                <validations:PowerFactorValidationRule />
                            </Binding.ValidationRules>
                        </Binding>
                    </TextBox.Text>
                </TextBox>
                <TextBlock
                    Grid.Row="3"
                    Grid.Column="0"
                    Margin="15,0,0,0"
                    VerticalAlignment="Center"
                    FontWeight="SemiBold"
                    Text="Power Factor Mech:" />
                <TextBox
                    Grid.Row="3"
                    Grid.Column="1"
                    Margin="0,0,10,0">
                    <TextBox.Text>
                        <Binding
                            Path="Data.PowerFactorMech"
                            UpdateSourceTrigger="LostFocus"
                            ValidatesOnDataErrors="True">
                            <Binding.ValidationRules>
                                <validations:PowerFactorValidationRule />
                            </Binding.ValidationRules>
                        </Binding>
                    </TextBox.Text>
                </TextBox>
                <TextBlock
                    Grid.Row="4"
                    Grid.Column="0"
                    Margin="15,0,0,0"
                    VerticalAlignment="Center"
                    FontWeight="SemiBold"
                    Text="Default Power Factor Lump Load:" />
                <TextBox
                    Grid.Row="4"
                    Grid.Column="1"
                    Margin="0,0,10,0">
                    <TextBox.Text>
                        <Binding
                            Path="Data.PowerFactorLumpLoad"
                            UpdateSourceTrigger="LostFocus"
                            ValidatesOnDataErrors="True">
                            <Binding.ValidationRules>
                                <validations:PowerFactorValidationRule />
                            </Binding.ValidationRules>
                        </Binding>
                    </TextBox.Text>
                </TextBox>
            </Grid>
        </materialDesign:Card>

        <!--  Zones Data Grid  -->
        <materialDesign:Card
            Grid.Row="2"
            Grid.RowSpan="2"
            Grid.Column="2"
            Grid.ColumnSpan="4"
            Margin="0,10,15,10"
            materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    Grid.Row="0"
                    Margin="16,16,25,14"
                    Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                    Text="Maximum Demand Zones" />
                <DataGrid
                    Grid.Row="1"
                    AlternatingRowBackground="WhiteSmoke"
                    AutoGenerateColumns="False"
                    BorderBrush="LightGray"
                    BorderThickness="1"
                    CanUserAddRows="False"
                    FrozenColumnCount="2"
                    GridLinesVisibility="All"
                    HorizontalScrollBarVisibility="Auto"
                    ItemsSource="{Binding Data.Zones}"
                    RowHeight="28"
                    VerticalScrollBarVisibility="Auto">

                    <!--  Remove unnecessary paddings and gaps  -->
                    <DataGrid.RowStyle>
                        <Style TargetType="DataGridRow">
                            <Setter Property="Padding" Value="0" />
                            <Setter Property="Margin" Value="0" />
                        </Style>
                    </DataGrid.RowStyle>
                    <DataGrid.CellStyle>
                        <Style TargetType="DataGridCell">
                            <Setter Property="Padding" Value="0" />
                            <Setter Property="Margin" Value="0" />
                        </Style>
                    </DataGrid.CellStyle>
                    <DataGrid.Resources>
                        <Style TargetType="{x:Type DataGridRow}">
                            <Style.Triggers>
                                <!--  Trigger for when the row is selected  -->
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="AliceBlue" />
                                </Trigger>
                                <!--  Optional: Trigger for mouse hover effect  -->
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="LightGray" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.Resources>

                    <DataGrid.Columns>
                        <!--  Zone Name  -->
                        <DataGridTemplateColumn Width="*" IsReadOnly="True">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Center" Text="Zone&#x0a;Name" />
                                        <TextBlock HorizontalAlignment="Center" Text="" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        SnapsToDevicePixels="True"
                                        Text="{Binding ZoneName}"
                                        TextOptions.TextFormattingMode="Display"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Zone Area  -->
                        <DataGridTemplateColumn Width="*" IsReadOnly="True">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Center" Text="Zone&#x0a;Area" />
                                        <TextBlock HorizontalAlignment="Center" Text="(m2)" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        SnapsToDevicePixels="True"
                                        Text="{Binding TotalArea, Converter={StaticResource UnitConverter}, ConverterParameter='m²'}"
                                        TextOptions.TextFormattingMode="Display"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Zone Power Load  -->
                        <DataGridTemplateColumn Width="*" IsReadOnly="True">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Center" Text="Power&#x0a;Load" />
                                        <TextBlock HorizontalAlignment="Center" Text="" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        SnapsToDevicePixels="True"
                                        Text="{Binding SmallPowerLoadSum, Converter={StaticResource UnitConverter}, ConverterParameter='kVA'}"
                                        TextOptions.TextFormattingMode="Display"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Zone Lighting Load  -->
                        <DataGridTemplateColumn Width="*" IsReadOnly="True">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Center" Text="Lighting&#x0a;Load" />
                                        <TextBlock HorizontalAlignment="Center" Text="" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        SnapsToDevicePixels="True"
                                        Text="{Binding LightingLoadSum, Converter={StaticResource UnitConverter}, ConverterParameter='kVA'}"
                                        TextOptions.TextFormattingMode="Display"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Zone Mechanical Load  -->
                        <DataGridTemplateColumn Width="*" IsReadOnly="True">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Center" Text="Mech&#x0a;Load" />
                                        <TextBlock HorizontalAlignment="Center" Text="" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        SnapsToDevicePixels="True"
                                        Text="{Binding MechLoadSum, Converter={StaticResource UnitConverter}, ConverterParameter='kVA'}"
                                        TextOptions.TextFormattingMode="Display"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Zone Lump Load  -->
                        <DataGridTemplateColumn Width="*" IsReadOnly="True">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Center" Text="Lump&#x0a;Load" />
                                        <TextBlock HorizontalAlignment="Center" Text="" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        SnapsToDevicePixels="True"
                                        Text="{Binding LumpLoadSum, Converter={StaticResource UnitConverter}, ConverterParameter='kVA'}"
                                        TextOptions.TextFormattingMode="Display"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Zone Calculated Diversity  -->
                        <DataGridTemplateColumn Width="*" IsReadOnly="True">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Center" Text="Calculated&#x0a;Diversity" />
                                        <TextBlock HorizontalAlignment="Center" Text="" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        SnapsToDevicePixels="True"
                                        Text="{Binding CalculatedDiversity, Converter={StaticResource PercentageConverter}}"
                                        TextOptions.TextFormattingMode="Display"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Zone Diversified Load kVA  -->
                        <DataGridTemplateColumn Width="*" IsReadOnly="True">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Center" Text="Diversified&#x0a;Load" />
                                        <TextBlock HorizontalAlignment="Center" Text="" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        SnapsToDevicePixels="True"
                                        Text="{Binding DiversifiedLoad_kVA, Converter={StaticResource UnitConverter}, ConverterParameter='kVA'}"
                                        TextOptions.TextFormattingMode="Display"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Calculated Power Factor  -->
                        <DataGridTemplateColumn Width="*" IsReadOnly="True">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Center" Text="Calculated&#x0a;Power Factor" />
                                        <TextBlock HorizontalAlignment="Center" Text="" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        SnapsToDevicePixels="True"
                                        Text="{Binding CalculatedPowerFactor, Converter={StaticResource RoundingConverter}, ConverterParameter=2}"
                                        TextOptions.TextFormattingMode="Display"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Zone Diversified Load kW  -->
                        <DataGridTemplateColumn Width="*" IsReadOnly="True">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Center" Text="Diversified&#x0a;Load Total" />
                                        <TextBlock HorizontalAlignment="Center" Text="" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        SnapsToDevicePixels="True"
                                        Text="{Binding DiversifiedLoad_kW, Converter={StaticResource UnitConverter}, ConverterParameter='kW'}"
                                        TextOptions.TextFormattingMode="Display"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Zone Diversified Current A  -->
                        <DataGridTemplateColumn Width="*" IsReadOnly="True">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Center" Text="Diversified&#x0a;Current" />
                                        <TextBlock HorizontalAlignment="Center" Text="" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        SnapsToDevicePixels="True"
                                        Text="{Binding DiversifiedCurrent_A, Converter={StaticResource UnitConverter}, ConverterParameter='A'}"
                                        TextOptions.TextFormattingMode="Display"
                                        TextOptions.TextRenderingMode="ClearType"
                                        UseLayoutRounding="True" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Edit button  -->
                        <DataGridTemplateColumn Width="100">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock Margin="10,0,0,0" Text="Edit&#x0a;Zone" />
                                        <TextBlock Text="" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button
                                        Width="65"
                                        Height="20"
                                        Background="#12A8B2"
                                        BorderBrush="#12A8B2"
                                        Click="GoToEditZone_Click"
                                        CommandParameter="{Binding}"
                                        FontSize="10"
                                        Foreground="White">
                                        <Button.Content>
                                            <TextBlock>
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Setter Property="Text" Value="Edit" />
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsLocked}" Value="True">
                                                                <Setter Property="Text" Value="Locked" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Button.Content>
                                        <Button.IsEnabled>
                                            <Binding Converter="{StaticResource InverseBooleanConverter}" Path="IsLocked" />
                                        </Button.IsEnabled>
                                    </Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!--  Under Buttons  -->
        <StackPanel
            Grid.Row="4"
            Grid.ColumnSpan="6"
            Margin="0,10,0,0"
            HorizontalAlignment="Center"
            Orientation="Horizontal">
            <Button
                Width="100"
                Margin="0,0,10,0"
                Background="#12A8B2"
                BorderBrush="#12A8B2"
                Click="GoToSummary_Click"
                Content="Summary"
                Foreground="White" />
            <Button
                Width="100"
                Margin="0,0,0,0"
                Background="#12A8B2"
                BorderBrush="#12A8B2"
                Command="{Binding ExportCommand}"
                Content="Export"
                Foreground="White" />
            <Button
                Width="100"
                Margin="10,0,0,0"
                Background="#FFCE00"
                BorderBrush="#FFCE00"
                Command="{Binding SaveDataPageCommand}"
                Content="Save"
                Foreground="Black" />
            <Button
                Width="100"
                Margin="10,0,0,0"
                Background="#12A8B2"
                BorderBrush="#12A8B2"
                Click="Button_Click"
                Content="Close"
                Foreground="White" />
            <Popup
                x:Name="SuccessPopup"
                AllowsTransparency="True"
                HorizontalOffset="10"
                IsOpen="{Binding IsPopupVisible, Mode=TwoWay}"
                Placement="Top"
                PlacementTarget="{Binding ElementName=SaveButton}"
                StaysOpen="False"
                VerticalOffset="-15">
                <Border
                    Width="180"
                    Height="40"
                    Padding="10"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Background="Gray"
                    BorderBrush="Gray"
                    BorderThickness="1"
                    CornerRadius="5">
                    <TextBlock
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="14"
                        Foreground="White"
                        Text="{Binding PopupMessage}"
                        TextWrapping="Wrap" />
                </Border>
            </Popup>
        </StackPanel>

        <!--  Footer  -->
        <TextBlock
            Grid.Row="6"
            Grid.Column="5"
            Margin="0,5,15,10"
            HorizontalAlignment="Right"
            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
            Text="Make Everyday Better" />
        <Image
            Grid.Row="5"
            Grid.ColumnSpan="2"
            Height="25"
            Margin="15,5,0,10"
            HorizontalAlignment="Left"
            Source="/MEP.MaxDemand;component/Resources/BecaLogoBlack.png" />
    </Grid>
</Page>
