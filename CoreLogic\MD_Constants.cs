﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.MaxDemand.CoreLogic
{
    public enum LoadDensityValue
    {
        PD_Beca = 1,
        PD_AS_NZS_3000
    }

    public static class MD_Constants
    {
        // Parameters
        public const string SpaceDBParameterName_DB_Zone = "Beca_MD_DB_Zone";

        public const string SpaceParameterName_Lighting_Power_Density = "Beca_MD_Lighting_Power_Density";
        public const string SpaceParameterName_Small_Power_Density = "Beca_MD_Small_Power_Density";
        public const string SpaceParameterName_Mech_Power_Density = "Beca_MD_Mech_Power_Density";
        public const string SpaceParameterName_Lump_Load_1Phase = "Beca_MD_Lump_Load_1Phase";
        public const string SpaceParameterName_Lump_Load_3Phase = "Beca_MD_Lump_Load_3Phase";
        public const string SpaceParameterName_Lump_Load_Diversity = "Beca_MD_Lump_Load_Diversity";
        public const string SpaceParameterName_Lump_Load_Power_Factor = "Beca_MD_Lump_Load_Power_Factor";
        public const string SpaceParameterName_Spatial_Function = "Beca_MD_Spatial_Function";

        public const string DBParameterName_MD_Lighting = "Beca_MD_Lighting";
        public const string DBParameterName_MD_Lump = "Beca_MD_Lump";
        public const string DBParameterName_MD_Mech = "Beca_MD_Mech";
        public const string DBParameterName_MD_Power_Supply_Class = "Beca_MD_Power_Supply_Class";
        public const string DBParameterName_MD_Small_Power = "Beca_MD_Small_Power";

        public const string ProjectInfoParameterName_Site_Diversity = "Beca_MD_Site_Diversity";
        public const string ProjectInfoParameterName_Site_Spare_Capacity = "Beca_MD_Site_Spare_Capacity";
        public const string ProjectInfoParameterName_Diversity_Lighting = "Beca_MD_Diversity_Lighting";
        public const string ProjectInfoParameterName_Diversity_Mech = "Beca_MD_Diversity_Mech";
        public const string ProjectInfoParameterName_Diversity_Power = "Beca_MD_Diversity_Power"; 
        public const string ProjectInfoParameterName_Diversity_LumpLoad_Default = "Beca_MD_Diversity_Lump_Load_Default";
        public const string ProjectInfoParameterName_Load_Density_Data = "Beca_MD_Load_Density_Data";
        public const string ProjectInfoParameterName_PF_Lighting = "Beca_MD_PF_Lighting";
        public const string ProjectInfoParameterName_PF_Power = "Beca_MD_PF_Power";
        public const string ProjectInfoParameterName_PF_Mech = "Beca_MD_PF_Mech";
        public const string ProjectInfoParameterName_PF_Lump = "Beca_MD_PF_Lump";
        public const string ProjectInfoParameterName_Engineer = "Beca_Electrical_Engineer";
        public const string ProjectInfoParameterName_Verifier = "Beca_Electrical_Verifier"; 
        public const string ProjectInfoParameterName_Beca_MD_HVAC_Method = "Beca_MD_HVAC_Method";
        public const string ProjectInfoParameterName_Beca_MD_HVAC_Topology = "Beca_MD_HVAC_Topology";
        public const string ProjectInfoParameterName_ReferenceDrawing = "";

        public static string SharedParameterPath = Path.Combine(Path.GetFullPath(Path.Combine(System.Reflection.Assembly.GetExecutingAssembly().Location, @"../../../../")),
            "Addins", "MEP Tools", "_Shared Parameters");

        // Excel stuff
        public static string Default_MaxDemandTemplatePath = Path.Combine
            (Path.GetFullPath(Path.Combine(System.Reflection.Assembly.GetExecutingAssembly().Location, @"../../../../")),
            "Addins", "MEP Tools", "Maximum Demand Tool", "MASTER - Maximum Demand Calculation Sheet - Revit Version.xlsx");

        public static string PD_BecaPath = Path.Combine
            (Path.GetFullPath(Path.Combine(System.Reflection.Assembly.GetExecutingAssembly().Location, @"../../../../")),
            "Addins", "MEP Tools", "Maximum Demand Tool", "PD - Beca.csv");

        public static string PD_AS_NZA_3000Path = Path.Combine
            (Path.GetFullPath(Path.Combine(System.Reflection.Assembly.GetExecutingAssembly().Location, @"../../../../")),
            "Addins", "MEP Tools", "Maximum Demand Tool", "PD - AS-NZS 3000.csv");

        public static string DefaultOutputPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments) + "\\";

        public static int[] TransformerSizes = { 100, 150, 200, 300, 500, 750, 1000, 1500, 2000, 3000 };

        public static int[] GeneratorSizes = { 20, 50, 100, 150, 200, 300, 500, 750, 1000, 1500, 2000, 3000 };

        public static int[] UPSSizes = { 10, 20, 40, 50, 80, 100, 150, 200, 300, 500, 750, 1000 };

        public static Dictionary<string, double> HVACTopologyDictionary = new Dictionary<string, double>
        {
            {"Generic / TBC", 1 },
            {"VAV", 0.2 },
            {"VFR", 0.3 },
            {"2 Pipe", 0.4 },
            {"4 Pipe", 0.4 },
            {"Electric Heating", 1 },
            {"None", 0 }
        };

        // LPD connection
        // Locale prefixes.
        public const string SpacePrefixAu = "Au - ";
        public const string SpacePrefixNZ = "NZ - ";
        // Blank HVACSpaceType assignment.
        public const string NotAssigned = "<Not Assigned>";
        // Space types string. These hold the names of the possible space functions in J6/H1. They are matched to the HVACLoadSpaceType names which is how the
        // Space types are defined within Revit.
        public static string[][] NZSpaceTypes = new string[16][];
        public static double[][] NZSpaceTypeLimits = new double[16][];

        static MD_Constants()
        {
            //
            // Constants
            //
            // Static constructor to initialise the strings. Does not need to be called.
            //
            // NZ space types.
            InitializeNZSpaceTypes();

        }
        private static void InitializeNZSpaceTypes()
        {
            // Initialize "Not Assigned"
            NZSpaceTypes[0] = new string[] { MD_Constants.NotAssigned };
            NZSpaceTypeLimits[0] = new double[] { 0 };

            // Initialize General Space Functions
            NZSpaceTypes[1] = new string[]
            {
            "General spatial function - Commercial Laundry",
            "General spatial function - Circulation area",
            "General spatial function - Data / IT server / security room",
            "General spatial function - Entrance / portico",
            "General spatial function - Goods loading bay",
            "General spatial function - Kitchen / kitchenette / common room",
            "General spatial function - Lounges / breakout room",
            "General spatial function - Main reception / lift lobby / concierge",
            "General spatial function - Plant room / boiler room",
            "General spatial function - Private car parking (indoor)",
            "General spatial function - Public car parking (indoor)",
            "General spatial function - Public rest room / staff toilet / shower facilities",
            "General spatial function - Storeroom"
            };
            NZSpaceTypeLimits[1] = new double[] { 9, 7, 7, 13, 6, 10, 11, 10, 10, 2, 3, 7, 5 };

            // Initialize Café / Restaurant
            NZSpaceTypes[2] = new string[]
            {
            "Café / restaurant / fast food outlet - Unspecified spatial function",
            "Café / restaurant / fast food outlet - Commercial kitchen / food prep",
            "Café / restaurant / fast food outlet - Self-serve food counter",
            "Café / restaurant / fast food outlet - Seated / dining area"
            };
            NZSpaceTypeLimits[2] = new double[] { 9, 10, 9, 10 };

            // Initialize Assembly Service
            NZSpaceTypes[3] = new string[]
            {
            "Assembly service - Unspecified spatial function",
            "Assembly service - Seated area - town hall / church / events centre / whare rūnanga",
            "Assembly service - Ticketing hall",
            "Assembly service - Train concourse / platform",
            "Assembly service - Arrival / departure halls ≤ 5m height",
            "Assembly service - Arrival / departure halls ≥ 5m height",
            "Assembly service - Exhibition hall / gallery",
            "Assembly service - Museum",
            "Assembly service - Movie theatre",
            "Assembly service - Indoor swimming pool"
            };
            NZSpaceTypeLimits[3] = new double[] { 8, 10, 8, 7, 9, 11, 8, 9, 9, 9 };

            // Initialize Educational Spaces
            NZSpaceTypes[4] = new string[]
            {
            "Educational - Unspecified spatial function",
            "Educational - Classroom / science / technology",
            "Educational - Lecture theatre",
            "Educational - Gymnasium / fitness centre auditorium"
            };
            NZSpaceTypeLimits[4] = new double[] { 9, 10, 11, 10 };

            // Initialize Other Space Types (Healthcare, Office, Hotel, etc.)
            NZSpaceTypes[5] = new string[]
            {
            "Emergency - Unspecified spatial function",
            "Emergency - First response / emergency vehicle bay"
            };
            NZSpaceTypeLimits[5] = new double[] { 9, 9 };

            NZSpaceTypes[6] = new string[]
            {
            "General office - Unspecified spatial function",
            "General office - Individual office / meeting room (no natural light available)",
            "General office - Individual office / meeting room (natural light available)",
            "General office - Open office (no natural light available)",
            "General office - Open office (natural light available)"
            };
            NZSpaceTypeLimits[6] = new double[] { 7, 9, 7, 9, 9 };

            NZSpaceTypes[7] = new string[]
            {
            "Healthcare - Unspecified spatial function",
            "Healthcare - Aged-care facility",
            "Healthcare - Examination room",
            "Healthcare - Patient ward",
            "Healthcare - Ward lounge / whānau room",
            "Healthcare - Ward admin / office"
            };
            NZSpaceTypeLimits[7] = new double[] { 10, 18, 10, 5, 8, 10 };

            NZSpaceTypes[8] = new string[]
            {
            "Hotel / motel / hostel - Unspecified spatial function",
            "Hotel / motel / hostel - Bar / lounge / casino area",
            "Hotel / motel / hostel - Banquet room / conference room / function room / ballroom",
            "Hotel / motel / hostel - Hotel rooms / suites"
            };
            NZSpaceTypeLimits[8] = new double[] { 10, 10, 10, 6 };

            NZSpaceTypes[9] = new string[]
            {
            "Judicial / correctional - Unspecified spatial function",
            "Judicial / correctional - Courtroom"
            };
            NZSpaceTypeLimits[9] = new double[] { 9, 14 };

            // Initialize Remaining Space Categories (Simplified for brevity)
            NZSpaceTypes[10] = new string[] { "Laboratory - Unspecified spatial function" };
            NZSpaceTypeLimits[10] = new double[] { 11 };

            NZSpaceTypes[11] = new string[] { "Library - Unspecified spatial function" };
            NZSpaceTypeLimits[11] = new double[] { 12 };

            NZSpaceTypes[12] = new string[] { "Retail bank / post office - Unspecified spatial function" };
            NZSpaceTypeLimits[12] = new double[] { 10 };

            NZSpaceTypes[13] = new string[]
            {
            "Retail shopping / mega centre - Unspecified spatial function",
            "Retail shopping / mega centre - Pharmacy / convenience / general store",
            "Retail shopping / mega centre - Food court",
            "Retail shopping / mega centre - Supermarket",
            "Retail shopping / mega centre - Mega / furniture store",
            "Retail shopping / mega centre - Hardware / DIY store"
            };
            NZSpaceTypeLimits[13] = new double[] { 13, 14, 10, 13, 14, 13 };

            NZSpaceTypes[14] = new string[]
            {
            "Warehousing - Unspecified spatial function",
            "Warehousing - General Storage",
            "Warehousing - Logistics / sorting",
            "Warehousing - Cold Storage"
            };
            NZSpaceTypeLimits[14] = new double[] { 8, 8, 8, 8 };

            NZSpaceTypes[15] = new string[]
            {
            "Workshop - Unspecified spatial function",
            "Workshop - Rough task (such as heavy equipment)",
            "Workshop - Medium task (such as bench tasks)",
            "Workshop - Fine task (such as inspection tasks)"
            };
            NZSpaceTypeLimits[15] = new double[] { 10, 6, 9, 12 };
        }
    }
}
