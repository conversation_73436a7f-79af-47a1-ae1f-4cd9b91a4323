﻿using MEP.MaxDemand.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace MEP.MaxDemand.UI.View
{
    /// <summary>
    /// Interaction logic for MD_ProjectInfo.xaml
    /// </summary>
    public partial class MD_ProjectInfo : Page
    {
        public MD_ProjectInfo(MD_ViewModel viewModel)
        {
            InitializeComponent();

            DataContext = viewModel;
        }

        private void BackToZones_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.GoBack();
        }
    }
}
