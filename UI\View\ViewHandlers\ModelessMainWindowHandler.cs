﻿using Autodesk.Revit.DB.ExtensibleStorage;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.MaxDemand.CoreLogic.Calculation;
using MEP.MaxDemand.CoreLogic.MD_DataStorage;
using MEP.MaxDemand.CoreLogic;
using MEP.MaxDemand.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MEP.MaxDemand.ViewModels;
using System.Runtime.InteropServices;
using Autodesk.Revit.DB;
using System.Windows;
using MessageBox = System.Windows.MessageBox;
using Egor92.MvvmNavigation;
using BecaRevitUtilities.ElementUtilities;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB.Analysis;
using CommunityToolkit.Mvvm.Messaging;
using Newtonsoft.Json;

namespace MEP.MaxDemand.UI.View.ViewHandlers
{
    public class ModelessMainWindowHandler
    {
        static MD_ViewModel _viewModel;

        public static MD_ViewModel ViewModel { get =>  _viewModel; }

        static MainWindow _frmModelessMainWindow; 
        public static MainWindow FrmModelessMainWindow { get => _frmModelessMainWindow; }

        /// <summary>
        ///   This method creates and shows a modeless dialog, unless it already exists.
        /// </summary>
        /// <remarks>
        ///   The external command invokes this on the end-user's request
        /// </remarks>
        /// 
        public static void ShowForm(UIApplication uiapp, BecaActivityLoggerData logger, MD_DataModel data)
        {
            try
            {
                // If we do not have a dialog yet, create and show it
                if (_frmModelessMainWindow == null)
                {

                    // A new handler to handle request posting by the dialog
                    RequestHandler handler = new RequestHandler(logger);

                    // External Event for the dialog to use (to post requests)
                    ExternalEvent exEvent = ExternalEvent.Create(handler);

                    _viewModel = new MD_ViewModel(exEvent, handler, logger, uiapp, data);

                    // We give the objects to the new dialog;
                    // The dialog becomes the owner responsible fore disposing them, eventually.
                    _frmModelessMainWindow = new MainWindow(_viewModel);
                    // Clear the reference on close
                    _frmModelessMainWindow.Closed += (s, e) => _frmModelessMainWindow = null; 
                    _frmModelessMainWindow.Show();

                }
                else
                    _frmModelessMainWindow.Activate();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString(), "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }

        }

        public static bool MinimizeWindow()
        {
            try
            {
                _frmModelessMainWindow.WindowState = WindowState.Minimized;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString(), "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

        }

        public static void SaveProjectInfo(Document doc)
        {
            try
            {
                using (Transaction t = new Transaction(doc))
                {
                    t.Start("Save project info");
                    doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Engineer)?.Set(_viewModel.Data.Engineer);
                    doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Verifier)?.Set(_viewModel.Data.Verifier);

                    doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Site_Spare_Capacity)?.Set(_viewModel.Data.SiteSpareCapacity);
                    doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Site_Diversity)?.Set(_viewModel.Data.SiteDiversity);
                    t.Commit();
                }

                MessageBox.Show("Project Info successfully saved", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        public static void SetLoadDensityData(Document doc)
        {
            using (Transaction trans = new Transaction(doc, "Update Load Density Data"))
            {
                trans.Start();

                var param = doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Load_Density_Data);
                if (param != null)
                {
                    param.Set((int)_viewModel.Data.LoadDensityValue);
                }

                trans.Commit();
            }

            var lookupData = MD_Helper.GetPDLookUpDataFromCSV(doc);
            if (lookupData.Count == 0)
            {
                MessageBox.Show("PDLookupData is empty", "Warning", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            else
            {
                _viewModel.Data.PDLookupData = lookupData;
            }
        }

        public static void CreateNewDataStorage(Document doc)
        {
            var zoneNameInput = _viewModel.ZoneNameInput;
            var mD_DBZone = _viewModel.Data.Zones.ToList().Find(z => z.ZoneName.Equals(zoneNameInput));
            if (mD_DBZone == null)
            {
                return;
            }

            mD_DBZone.DBZoneDataStorage = DBZoneManager.CreateZoneDataStorage(doc, zoneNameInput, mD_DBZone);

            _viewModel.ZoneNameInput = string.Empty; // Clear the textbox
        }

        public static void SaveDataInTheMainForm(Document doc)
        {
            if (_viewModel != null)
            {
                var childlessZone = _viewModel.Data.Zones.Count(z => z.DBs == null || !z.DBs.Any());
                if (childlessZone > 0)
                {
                    // Show a message box to confirm if the user wants to close
                    var result = MessageBox.Show(
                        $"There are {childlessZone} zone(s) without any DBs.\nThese zones will not be saved until a DB is added.\nDo you want to proceed with saving?",
                        "Confirm Saving",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning
                    );

                    // Cancel saving if the user chooses "No"
                    if (result == MessageBoxResult.No)
                    {
                        return;
                    }
                }

                var data = _viewModel.Data;
                var projectInfo = doc.ProjectInformation;

                // Save data to Revit
                using (var trans = new Transaction(doc, "Set Project info params"))
                {
                    trans.Start();

                    if (!projectInfo.IsLocked(doc))
                    {
                        projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Site_Diversity).Set(_viewModel.Data.SiteDiversity);
                        projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Site_Spare_Capacity).Set(_viewModel.Data.SiteSpareCapacity);
                        projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Lighting).Set(_viewModel.Data.DiversityLighting);
                        projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Mech).Set(_viewModel.Data.DiversityMech);
                        projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Power).Set(_viewModel.Data.DiversityPower);
                        projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_LumpLoad_Default).Set(_viewModel.Data.DiversityLumpLoadDefault);
                        projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Lighting).Set(_viewModel.Data.PowerFactorLighting);
                        projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Power).Set(_viewModel.Data.PowerFactorPower);
                        projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Mech).Set(_viewModel.Data.PowerFactorMech);
                        projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Lump).Set(_viewModel.Data.PowerFactorLumpLoad);
                    }
                    
                    foreach (var dBZone in data.Zones)
                    {
                        foreach (var dB in dBZone.DBs)
                        {
                            if (dB.IsLocked)
                                continue;

                            dB.DB.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(dBZone.ZoneName);
                        }
                    }

                    _viewModel.HasUnsavedChanges = false;

                    if (trans.Commit() == TransactionStatus.Committed)
                    {
                        WeakReferenceMessenger.Default.Send(new ShowPopupMessage($"Data is saved!"));
                    }
                }

            }

        }

        public static void RenameDBZoneInDBsAndSpaces(Document doc)
        {
            using (var trans = new Transaction(doc, "Rename DB Zone"))
            {
                trans.Start();
                var mD_DBZone = _viewModel.SelectedZone;

                if (mD_DBZone != null)
                {
                    foreach (var dB in mD_DBZone.DBs)
                    {
                        dB.DB.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(mD_DBZone.ZoneName);
                    }

                    foreach (var mDSpace in mD_DBZone.Spaces)
                    {
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(mD_DBZone.ZoneName);
                    }

                    // Rename DB Zone in data storage
                    mD_DBZone.DBZoneDataStorage.Name = mD_DBZone.ZoneName;
                }

                trans.Commit();
            }
        }

        public static void ClearZonePrameterInDB(Document doc)
        {
            using (var trans = new Transaction(doc, "Remove zone in DB"))
            {
                trans.Start();
                var db = _viewModel.SelectedDB;
                db.DB.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(string.Empty);
                trans.Commit();
            }
        }

        public static void ClearDBZoneParameterInDBsAndSpaces(Document doc)
        {
            using (var trans = new Transaction(doc, "Clear DB Zone"))
            {
                trans.Start();
                var data = _viewModel.Data;
                var mD_DBZone = _viewModel.SelectedZone;

                // Delete this DB Zone from data storage
                if (mD_DBZone.DBZoneDataStorage != null)
                {
                    doc.Delete(mD_DBZone.DBZoneDataStorage.Id);
                }
                
                if (mD_DBZone != null)
                {
                    foreach (var dB in mD_DBZone.DBs)
                    {
                        // Clear DB Parameters
                        dB.DB.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(string.Empty);
                        dB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Lighting).Set(string.Empty);
                        dB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Lump).Set(string.Empty);
                        dB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Mech).Set(string.Empty);
                        dB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Power_Supply_Class).Set(string.Empty);
                        dB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Small_Power).Set(string.Empty);

                        // Add to orphaned DBs
                        data.OrphanedDBs.Add(dB);
                    }

                    foreach (var mDSpace in mD_DBZone.Spaces)
                    {
                        // Clear Space Parameters
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Lighting_Power_Density).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_1Phase).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_3Phase).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_Diversity).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_Power_Factor).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Mech_Power_Density).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Small_Power_Density).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Spatial_Function).Set(string.Empty);

                        // Add to orphaned Spaces
                        data.OrphanedSpaces.Add(mDSpace);
                    }

                    data.Zones.Remove(mD_DBZone);

                    // Remove DataStorage
                    var dts = DBZoneManager.GetDBZoneDataStorageByName(doc, mD_DBZone.ZoneName);
                    if (dts != null)
                    {
                        doc.Delete(dts.Id);
                    }

                    MD_CoreCalculation.RecalculateData(data);

                }

                trans.Commit();
            }
        }

        public static void SaveSpaceDBParametersZoneEntities(Document doc)
        {
            var hVACSpaceTypes = _viewModel.Data.HVACSpaceTypes;
            var mDZone = _viewModel.SelectedZone;
            var mD_Spaces = mDZone.Spaces;
            var mD_DBs = mDZone.DBs;

            var datastorage = DBZoneManager.GetDBZoneDataStorageByName(doc, mDZone.ZoneName);
            var mDZoneEntity = DBZoneManager.GetDBZoneEntityByZoneName(doc, datastorage, mDZone.ZoneName);

            using (Transaction trans = new Transaction(doc, "Save Space parameters"))
            {
                trans.Start();
                // Save Project Info parameters
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Beca_MD_HVAC_Method)?.Set(GetDictionaryIndexFromStringKey(_viewModel.Card1HVACMethodologyMap, _viewModel.SelectedItem1));
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Beca_MD_HVAC_Topology)?.Set(GetDictionaryIndexFromStringKey(_viewModel.Card2HVACToplogyMap, _viewModel.SelectedItem2));

                // Save Space data
                foreach (var mD_Space in mD_Spaces)
                {
                    if (mD_Space.IsLocked)
                        continue;

                    // Serialize SpatialEquipments
                    string jsonEquipmentData = JsonConvert.SerializeObject(mD_Space.SpatialEquipments);

                    // Set Entity
                    Entity entity = new Entity(DBZoneManager.GetSchemaByName(DBZoneManager.SpaceSchemaName));
                    entity.Set(DBZoneManager.SpaceEntityFieldName, jsonEquipmentData);

                    // Attach Entity to Space
                    mD_Space.Space.SetEntity(entity);

                    // Set Space parameters
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(mDZone.ZoneName);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Spatial_Function)?.Set(mD_Space.SpatialFunction);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Lighting_Power_Density).Set(mD_Space.LightingLoadDensity);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Small_Power_Density).Set(mD_Space.SmallPowerLoadDensity);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Mech_Power_Density).Set(mD_Space.MechLoadDensity);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_1Phase).Set(mD_Space.SinglePhaseLumpLoad);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_3Phase).Set(mD_Space.ThreePhaseLumpLoad);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_Diversity).Set(mD_Space.LumpLoadDiversity);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_Power_Factor).Set(mD_Space.LumpLoadPowerFactor);
                    mD_Space.Space.LookupParameter("Beca Comments").Set(mD_Space.Notes);

                    UpdateSpaceType(hVACSpaceTypes, mD_Space);
                }
                // Save DB data
                foreach (var mD_DB in mD_DBs)
                {
                    if (mD_DB.IsLocked)
                        continue;

                    mD_DB.DB.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(mDZone.ZoneName);
                    mD_DB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Power_Supply_Class).Set(mD_DB.PowerSupplyClass);
                    mD_DB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Small_Power).Set(mD_DB.IsSmallPower ? 1 : 0);
                    mD_DB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Lighting).Set(mD_DB.IsLighting ? 1 : 0);
                    mD_DB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Mech).Set(mD_DB.IsMech ? 1 : 0);
                    mD_DB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Lump).Set(mD_DB.IsLump ? 1 : 0);
                    mD_DB.DB.LookupParameter("Beca Comments").Set(mD_DB.Notes);
                }

                // Save Zone Entity
                if (mDZoneEntity != null)
                {
                    //// Save HVAC Topology
                    //mDZoneEntity.Set<double>(FieldNames.HVACTopologyLoadFactor.ToString(), mDZone.HVACTopologyLoadFactor, UnitTypeId.General);

                    // Save DB Zone HVAC
                    mDZoneEntity.Set<double>(FieldNames.HVACTopologyLoadFactor.ToString(), mDZone.DBZoneHVAC, UnitTypeId.General); // HVACTopologyLoadFactor is updated to store DBZoneHVAC

                    // Save Datatorage Gen and UPS data
                    mDZoneEntity.Set<int>(FieldNames.GeneratorSmallPower.ToString(), mDZone.EssentialSmallPowerLoadPercentage);
                    mDZoneEntity.Set<int>(FieldNames.GeneratorLighting.ToString(), mDZone.EssentialLightingLoadPercentage);
                    mDZoneEntity.Set<int>(FieldNames.GeneratorMechanical.ToString(), mDZone.EssentialMechanicalLoadPercentage);
                    mDZoneEntity.Set<int>(FieldNames.GeneratorLump.ToString(), mDZone.EssentialLumpLoadPercentage);
                    mDZoneEntity.Set<int>(FieldNames.UPSSmallPower.ToString(), mDZone.UninteruptableSmallPowerLoadPercentage);
                    mDZoneEntity.Set<int>(FieldNames.UPSLighting.ToString(), mDZone.UninteruptableLightingLoadPercentage);
                    mDZoneEntity.Set<int>(FieldNames.UPSMechanical.ToString(), mDZone.UninteruptableMechanicalLoadPercentage);
                    mDZoneEntity.Set<int>(FieldNames.UPSLump.ToString(), mDZone.UninteruptableLumpLoadPercentage);

                    datastorage?.SetEntity(mDZoneEntity);
                }
                
                if (trans.Commit() == TransactionStatus.Committed)
                {
                    WeakReferenceMessenger.Default.Send(new ShowPopupMessage($"Zone data is saved!"));
                }
            }
        }

        private static int GetDictionaryIndexFromStringKey(Dictionary<string, string> dict, string key)
        {
            int index = dict.Keys.ToList().FindIndex(k => k == key);

            return index == -1 ? 0 : index;
        }

        private static void UpdateSpaceType(List<HVACLoadSpaceType> hVACSpaceTypes, MD_SpaceModel mD_Space)
        {
            string spatialFunction;//spatialFunction
            // Getting the HVACLoadSpaceType name for comparison with the Function.
            var hVACLoadSpaceType = mD_Space.Space.Document.GetElement(mD_Space.Space.SpaceTypeId) as HVACLoadSpaceType;

            // If no SpaceTypeId is set h will be null. The equivalent in Function is the empty string.
            if (hVACLoadSpaceType == null)
                spatialFunction = string.Empty;
            else
                spatialFunction = hVACLoadSpaceType.Name.ToString().Replace(MD_Constants.SpacePrefixAu, "").Replace(MD_Constants.SpacePrefixNZ, "");

            // If the BecaLPD_Space Function is different from the HVACLoadSpaceType name we need to change it in the model.
            if (mD_Space.SpatialFunction != spatialFunction)
            {
                // If it is an empty string we just set the SpaceType to NoSpaceType.
                // TODO this doesn't work!!! May need to clear SpaceTypeId.
                if (mD_Space.SpatialFunction == null || mD_Space.SpatialFunction == "" || mD_Space.SpatialFunction == MD_Constants.NotAssigned)
                {
                    mD_Space.Space.SpaceType = SpaceType.NoSpaceType;
                }
                else
                {
                    // Searching for the right HVACLoadSpaceType to set.
                    foreach (var n in hVACSpaceTypes)
                    {
                        if (mD_Space.SpatialFunction == n.SpaceTypeName.ToString().Replace(MD_Constants.SpacePrefixAu, "").Replace(MD_Constants.SpacePrefixNZ, ""))
                        {
                            // Writing the space type change.
                            mD_Space.Space.SpaceTypeId = n.Id;

                            // We can break this foreach once it has been found.
                            break;
                        }
                    }
                }
            }
        }

    }
}
