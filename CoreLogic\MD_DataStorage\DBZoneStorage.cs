﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.ExtensibleStorage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.MaxDemand.CoreLogic.MD_DataStorage
{
    public enum FieldNames
    {
        Comments,
        Name,
        ZoneArea,
        GeneratorSmallPower,
        GeneratorLighting,
        GeneratorMechanical,
        GeneratorLump,
        UPSSmallPower,
        UPSLighting,
        UPSMechanical,
        UPSLump,
        DBs,
        Spaces,
        HVACTopologyLoadFactor, // This is updated to store DBZoneHVAC
        HVACMethodology,
        HVACTopology,
        DBZoneHVAC
    }

    public class DBZoneStorage
    {

        #region Fields
        string _dBZoneSchemaGUID = "960C522F-440F-429D-9891-246B73989AD4";
        string _dBZoneSchemaName = DBZoneManager.ZoneSchemaName;

        Document _doc;
        DataStorage _dBZoneDataStorage;
        Schema _dBZoneSchema;


        #endregion

        #region Singleton
        private static DBZoneStorage _instance;

        private DBZoneStorage(Document activeDoc)
        {
            _doc = activeDoc;
            _dBZoneSchema = DBZoneManager.GetSchemaByName(DBZoneManager.ZoneSchemaName);
            if (_dBZoneSchema == null)
            {
                CreateDBZoneSchema(new Guid(_dBZoneSchemaGUID), _dBZoneSchemaName);
            }

        }

        /// <summary>
        /// Must be called outside transaction as if it was not created, it will create transaction and create new datastorage.
        /// </summary>
        /// <param name="activeDoc"></param>
        /// <returns></returns>
        public static DBZoneStorage InitializeDBZoneStorageInstance(Document activeDoc)
        {
            // Uses lazy initialization.
            // Note: this is not thread safe.
            if (_instance == null || _instance._doc.IsValidObject == false || _instance._doc.PathName != activeDoc.PathName)
            {
                _instance = new DBZoneStorage(activeDoc);
            }
            return _instance;
        }


        #endregion

        #region Methods
        private void CreateDataStorage()
        {
            using (Transaction tx = new Transaction(_doc, "Create data storage"))
            {
                tx.Start();
                _dBZoneDataStorage = DataStorage.Create(_doc);
                _dBZoneDataStorage.Name = DBZoneManager.DataStorageName;
                tx.Commit();
            }
        }


        private void CreateDBZoneSchema(Guid guid, string schemaName)
        {
            using (var trans = new Transaction(_doc, "Create DB Zone schema"))
            {
                trans.Start();

                // Build schema
                SchemaBuilder schemaBuilder = new SchemaBuilder(guid);

                // Access level
                schemaBuilder.SetReadAccessLevel(AccessLevel.Public);
                schemaBuilder.SetWriteAccessLevel(AccessLevel.Vendor);

                // Vendor id
                schemaBuilder.SetVendorId("Beca");

                // Schema name
                schemaBuilder.SetSchemaName(schemaName);

                // Add DB Zone fields
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.Name), typeof(string))
                             .SetDocumentation("Unique Name for the DB Zone");

                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.ZoneArea), typeof(double)).SetSpec(SpecTypeId.Area);
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.HVACTopologyLoadFactor), typeof(double)).SetSpec(SpecTypeId.Number);// This is updated to store DBZoneHVAC
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.GeneratorSmallPower), typeof(int));
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.GeneratorLighting), typeof(int));
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.GeneratorMechanical), typeof(int));
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.GeneratorLump), typeof(int));
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.UPSSmallPower), typeof(int));
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.UPSLighting), typeof(int));
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.UPSMechanical), typeof(int));
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.UPSLump), typeof(int));
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.Comments), typeof(string)); 

                // Add fields for lists of ElementId
                schemaBuilder.AddArrayField(nameof(DBZoneEntity.DBs), typeof(ElementId));
                schemaBuilder.AddArrayField(nameof(DBZoneEntity.Spaces), typeof(ElementId)); 

                // Finish (register) the schema
                _dBZoneSchema = schemaBuilder.Finish();

                trans.Commit();
            }
        }

        private void CreateDataStorageEntity()
        {
            var activeDataStorageEntity = _dBZoneDataStorage.GetEntity(_dBZoneSchema);
            if (activeDataStorageEntity != null)
            {
                using (Transaction tx = new Transaction(_doc, "Create storage entity"))
                {
                    tx.Start();
                    new Entity(_dBZoneSchema);
                    tx.Commit();
                }
            }
        }

        #endregion

    }
}