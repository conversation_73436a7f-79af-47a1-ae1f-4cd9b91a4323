﻿using Autodesk.Revit.DB.ExtensibleStorage;
using CommunityToolkit.Mvvm.DependencyInjection;
using CommunityToolkit.Mvvm.Messaging;
using MEP.MaxDemand.CoreLogic.MD_DataStorage;
using MEP.MaxDemand.Models;
using MEP.MaxDemand.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Button = System.Windows.Controls.Button;

namespace MEP.MaxDemand.UI.View
{
    /// <summary>
    /// Interaction logic for MD_DataPage.xaml
    /// </summary>
    public partial class MD_DataPage : Page
    {
        private MD_ViewModel _viewModel;

        public MD_DataPage(MD_ViewModel viewModel)
        {
            InitializeComponent();

            _viewModel = viewModel;
            DataContext = _viewModel;
        }

        private void TreeViewZones_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (DataContext is MD_ViewModel viewModel)
            {
                if (e.NewValue is MD_ZoneModel)
                {
                    viewModel.SelectedZone = e.NewValue as MD_ZoneModel;
                }
                else if (e.NewValue is MD_DBModel)
                {
                    viewModel.SelectedDB = e.NewValue as MD_DBModel;
                }
            }
        }

        private void GoToEditZone_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            if (sender is Button button && button.CommandParameter is MD_ZoneModel selectedZone)
            {
                if (DataContext is MD_ViewModel viewModel)
                {
                    viewModel.SelectedZone = selectedZone;

                    if (selectedZone.DBs == null || selectedZone.DBs.Count < 1)
                    {
                        System.Windows.Forms.MessageBox.Show($"Please add DB to this zone.", "Null DB", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    var doc = selectedZone.DBs.FirstOrDefault().DB.Document;
                    var mDZoneEntity = DBZoneManager.GetDBZoneEntityByZoneName(doc, DBZoneManager.GetDBZoneDataStorageByName(doc, selectedZone.ZoneName), selectedZone.ZoneName);
                    if (mDZoneEntity == null)
                    {
                        System.Windows.Forms.MessageBox.Show($"Something is wrong with {selectedZone.ZoneName}." +
                            $"\nPlease delete and recreate this zone.", "Null Entity", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                    else
                    {
                        NavigationService.Navigate(new MD_ZoneEdit(_viewModel));
                    }
                }
            }
        }

        private void GoToProjectInfo_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.Navigate(new MD_ProjectInfo(_viewModel));
        }

        private void GoToSummary_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.Navigate(new MD_Summary(_viewModel));
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            var window = Window.GetWindow(this);
            if (window != null) window.Close();
        }

        private void txtNodeName_PreviewKeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == Key.Return)
            {
                var viewModel = DataContext as MD_ViewModel;
                if (viewModel?.AddZoneToDataCommand.CanExecute(null) == true)
                {
                    viewModel.AddZoneToDataCommand.Execute(null);
                    e.Handled = true;
                }
            }
        }
    }
}
