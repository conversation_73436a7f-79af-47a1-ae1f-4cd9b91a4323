﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.DB.ExtensibleStorage;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities.ElementUtilities;
using BecaRevitUtilities.SelectionFilters;
using Common.UI.Forms;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using MEP.MaxDemand.CoreLogic;
using MEP.MaxDemand.CoreLogic.Calculation;
using MEP.MaxDemand.CoreLogic.MD_DataStorage;
using MEP.MaxDemand.Models;
using MEP.MaxDemand.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Policy;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using Document = Autodesk.Revit.DB.Document;

namespace MEP.MaxDemand.UI.ModelessRevitForm
{
    public partial class FrmModelessMaxDemand : BecaBaseForm
    {

        public int SelectedZone;
        public FrmModelessMaxDemandMain MainForm;
        public string SelectedSpaceType;
        public MD_ZoneModel SelectedDBZone;

        RequestHandler _handler;
        ExternalEvent _externalEvent;

        UIApplication _uiapp;
        Document _doc;
        MD_DataModel _data;
        string _zoneNamePlaceholderText = "Enter zone name ...";

        private bool _isdgv_ConfigureSpaces_CellValueChanging = false;
        private bool _isdgv_ConfigureDBs_CellValueChanging = false;

        #region Constructors

        public FrmModelessMaxDemand(ExternalEvent exEvent, RequestHandler handler, UIApplication uiapp, FrmModelessMaxDemandMain mainForm)
        {
            InitializeComponent();

            _handler = handler;
            _externalEvent = exEvent;

            _uiapp = uiapp;
            _doc = uiapp.ActiveUIDocument.Document;
            _data = mainForm.Data;
            MainForm = mainForm;

            SelectedDBZone = MainForm.SelectedDBZoneToEdit;

            lbl_SelectedNode.Text = $"DB Zone: {SelectedDBZone?.ZoneName}";

            PopulateSpaceType(_data.PDLookupData);

            InitializeDBZoneGeneratorAndUPS();

            RecalculateDataAndUpdateUI();
        }

        private void RecalculateDataAndUpdateUI()
        {
            MD_CalculationService.RecalculateData(_data);

            PopulateDBZoneSpaces(SelectedDBZone);

            UpdateTotalAndWeightedAverageRow();

            PopulateDBZoneDBs();
        }

        private void PopulateDBZoneDBs()
        {
            dgv_ConfigureDBs.Rows.Clear(); 

            foreach (var mD_DB in SelectedDBZone.DBs)
            {
                var parentDB = 
                dgv_ConfigureDBs.Rows.Add(
                    mD_DB.Level.Name,
                    mD_DB.DB.Name, 
                    mD_DB.PowerSupplyClass,
                    mD_DB.IsSmallPower,
                    mD_DB.IsLighting,
                    mD_DB.IsMech,
                    mD_DB.IsLump,
                    mD_DB.PowerFactor,
                    mD_DB.DiversifiedPerPhaseCurrent,
                    mD_DB.DBCableLength,
                    mD_DB.ParentDB?.Name);
            }

            UIHelper.InitializeTableLayoutPanel(tlp_ConfigureDBs, dgv_ConfigureDBs);

            UIHelper.AlignTableLayoutPanelWithDataGridView(tlp_ConfigureDBs, dgv_ConfigureDBs);

        }

        public void InitializeDBZoneGeneratorAndUPS()
        {
            dgv_ConfigureGeneratorAndUPS.Rows.Clear();

            var dataStorage = SelectedDBZone.DBZoneDataStorage;
            if(dataStorage == null)
            {
                MessageBox.Show("Could not find DBZone DataStorage.\nGenerator and UPS data cannot be loaded.");
                return;
            }

            var dBZoneEntity = DBZoneManager.GetDBZoneEntityByZoneName(_doc, dataStorage, SelectedDBZone.ZoneName);

            var GeneratorSmallPower = dBZoneEntity?.Get<int>(nameof(DBZoneEntity.GeneratorSmallPower)) ?? 0;
            var GeneratorLighting = dBZoneEntity?.Get<int>(nameof(DBZoneEntity.GeneratorLighting)) ?? 0;
            var GeneratorMechanical = dBZoneEntity?.Get<int>(nameof(DBZoneEntity.GeneratorMechanical)) ?? 0;
            var GeneratorLump = dBZoneEntity?.Get<int>(nameof(DBZoneEntity.GeneratorLump)) ?? 0;
            var UPSSmallPower = dBZoneEntity?.Get<int>(nameof(DBZoneEntity.UPSSmallPower)) ?? 0;
            var UPSLighting = dBZoneEntity?.Get<int>(nameof(DBZoneEntity.UPSLighting)) ?? 0;
            var UPSMechanical = dBZoneEntity?.Get<int>(nameof(DBZoneEntity.UPSMechanical)) ?? 0;
            var UPSLump = dBZoneEntity?.Get<int>(nameof(DBZoneEntity.UPSLump)) ?? 0;

            dgv_ConfigureGeneratorAndUPS.Rows.Add(
                SelectedDBZone.ZoneName,
                dBZoneEntity?.Get<int>(nameof(DBZoneEntity.GeneratorSmallPower)) ?? 0,
                dBZoneEntity?.Get<int>(nameof(DBZoneEntity.GeneratorLighting)) ?? 0,
                dBZoneEntity?.Get<int>(nameof(DBZoneEntity.GeneratorMechanical)) ?? 0,
                dBZoneEntity?.Get<int>(nameof(DBZoneEntity.GeneratorLump)) ?? 0,
                dBZoneEntity?.Get<int>(nameof(DBZoneEntity.UPSSmallPower)) ?? 0,
                dBZoneEntity?.Get<int>(nameof(DBZoneEntity.UPSLighting)) ?? 0,
                dBZoneEntity?.Get<int>(nameof(DBZoneEntity.UPSMechanical)) ?? 0,
                dBZoneEntity?.Get<int>(nameof(DBZoneEntity.UPSLump)) ?? 0
                );

            UIHelper.InitializeTableLayoutPanel(tlp_ConfigureGeneratorAndUPS, dgv_ConfigureGeneratorAndUPS);

            UIHelper.AlignTableLayoutPanelWithDataGridView(tlp_ConfigureGeneratorAndUPS, dgv_ConfigureGeneratorAndUPS);
        }


        #endregion

        #region Methods

        #region Modeless Functionality

        /// <summary>
        ///   A private helper method to make a request
        ///   and put the dialog to sleep at the same time.
        /// </summary>
        /// <remarks>
        ///   It is expected that the process which executes the request 
        ///   (the Idling helper in this particular case) will also
        ///   wake the dialog up after finishing the execution.
        /// </remarks>
        ///
        private void MakeRequest(RequestId request)
        {
            _handler.Request.Make(request);
            _externalEvent.Raise();
            DozeOff();

        }

        /// <summary>
        ///   Control enabler / disabler 
        /// </summary>
        ///
        private void EnableCommands(bool status)
        {
            foreach (System.Windows.Forms.Control ctrl in this.Controls)
            {
                ctrl.Enabled = status;
            }
            if (!status)
            {
                this.btn_SaveDBZone.Enabled = true;
            }
        }

        /// <summary>
        ///   DozeOff -> disable all controls (but the Exit button)
        /// </summary>
        /// 
        private void DozeOff()
        {
            EnableCommands(false);
        }

        /// <summary>
        ///   WakeUp -> enable all controls
        /// </summary>
        /// 
        public void WakeUp()
        {
            EnableCommands(true);
        }

        #region Button Clicks

        private void btn_SaveDBZone_Click(object sender, EventArgs e)
        {
            UpdateDBGENUPSDataFromUI(_data);

            MainForm.Data = _data;

            MakeRequest(RequestId.SaveSpaceDBParametersZoneEntities);
            this.Close();
        }

        private void UpdateDBGENUPSDataFromUI(MD_DataModel data)
        {
            var mD_DBs = SelectedDBZone.DBs;
            var mD_Spaces = SelectedDBZone.Spaces;

            // Save Space property changers from UI
            foreach (DataGridViewRow row in eadgv_ConfigureSpaces.Rows)
            {
                var mD_Space = mD_Spaces.Find(s => s.Space.Name.Equals(row.Cells[SpaceName.Index].Value?.ToString()));

                if (mD_Space == null)
                    continue;

                mD_Space.SpaceName = row.Cells[SpaceName.Index].Value.ToString();
                mD_Space.Area = Convert.ToDouble(row.Cells[Area.Index].Value.ToString());
                mD_Space.SpatialFunction =  row.Cells[SpatialFunction.Index].Value.ToString();
                mD_Space.LightingLoadDensity = Convert.ToDouble(row.Cells[LightingLoadDensity.Index].Value.ToString());
                mD_Space.MechLoadDensity = Convert.ToDouble(row.Cells[MechLoadDensity.Index].Value.ToString());
                mD_Space.SmallPowerLoadDensity = Convert.ToDouble(row.Cells[SmallPowerLoadDensity.Index].Value.ToString());
                mD_Space.SinglePhaseLumpLoad = Convert.ToDouble(row.Cells[SinglePhaseLumpLoad.Index].Value.ToString());
                mD_Space.ThreePhaseLumpLoad = Convert.ToDouble(row.Cells[ThreePhaseLumpLoad.Index].Value.ToString());
                mD_Space.LumpLoadDiversity = Convert.ToDouble(row.Cells[LumpLoadDiversity.Index].Value.ToString());
            }

            // Save DB property changes from UI
            foreach (DataGridViewRow row in dgv_ConfigureDBs.Rows)
            {
                var mD_DB = mD_DBs.Find(db => db.DB.Name.Equals(row.Cells[DBName.Index].Value?.ToString()));

                mD_DB.PowerSupplyClass = row.Cells[PowerSupplyClass.Index].Value?.ToString();
                mD_DB.IsSmallPower = row.Cells[SmallPowerGEU.Index].Value != null && (bool)row.Cells[SmallPowerGEU.Index].Value;
                mD_DB.IsLighting = row.Cells[LightingGEU.Index].Value != null && (bool)row.Cells[LightingGEU.Index].Value;
                mD_DB.IsMech = row.Cells[MechGEU.Index].Value != null && (bool)row.Cells[MechGEU.Index].Value;
                mD_DB.IsLump = row.Cells[LumpGEU.Index].Value != null && (bool)row.Cells[LumpGEU.Index].Value;
                mD_DB.Notes = row.Cells[Notes.Index].Value?.ToString();
            }

            // Save Generator and UPS configuration from UI
            foreach (DataGridViewRow row in dgv_ConfigureGeneratorAndUPS.Rows)
            {
                SelectedDBZone.EssentialSmallPowerLoadPercentage = row.Cells[SmallPowerOnGenerator.Index].Value != null && int.TryParse(row.Cells[SmallPowerOnGenerator.Index].Value.ToString(), out int smallPowerValue) ? smallPowerValue : 0;
                SelectedDBZone.EssentialLightingLoadPercentage = row.Cells[LightingOnGenerator.Index].Value != null && int.TryParse(row.Cells[LightingOnGenerator.Index].Value.ToString(), out int lightingValue) ? lightingValue : 0;
                SelectedDBZone.EssentialMechanicalLoadPercentage = row.Cells[MechOnGenerator.Index].Value != null && int.TryParse(row.Cells[MechOnGenerator.Index].Value.ToString(), out int mechValue) ? mechValue : 0;
                SelectedDBZone.EssentialLumpLoadPercentage = row.Cells[LumpLoadOnGenerator.Index].Value != null && int.TryParse(row.Cells[LumpLoadOnGenerator.Index].Value.ToString(), out int lumpValue) ? lumpValue : 0;
                SelectedDBZone.UninteruptableSmallPowerLoadPercentage = row.Cells[SmallPowerOnUPS.Index].Value != null && int.TryParse(row.Cells[SmallPowerOnUPS.Index].Value.ToString(), out int upsSmallPowerValue) ? upsSmallPowerValue : 0;
                SelectedDBZone.UninteruptableLightingLoadPercentage = row.Cells[LightingOnUPS.Index].Value != null && int.TryParse(row.Cells[LightingOnUPS.Index].Value.ToString(), out int upsLightingValue) ? upsLightingValue : 0;
                SelectedDBZone.UninteruptableMechanicalLoadPercentage = row.Cells[MechOnUPS.Index].Value != null && int.TryParse(row.Cells[MechOnUPS.Index].Value.ToString(), out int upsMechValue) ? upsMechValue : 0;
                SelectedDBZone.UninteruptableLumpLoadPercentage = row.Cells[LumpLoadOnUPS.Index].Value != null && int.TryParse(row.Cells[LumpLoadOnUPS.Index].Value.ToString(), out int upsLumpValue) ? upsLumpValue : 0;
            }

        }

        #endregion

        #endregion

        #endregion

        private void PopulateSpaceType(List<PDLoadRow> lookupdata)
        {
            foreach (var pDLoadRow in lookupdata)
            {
                lb_SpaceType.Items.Add(pDLoadRow.SpatialFunction);
            }
        }

        private void btn_AddSpaces_Click(object sender, EventArgs e)
        {
            var spaceExistsCount = 0;
            var zoneSpaces = new List<ZoneSpace>();
            var sb = new StringBuilder();
            var sbLockedSpaces = new StringBuilder();
            try
            {
                var dbZone = SelectedDBZone;

                // Pick spaces from Revit.
                var references = _uiapp.ActiveUIDocument.Selection.PickObjects(ObjectType.Element, new OnlySpacesSelectionFilter(), $"Select spaces to add to {SelectedDBZone.ZoneName}");
                var doc = _uiapp.ActiveUIDocument.Document;
                foreach (var reference in references)
                {
                    var flag = false;
                    var selectedSpace = doc.GetElement(reference.ElementId) as Space;

                    if (selectedSpace?.Area > 0)
                    {
                        // Check if it's locked
                        var spaceIsLocked = selectedSpace.IsLocked(doc);
                        if (spaceIsLocked)
                        {
                            sbLockedSpaces.AppendLine($"{selectedSpace.Number} : {selectedSpace.Name}. Locked by: {selectedSpace.ElementOwner(doc)}");
                            continue;
                        }

                        // Check if the space is already used
                        foreach (var zone in _data.Zones)
                        {
                            foreach (var space in zone.Spaces)
                            {
                                if (space.Space.Name == selectedSpace.Name)
                                {
                                    zoneSpaces.Add(new ZoneSpace() { SpaceName = space.Space.Name, ZoneName = zone.ZoneName });
                                    spaceExistsCount++;
                                    flag = true;
                                    continue;
                                }
                            }
                        }
                        if (flag)
                            continue;

                        // Add space to db zone
                        dbZone.Spaces.Add(new MD_SpaceModel(selectedSpace));
                        //Remove space from orphaned spaces
                        _data.OrphanedSpaces.RemoveAll(s => s.Id == selectedSpace.Id);
                    }
                }
                this.BringToFront();

                if (sbLockedSpaces.Length > 0)
                {
                    MessageBox.Show($"These spaces are locked and will not be added to the selected zone.\n\n{sbLockedSpaces}");
                }
                if (spaceExistsCount > 0)
                {
                    zoneSpaces.OrderBy(x => x.ZoneName).ToList().ForEach(x => sb.AppendLine($"DBZone: {x.ZoneName}. Space: {x.SpaceName}."));
                    MessageBox.Show($"{spaceExistsCount} space(s) already being used and will not be added to the selected zone:\n\n{sb}");
                }

                RecalculateDataAndUpdateUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void btn_RemoveSpaces_Click(object sender, EventArgs e)
        {
            try
            {
                var dbZone = _data.Zones.Find(z => z.ZoneName == SelectedDBZone.ZoneName);
                foreach (DataGridViewRow row in eadgv_ConfigureSpaces.SelectedRows)
                {
                    var space = _uiapp.ActiveUIDocument.Document.GetElement(new ElementId(Convert.ToInt32(row.Cells[0].Value.ToString()))) as Space;
                    dbZone.Spaces.RemoveAt(row.Index);
                    _data.OrphanedSpaces.Add(space);
                    // Clear parameter
                    space.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(String.Empty);
                    // Remove from dgv
                    eadgv_ConfigureSpaces.Rows.RemoveAt(row.Index);
                }

                RecalculateDataAndUpdateUI();

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void PopulateDBZoneSpaces(MD_ZoneModel selectedDbZone)
        {
            eadgv_ConfigureSpaces.Rows.Clear();

            if (selectedDbZone != null)
            {
                foreach (var mD_Space in selectedDbZone.Spaces.OrderBy(s => s.SpaceName))
                {
                    eadgv_ConfigureSpaces.Rows.Add
                        (mD_Space.Id.GetIdValue().ToString(),
                        mD_Space.SpaceName,
                        mD_Space.Space.Level.Name,
                        mD_Space.Area, 
                        mD_Space.SpatialFunction,
                        Math.Round(mD_Space.SmallPowerLoadDensity, 2),
                        Math.Round(mD_Space.LightingLoadDensity, 2),
                        Math.Round(mD_Space.MechLoadDensity, 2),
                        Math.Round(mD_Space.SinglePhaseLumpLoad, 2),
                        Math.Round(mD_Space.ThreePhaseLumpLoad, 2),
                        Math.Round(mD_Space.LumpLoadDiversity * 100, 2),
                        Math.Round(mD_Space.SmallPowerLoad, 2), 
                        Math.Round(mD_Space.LightingLoad, 2),  
                        Math.Round(mD_Space.MechLoad, 2),  
                        Math.Round(mD_Space.LumpLoad, 2), 
                        Math.Round(mD_Space.CalculatedDiversity, 2), 
                        Math.Round(mD_Space.DiversifiedLoad_kVA, 2),
                        Math.Round(mD_Space.CalculatedPowerFactor, 2),
                        Math.Round(mD_Space.DiversifiedLoad_kW, 2),
                        Math.Round(mD_Space.DiversifiedCurrent_A, 2)); 
                }

                var totalDiversifiedLoad = Math.Round(selectedDbZone.Spaces.Select(a => a.DiversifiedLoad_kVA).Sum(), 2);

                if (selectedDbZone.Spaces.Count > 0)
                {
                    // Add Total and Weighted Average rows
                    int newRowIndex = eadgv_ConfigureSpaces.Rows.Add
                            (string.Empty,
                            string.Empty,
                            "Total / Average",
                            Math.Round(selectedDbZone.Spaces.Select(a => a.Area).Sum(), 2),
                            string.Empty,
                            Math.Round(selectedDbZone.Spaces.Select(a => a.SmallPowerLoadDensity).Average(), 2),
                            Math.Round(selectedDbZone.Spaces.Select(a => a.LightingLoadDensity).Average(), 2),
                            Math.Round(selectedDbZone.Spaces.Select(a => a.MechLoadDensity).Average(), 2),
                            Math.Round(selectedDbZone.Spaces.Select(a => a.SinglePhaseLumpLoad).Sum(), 2),
                            Math.Round(selectedDbZone.Spaces.Select(a => a.ThreePhaseLumpLoad).Sum(), 2),
                            Math.Round(selectedDbZone.TotalLumpLoadWeightedAverage, 2),
                            Math.Round(selectedDbZone.Spaces.Select(a => a.SmallPowerLoad).Sum(), 2),
                            Math.Round(selectedDbZone.Spaces.Select(a => a.LightingLoad).Sum(), 2),
                            Math.Round(selectedDbZone.Spaces.Select(a => a.MechLoad).Sum(), 2),
                            Math.Round(selectedDbZone.Spaces.Select(a => a.LumpLoad).Sum(), 2),
                            Math.Round(selectedDbZone.CalculatedDiversityWeightedAverage, 2),
                            totalDiversifiedLoad,
                            Math.Round(selectedDbZone.TotalPowerFactorWeightedAverage, 2),
                            Math.Round(selectedDbZone.Spaces.Select(a => a.DiversifiedLoad_kW).Sum(), 2),
                            Math.Round(selectedDbZone.Spaces.Select(a => a.DiversifiedCurrent_A).Sum(), 2));
                    eadgv_ConfigureSpaces.Rows[newRowIndex].Height = 30;
                    eadgv_ConfigureSpaces.Rows[newRowIndex].DefaultCellStyle.Font = new System.Drawing.Font(eadgv_ConfigureSpaces.Font, FontStyle.Bold);
                }

                // Freeze the first three columns
                for (int i = 0; i < 3; i++)
                {
                    eadgv_ConfigureSpaces.Columns[i].Frozen = true;
                }

                lbl_TotalSpaceLoad.Text = $"Total: {totalDiversifiedLoad} KVA";
            }

        }

        private void dgv_ConfigureGeneratorAndUPS_ColumnWidthChanged(object sender, DataGridViewColumnEventArgs e)
        {
            UIHelper.AlignTableLayoutPanelWithDataGridView(tlp_ConfigureGeneratorAndUPS, dgv_ConfigureGeneratorAndUPS);
        }

        private void dgv_ConfigureGeneratorAndUPS_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            // Check if the column is one of the specified indices
            if (e.ColumnIndex == SmallPowerOnGenerator.Index ||
                e.ColumnIndex == LightingOnGenerator.Index ||
                e.ColumnIndex == MechOnGenerator.Index ||
                e.ColumnIndex == LumpLoadOnGenerator.Index ||
                e.ColumnIndex == SmallPowerOnUPS.Index ||
                e.ColumnIndex == LightingOnUPS.Index ||
                e.ColumnIndex == MechOnUPS.Index ||
                e.ColumnIndex == LumpLoadOnUPS.Index)
            {
                var inputIsGood = true;
                var percentageAllocationIsGood = true;

                // Get the cell value
                var cellValue = dgv_ConfigureGeneratorAndUPS.Rows[e.RowIndex].Cells[e.ColumnIndex].Value;

                // Check if the input is valid
                if (cellValue == null || !int.TryParse(cellValue.ToString(), out int value) || value < 0 || value > 100)
                {
                    // Show an error message for invalid input
                    MessageBox.Show("Please enter a valid integer between 0 and 100", "Invalid Input", MessageBoxButtons.OK, MessageBoxIcon.Error);

                    // Reset the cell value to a default (e.g., 0)
                    dgv_ConfigureGeneratorAndUPS.Rows[e.RowIndex].Cells[e.ColumnIndex].Value = 0;

                    inputIsGood = false;
                }

                try
                {
                    // Define the generator and UPS column indices for each power type
                    var powerTypeColumns = new (int generatorIndex, int upsIndex)[]
                    {
                        (SmallPowerOnGenerator.Index, SmallPowerOnUPS.Index),
                        (LightingOnGenerator.Index, LightingOnUPS.Index),
                        (MechOnGenerator.Index, MechOnUPS.Index),
                        (LumpLoadOnGenerator.Index, LumpLoadOnUPS.Index)
                    };

                    // Iterate through each power type and check the allocation
                    foreach (var (generatorIndex, uPSIndex) in powerTypeColumns)
                    {
                        int generatorValue = GetCellValueAsInt(dgv_ConfigureGeneratorAndUPS, e.RowIndex, generatorIndex);
                        int uPSValue = GetCellValueAsInt(dgv_ConfigureGeneratorAndUPS, e.RowIndex, uPSIndex);

                        if (generatorValue + uPSValue > 100)
                        {
                            MessageBox.Show("Allocated percentages add up to more than 100% (see Percent Inputs)");
                            percentageAllocationIsGood = false;
                            break; 
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error processing cell value: {ex.Message}");
                }

                if (inputIsGood && percentageAllocationIsGood)
                {
                    RecalculateDataAndUpdateUI();
                }
            }
        }

        private int GetCellValueAsInt(DataGridView dgv, int rowIndex, int columnIndex, int defaultValue = 0)
        {
            // Ensure the rowIndex and columnIndex are within valid bounds
            if (rowIndex >= 0 && rowIndex < dgv.Rows.Count &&
                columnIndex >= 0 && columnIndex < dgv.Columns.Count)
            {
                var cellValue = dgv.Rows[rowIndex].Cells[columnIndex].Value;

                // Check if the value is not null and can be parsed as an integer
                if (cellValue != null && int.TryParse(cellValue.ToString(), out int intValue))
                {
                    return intValue;
                }
            }

            // Return the default value if invalid or null
            return defaultValue;
        }

        private void lb_SpaceType_SelectedIndexChanged(object sender, EventArgs e)
        {
            //
            // lbxSpaceType_SelectedIndexChange
            //
            // When the user selects something in the list box we want to update the "Function" of the currently selected space to match it. Note that this
            // doesn't actually update the space in Revit yet - we wait for the save to check which space functions have been changed and update them.
            //

            // Getting the value of the newly selected list box item.
            string sel = lb_SpaceType.SelectedItem.ToString();

            var allMD_Spaces = _data.Zones.SelectMany(dz => dz.Spaces).ToList();

            // Setting the values in the BecaLPD_Space object.
            foreach (DataGridViewRow row in eadgv_ConfigureSpaces.SelectedRows)
            {
                // Get MD_Space
                var mD_Space = allMD_Spaces.Find(s => s.Space.Id.GetIdValue().ToString().Equals(row.Cells[SpaceID.Index].Value.ToString()));

                // Set Spatial Function
                row.Cells[SpatialFunction.Index].Value = sel;

                // Set W/m2 from lookup
                var pDLoad = _data.PDLookupData.Find(p => p.SpatialFunction == sel);
                if (pDLoad == null)
                    continue;

                var lightingDensity = pDLoad.LightingDensity;
                var powerDensity = pDLoad.PowerDensity;
                var mechanicalDensity = pDLoad.MechanicalDensity;

                var lightingLoad = Math.Round(lightingDensity * mD_Space?.Area ?? 0, 2);
                var powerLoad = Math.Round(powerDensity * mD_Space?.Area ?? 0, 2);
                var mechanicalLoad = Math.Round(mechanicalDensity * mD_Space?.Area ?? 0, 2);

                row.Cells[LightingLoadDensity.Index].Value = lightingDensity;
                row.Cells[SmallPowerLoadDensity.Index].Value = powerDensity;
                row.Cells[MechLoadDensity.Index].Value = mechanicalDensity;

                // Set MD_DBSpace data and parameters
                if (mD_Space != null)
                {
                    mD_Space.SpatialFunction = sel;

                    mD_Space.LightingLoadDensity = lightingDensity;
                    mD_Space.SmallPowerLoadDensity = powerDensity;
                    mD_Space.MechLoadDensity = mechanicalDensity;

                    mD_Space.LightingLoad = lightingLoad;
                    mD_Space.SmallPowerLoad = powerLoad;
                    mD_Space.MechLoad = mechanicalLoad;

                    mD_Space.TotalSpaceLoad = lightingLoad + powerLoad + mechanicalLoad;

                    // Set space parameters is triggered in btn_Save

                }
            }

            RecalculateDataAndUpdateUI();

            lbl_TotalSpaceLoad.Text = $"Total: {this.SelectedDBZone.TotalLoad}";

            return;
        }

        private void btn_UnassignedSpaces_Click(object sender, EventArgs e)
        {
            using (var frmUassignedSpaces = new FrmSpaceList(_data))
            {
                frmUassignedSpaces.ShowDialog();

                if (frmUassignedSpaces.DialogResult == DialogResult.OK)
                {
                    foreach (var space in frmUassignedSpaces.SelectedSpaces)
                    {
                        var mD_Space = new MD_SpaceModel(space, _data);
                        // Add space to db zone
                        SelectedDBZone.Spaces.Add(new MD_SpaceModel(space, _data));
                        //Remove space from orphaned spaces
                        _data.OrphanedSpaces.Remove(space);
                    }

                    eadgv_ConfigureSpaces.Rows.Clear();

                    RecalculateDataAndUpdateUI();
                }
            }
        }

        private void dgv_ConfigureGeneratorAndUPS_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            CellValueErrorHandler(e, dgv_ConfigureDBs);
        }

        private void CellValueErrorHandler(DataGridViewDataErrorEventArgs e, DataGridView dgv)
        {
            if (e.Exception.Message == "DataGridViewComboBoxCell value is not valid.")
            {
                object value = dgv[e.ColumnIndex, e.RowIndex].Value;
                if (!dgv.Columns[e.ColumnIndex].Name.Contains(value.ToString()))
                {
                    dgv.Columns[e.ColumnIndex].Name = value.ToString();
                    e.ThrowException = false;
                }
            }
        }

        private void eadgv_ConfigureSpaces_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            // Ensure we're not processing during initial load
            if (_isdgv_ConfigureSpaces_CellValueChanging || e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            try
            {
                _isdgv_ConfigureSpaces_CellValueChanging = true;

                DataGridView grid = sender as DataGridView;

                // Check if the changed cell is in the relevant columns
                if (e.ColumnIndex == grid.Columns[SinglePhaseLumpLoad.Index].Index ||
                    e.ColumnIndex == grid.Columns[ThreePhaseLumpLoad.Index].Index ||
                    e.ColumnIndex == grid.Columns[LumpLoadDiversity.Index].Index)
                {
                    // Get the current row
                    DataGridViewRow row = grid.Rows[e.RowIndex];

                    // Get the current MD_Space
                    var mD_Space = SelectedDBZone.Spaces.Find(s => s.SpaceName.Equals(row.Cells[SpaceName.Index].Value.ToString()));

                    if (mD_Space != null)
                    {
                        // Try to retrieve the value of the changed cell
                        double cellValue;
                        bool cellValueParsed = double.TryParse(row.Cells[e.ColumnIndex].Value?.ToString().TrimEnd('%'), out cellValue);

                        if (cellValueParsed)
                        {
                            // Get the column name from the DataGridView
                            string columnName = grid.Columns[e.ColumnIndex].Name;

                            // Use reflection to find and set the matching property on the MD_Space object
                            var property = typeof(MD_SpaceModel).GetProperty(columnName);

                            if (property != null && property.CanWrite && property.PropertyType == typeof(double))
                            {
                                if (e.ColumnIndex == grid.Columns[LumpLoadDiversity.Index].Index)
                                {
                                    // Validate value
                                    if (cellValue > 100)
                                    {
                                        MessageBox.Show($"Please enter a value for {property.Name} between 0 and 100.");
                                        cellValue = 0;
                                        row.Cells[e.ColumnIndex].Value = 0;
                                    }
                                    else
                                    {
                                        cellValue = cellValue / 100;
                                    }
                                }

                                // Set the value of the property to the parsed cell value
                                property.SetValue(mD_Space, cellValue);
                            }
                        }
                        else
                        {
                            // If parsing failed, clear the calculation cells
                            row.Cells[SmallPowerLoad.Index].Value = DBNull.Value;
                            row.Cells[LightingLoad.Index].Value = DBNull.Value;
                            row.Cells[MechLoad.Index].Value = DBNull.Value;
                            row.Cells[LumpLoad.Index].Value = DBNull.Value;
                            row.Cells[CalculatedDiversity.Index].Value = DBNull.Value;
                            row.Cells[DiversifiedLoadKVA.Index].Value = DBNull.Value;
                            row.Cells[CalculatedPowerFactor.Index].Value = DBNull.Value;
                            row.Cells[DiversifiedLoadKW.Index].Value = DBNull.Value;
                            row.Cells[DiversifiedCurrent.Index].Value = DBNull.Value;
                        }

                        // Update Total
                        double total = SelectedDBZone.Spaces.Sum(s => s.DiversifiedLoad_kVA);

                        // Update label text
                        lbl_TotalSpaceLoad.Text = total > 0.0 ? $"Total: {Math.Round(total, 2)} KVA" : "Total:";

                        RecalculateDataAndUpdateUI();
                    }
                }
            }
            finally
            {
                _isdgv_ConfigureSpaces_CellValueChanging = false;
            }
        }

        private void UpdateTotalAndWeightedAverageRow()
        {
            var lastRowIndex = eadgv_ConfigureSpaces.Rows.GetLastRow(DataGridViewElementStates.None);

            if (lastRowIndex != -1 && eadgv_ConfigureSpaces.Rows[lastRowIndex] != null)
            {
                eadgv_ConfigureSpaces[Area.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.Area).Sum(), 2);
                eadgv_ConfigureSpaces[Area.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.Area).Sum(), 2);
                eadgv_ConfigureSpaces[SmallPowerLoadDensity.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.SmallPowerLoadDensity).Average(), 2);
                eadgv_ConfigureSpaces[LightingLoadDensity.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.LightingLoadDensity).Average(), 2);
                eadgv_ConfigureSpaces[MechLoadDensity.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.MechLoadDensity).Average(), 2);
                eadgv_ConfigureSpaces[SinglePhaseLumpLoad.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.SinglePhaseLumpLoad).Sum(), 2);
                eadgv_ConfigureSpaces[ThreePhaseLumpLoad.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.ThreePhaseLumpLoad).Sum(), 2);
                eadgv_ConfigureSpaces[LumpLoadDiversity.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.TotalLumpLoadWeightedAverage, 2);
                eadgv_ConfigureSpaces[SmallPowerLoad.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.SmallPowerLoad).Sum(), 2);
                eadgv_ConfigureSpaces[LightingLoad.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.LightingLoad).Sum(), 2);
                eadgv_ConfigureSpaces[MechLoad.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.MechLoad).Sum(), 2);
                eadgv_ConfigureSpaces[LumpLoad.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.LumpLoad).Sum(), 2);
                eadgv_ConfigureSpaces[CalculatedDiversity.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.CalculatedDiversityWeightedAverage, 2);
                eadgv_ConfigureSpaces[DiversifiedLoadKVA.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.DiversifiedLoad_kVA).Sum(), 2);
                eadgv_ConfigureSpaces[CalculatedPowerFactor.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.TotalPowerFactorWeightedAverage, 2);
                eadgv_ConfigureSpaces[DiversifiedLoadKW.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.DiversifiedLoad_kW).Sum(), 2);
                eadgv_ConfigureSpaces[DiversifiedCurrentSpace.Index, lastRowIndex].Value = Math.Round(SelectedDBZone.Spaces.Select(a => a.DiversifiedCurrent_A).Sum(), 2);

                eadgv_ConfigureSpaces.Refresh();
            }
            
        }

        private void dgv_ConfigureDBs_ColumnWidthChanged(object sender, DataGridViewColumnEventArgs e)
        {
            UIHelper.AlignTableLayoutPanelWithDataGridView(tlp_ConfigureDBs, dgv_ConfigureDBs);
        }

        private void tc_Configure_TabSelectedIndexChanged(object sender, EventArgs e)
        {
            if (sender is TabControl tabControl)
            {
                // Get the currently selected tab page
                TabPage selectedTab = tabControl.SelectedTab;

                if (selectedTab != null)
                {
                    // Check if the selected tab is NOT "A"
                    if (selectedTab.Name != "tp_ConfigureSpaces")
                    {
                        btn_UnassignedSpaces.Visible = false;
                    }
                    else
                    {
                        btn_UnassignedSpaces.Visible = true;
                    }
                }
            }
        }

        private void dgv_ConfigureDBs_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            // Ensure we're not processing during initial load
            if (_isdgv_ConfigureDBs_CellValueChanging || e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            try
            {
                _isdgv_ConfigureDBs_CellValueChanging = true;

                DataGridView grid = sender as DataGridView; //DataGridViewComboBoxColumn // dgv_ConfigureDBs.Rows[e.RowIndex].Cells[e.ColumnIndex] is DataGridViewCheckBoxCell

                // Check if the changed cell is in the relevant columns
                if (e.ColumnIndex == grid.Columns[PowerSupplyClass.Index].Index ||
                    e.ColumnIndex == grid.Columns[SmallPowerGEU.Index].Index ||
                    e.ColumnIndex == grid.Columns[LightingGEU.Index].Index ||
                    e.ColumnIndex == grid.Columns[MechGEU.Index].Index ||
                    e.ColumnIndex == grid.Columns[LumpGEU.Index].Index)
                {
                    // Get the current row
                    DataGridViewRow row = grid.Rows[e.RowIndex];

                    // Get the current DB
                    var mD_DB = SelectedDBZone.DBs.Find(db => db.DB.Name.Equals(row.Cells[DBName.Index].Value.ToString()));

                    if (mD_DB != null)
                    {
                        if (e.ColumnIndex == SmallPowerGEU.Index)
                        {
                            mD_DB.IsSmallPower = Convert.ToBoolean(row.Cells[e.ColumnIndex].Value);
                        }
                        else if (e.ColumnIndex == LightingGEU.Index)
                        {
                            mD_DB.IsLighting = Convert.ToBoolean(row.Cells[e.ColumnIndex].Value);
                        }
                        else if (e.ColumnIndex == MechGEU.Index)
                        {
                            mD_DB.IsMech = Convert.ToBoolean(row.Cells[e.ColumnIndex].Value);
                        }
                        else if (e.ColumnIndex == LumpGEU.Index)
                        {
                            mD_DB.IsLump = Convert.ToBoolean(row.Cells[e.ColumnIndex].Value);
                        }
                        else if (e.ColumnIndex == PowerSupplyClass.Index)
                        {
                            mD_DB.PowerSupplyClass = row.Cells[e.ColumnIndex].Value.ToString();
                        }
                        else
                        {
                            return;
                        }
                    }

                    RecalculateDataAndUpdateUI();
                }
            }
            finally
            {
                _isdgv_ConfigureDBs_CellValueChanging = false;
            }
        }

        private void dgv_ConfigureDBs_CurrentCellDirtyStateChanged(object sender, EventArgs e)
        {
            if (dgv_ConfigureDBs.IsCurrentCellDirty &&
                (dgv_ConfigureDBs.CurrentCell is DataGridViewCheckBoxCell || dgv_ConfigureDBs.CurrentCell is DataGridViewComboBoxCell))
            {
                dgv_ConfigureDBs.CommitEdit(DataGridViewDataErrorContexts.Commit);
            }
        }
    }

    class ZoneSpace
    {
        public string ZoneName { get; set; }
        public string SpaceName { get; set; }
    }

    #region Classes to be Created

    #region Request

    /// <summary>
    ///   A list of requests the dialog has available
    /// </summary>
    /// 
    public enum EditFormRequestId : int
    {
        None,
    }

    /// <summary>
    ///   A class around a variable holding the current request.
    /// </summary>
    /// <remarks>
    ///   Access to it is made thread-safe, even though we don't necessarily
    ///   need it if we always disable the dialog between individual requests.
    /// </remarks>
    /// 
    public class EditForm_Request
    {
        // Storing the value as a plain Int makes using the interlocking mechanism simpler
        private int m_request = (int)EditFormRequestId.None;

        /// <summary>
        ///   Take - The Idling handler calls this to obtain the latest request. 
        /// </summary>
        /// <remarks>
        ///   This is not a getter! It takes the request and replaces it
        ///   with 'None' to indicate that the request has been "passed on".
        /// </remarks>
        /// 
        public EditFormRequestId Take()
        {
            return (EditFormRequestId)Interlocked.Exchange(ref m_request, (int)EditFormRequestId.None);
        }

        /// <summary>
        ///   Make - The Dialog calls this when the user presses a command button there. 
        /// </summary>
        /// <remarks>
        ///   It replaces any older request previously made.
        /// </remarks>
        /// 
        public void Make(EditFormRequestId request)
        {
            Interlocked.Exchange(ref m_request, (int)request);
        }
    }

    #endregion

    #region Request Handler

    /// <summary>
    ///   A class with methods to execute requests made by the dialog user.
    /// </summary>
    /// 
    public class EditForm_RequestHandler : IExternalEventHandler
    {

        #region Fields

        BecaActivityLoggerData _logger;
        List<Element> _selectedElemnets;
        // The value of the latest request made by the modeless form 
        EditForm_Request _request = new EditForm_Request();

        #endregion

        #region Properties

        /// <summary>
        /// A public property to access the current request value
        /// </summary>
        public EditForm_Request Request
        {
            get { return _request; }
        }

        #endregion

        #region Constructors

        public EditForm_RequestHandler(BecaActivityLoggerData logger)
        {
            _logger = logger;
        }

        #endregion

        #region Methods

        #region IExternalEventHandler Methods

        /// <summary>
        ///   A method to identify this External Event Handler
        /// </summary>
        public String GetName()
        {
            return "change to your name";
        }


        /// <summary>
        ///   The top method of the event handler.
        /// </summary>
        /// <remarks>
        ///   This is called by Revit after the corresponding
        ///   external event was raised (by the modeless form)
        ///   and Revit reached the time at which it could call
        ///   the event's handler (i.e. this object)
        /// </remarks>
        /// <returns>Status</returns>
        public void Execute(UIApplication uiapp)
        {
            try
            {
                switch (Request.Take())
                {
                    case EditFormRequestId.None:
                        {
                            return;  // no request at this time -> we can leave immediately
                        }

                    default:
                        {
                            // some kind of a warning here should
                            // notify us about an unexpected request 
                            break;
                        }
                }
            }
            finally
            {
                EditForm_ModelessHandler.WakeFormUp();
            }

            return;
        }

        #endregion

        #region Logic Helpers

        #endregion

        #endregion

    }

    #endregion

    #region Modeless Form Handler 

    public class EditForm_ModelessHandler
    {
        #region Fields

        public static FrmModelessMaxDemand _frmEditMaxDemand; 

        #endregion

        #region Properties

        public static FrmModelessMaxDemand FrmEditMaxDemand { get => _frmEditMaxDemand; }

        #endregion

        #region Methods

        /// <summary>
        ///   This method creates and shows a modeless dialog, unless it already exists.
        /// </summary>
        /// <remarks>
        ///   The external command invokes this on the end-user's request
        /// </remarks>
        /// 
        public static void ShowForm(UIApplication uiapp, BecaActivityLoggerData logger, MD_DataModel data, FrmModelessMaxDemandMain mainForm)
        {
            //try
            //{
            //    // If we do not have a dialog yet, create and show it
            //    if (_frmEditMaxDemandn == null || _frmEditMaxDemandn.IsDisposed)
            //    {

            //        // A new handler to handle request posting by the dialog
            //        EditForm_RequestHandler handler = new EditForm_RequestHandler(logger);

            //        // External Event for the dialog to use (to post requests)
            //        ExternalEvent exEvent = ExternalEvent.Create(handler);

            //        // We give the objects to the new dialog;
            //        // The dialog becomes the owner responsible fore disposing them, eventually.
            //        _frmEditMaxDemandn = new FrmModelessMaxDemand(exEvent, handler, uiapp, data, mainForm);
            //        _frmEditMaxDemandn.Show();
            //    }
            //    else
            //        _frmEditMaxDemandn.Activate();
            //    //m_MyForm.BringToFront();
            //}
            //catch (Exception e)
            //{
            //    MessageBox.Show(e.ToString());
            //}
            
        }

        /// <summary>
        ///   Waking up the dialog from its waiting state.
        /// </summary>
        /// 
        public static void WakeFormUp()
        {
            if (_frmEditMaxDemand != null)
            {
                _frmEditMaxDemand.WakeUp();
            }
        }

        /// <summary>
        /// Must be called in OnShutdown(UIControlledApplication a) Event of the App command.
        /// </summary>
        public static void OnRevitShutDown()
        {
            if (_frmEditMaxDemand != null)
            {
                _frmEditMaxDemand.Close();
            }
        }

        public static void UpdateDBZoneEntity(Document doc, Entity entity, DataStorage dataStorage)
        {
            var mD_DBZone = _frmEditMaxDemand.MainForm.Data.Zones.Find(z => z.ZoneName.Equals(_frmEditMaxDemand.SelectedDBZone.ZoneName));
            using (Transaction trans = new Transaction(doc, "Create DataStorage"))
            {
                trans.Start();
                entity.Set(FieldNames.Name.ToString(), mD_DBZone.ZoneName);
                entity.Set(FieldNames.ZoneArea.ToString(), mD_DBZone.TotalArea);
                entity.Set(FieldNames.GeneratorLighting.ToString(), mD_DBZone.EssentialLightingLoadPercentage);
                entity.Set(FieldNames.GeneratorLump.ToString(), mD_DBZone.EssentialLumpLoadPercentage);
                entity.Set(FieldNames.GeneratorMechanical.ToString(), mD_DBZone.EssentialMechanicalLoadPercentage);
                entity.Set(FieldNames.GeneratorSmallPower.ToString(), mD_DBZone.EssentialSmallPowerLoadPercentage);
                entity.Set(FieldNames.UPSLump.ToString(), mD_DBZone.UninteruptableLumpLoadPercentage);
                entity.Set(FieldNames.UPSMechanical.ToString(), mD_DBZone.UninteruptableMechanicalLoadPercentage);
                entity.Set(FieldNames.UPSLighting.ToString(), mD_DBZone.UninteruptableLightingLoadPercentage);
                entity.Set(FieldNames.UPSSmallPower.ToString(), mD_DBZone.UninteruptableSmallPowerLoadPercentage);
                entity.Set(FieldNames.Spaces.ToString(), mD_DBZone.Spaces.Select(s => s.Id).ToList());
                entity.Set(FieldNames.DBs.ToString(), mD_DBZone.DBs.Select(db => db.DB.Id).ToList());

                dataStorage.SetEntity(entity);
                trans.Commit();
            }
        }

        public static void SaveSpaceDBParametersZoneEntities(Document doc)
        {
            var mDZone = _frmEditMaxDemand.MainForm.Data.Zones.Find(x => x.ZoneName.Equals(_frmEditMaxDemand.SelectedDBZone.ZoneName));
            var mD_Spaces = mDZone.Spaces;
            var mD_DBs = mDZone.DBs;
            var mDZoneEntity = DBZoneManager.GetDBZoneEntityByZoneName(doc, mDZone.DBZoneDataStorage, mDZone.ZoneName);

            using (Transaction trans = new Transaction(doc, "Save Space parameters"))
            {
                trans.Start();

                // Save Space data
                foreach (var mD_Space in mD_Spaces)
                {
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(mDZone.ZoneName);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Spatial_Function).Set(mD_Space.SpatialFunction);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Lighting_Power_Density).Set(mD_Space.LightingLoadDensity);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Small_Power_Density).Set(mD_Space.SmallPowerLoadDensity);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Mech_Power_Density).Set(mD_Space.MechLoadDensity);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_1Phase).Set(mD_Space.SinglePhaseLumpLoad);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_3Phase).Set(mD_Space.ThreePhaseLumpLoad);
                    mD_Space.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_Diversity).Set(mD_Space.LumpLoadDiversity);
                    mD_Space.Space.LookupParameter("Beca Comments").Set(mD_Space.Notes);
                }
                // Save DB data
                foreach (var mD_DB in mD_DBs)
                {
                    mD_DB.DB.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(mDZone.ZoneName);
                    mD_DB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Power_Supply_Class).Set(mD_DB.PowerSupplyClass);
                    mD_DB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Small_Power).Set(mD_DB.IsSmallPower ? 1 : 0);
                    mD_DB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Lighting).Set(mD_DB.IsLighting ? 1 : 0);
                    mD_DB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Mech).Set(mD_DB.IsMech ? 1 : 0);
                    mD_DB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Lump).Set(mD_DB.IsLump ? 1 : 0);
                    mD_DB.DB.LookupParameter("Beca Comments").Set(mD_DB.Notes);
                }

                // Save DataStorage Gen and UPS data
                mDZoneEntity?.Set(nameof(DBZoneEntity.GeneratorSmallPower), mDZone.EssentialSmallPowerLoadPercentage);
                mDZoneEntity?.Set(nameof(DBZoneEntity.GeneratorLighting), mDZone.EssentialLightingLoadPercentage);
                mDZoneEntity?.Set(nameof(DBZoneEntity.GeneratorMechanical), mDZone.EssentialMechanicalLoadPercentage);
                mDZoneEntity?.Set(nameof(DBZoneEntity.GeneratorLump), mDZone.EssentialLumpLoadPercentage);
                mDZoneEntity?.Set(nameof(DBZoneEntity.UPSSmallPower), mDZone.UninteruptableSmallPowerLoadPercentage);
                mDZoneEntity?.Set(nameof(DBZoneEntity.UPSLighting), mDZone.UninteruptableLightingLoadPercentage);
                mDZoneEntity?.Set(nameof(DBZoneEntity.UPSMechanical), mDZone.UninteruptableMechanicalLoadPercentage);
                mDZoneEntity?.Set(nameof(DBZoneEntity.UPSLump), mDZone.UninteruptableLumpLoadPercentage);

                mDZone.DBZoneDataStorage?.SetEntity(mDZoneEntity);

                trans.Commit();
            }
        }

        #endregion

    }

    #endregion

    #endregion

    public enum SpaceColumnIndex
    {
        Level = 2,
        SmallPowerLoadDensity = 5,
        LightingLoadDensity = 6,
        MechLoadDensity = 7,
        SinglePhaseLumpLoad = 8,
        ThreePhaseLumpLoad = 9,
        LumpLoadDiversity = 10,
        SmallPowerLoad = 11,
        LightingLoad = 12,
        MechLoad = 13,
        LumpLoad = 14,
        CalculatedDiversity = 15,
        DiversifiedLoadKVA = 16,
        CalculatedPowerFactor = 17,
        DiversifiedLoadKW = 18,
        DiversifiedCurrentSpace = 19
    }
}