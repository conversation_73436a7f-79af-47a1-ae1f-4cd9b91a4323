﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.MaxDemand.CoreLogic.MD_DataStorage
{
    public class DBZoneEntity
    {
        public string Name { get; set; }
        public double ZoneArea { get; set; }
        public int GeneratorSmallPower { get; set; }
        public int GeneratorLighting { get; set; }
        public int GeneratorMechanical { get; set; }
        public int GeneratorLump { get; set; }
        public int UPSSmallPower { get; set; }
        public int UPSLighting { get; set; }
        public int UPSMechanical { get; set; }
        public int UPSLump { get; set; }
        public double TotalLightingLoad { get; set; }
        public double TotalPowerLoad { get; set; }
        public double TotalMechLoad { get; set; }
        public double TotalLoad { get; set; }
        public double HVACTopologyLoadFactor {  get; set; } // from 04/08/2025 dev, this is storing DB Zone HVAC user input
        public int HVACMethodology { get; set; }
        public int HVACTopology { get; set; }
        public double DBZoneHVAC { get; set; }
        public string Comments { get; set; }

        public List<ElementId> DBs { get; set; }
        public List<ElementId> Spaces { get; set; }
    }
}
