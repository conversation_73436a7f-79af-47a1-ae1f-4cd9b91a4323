﻿using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using ClosedXML.Excel;
using Common.OpenXML;
using Common.Utilities;
using MEP.MaxDemand.CoreLogic.Calculation;
using MEP.MaxDemand.Models;
using MEP.MaxDemand.UI.View.ViewHandlers;
using MessageBox = System.Windows.MessageBox;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.MaxDemand.CoreLogic.Export
{
    public static class MD_FileExport
    {
        public static void ExportData(UIApplication uiapp, BecaActivityLoggerData logger, MD_DataModel data)
        {
            TaskDialog mainDialog = new TaskDialog("Maximum Demand");
            mainDialog.MainContent = "Where do you want to export?";
            mainDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink1, "Excel");
            mainDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink2, "Power CAD");
            mainDialog.CommonButtons = TaskDialogCommonButtons.Cancel;
            TaskDialogResult tResult = mainDialog.Show();

            var doc = uiapp.ActiveUIDocument.Document;
            string strOutputDir = MD_Constants.DefaultOutputPath;
            if (!Directory.Exists(strOutputDir += @"\Beca MEP Tools\")) Directory.CreateDirectory(strOutputDir);
            if (!Directory.Exists(strOutputDir += @"MD Reports\")) Directory.CreateDirectory(strOutputDir);
            if (!Directory.Exists(strOutputDir += doc.Title + @"\")) Directory.CreateDirectory(strOutputDir);

            switch (tResult)
            {
                case TaskDialogResult.Cancel:
                    // Bring the WPF window back to the front
                    ModelessMainWindowHandler.ShowForm(uiapp, logger, data);
                    break;
                case TaskDialogResult.CommandLink1:
                    MD_FileExport.ExportToExcel(doc, data, strOutputDir);
                    // Bring the WPF window back to the front
                    ModelessMainWindowHandler.ShowForm(uiapp, logger, data);
                    break;
                case TaskDialogResult.CommandLink2:
                    MD_FileExport.ExportToPowerCAD(doc, data, strOutputDir);
                    // Bring the WPF window back to the front
                    ModelessMainWindowHandler.ShowForm(uiapp, logger, data);
                    break;
            }
        }

        public static void ExportToExcel(Document doc, MD_DataModel data, string strOutputDir)
        {
            try
            {
                // Generate output filename
                string outputFilePath = strOutputDir + GenerateOutputFilename(doc);

                // Copying the template file to the output location.
                File.Copy(MD_Constants.Default_MaxDemandTemplatePath, outputFilePath);

                var workbook = new XLWorkbook(outputFilePath);
                var calculationWorksheet = workbook.Worksheet("Calculation");
                var instructionWorksheet = workbook.Worksheet("Instructions");
                var summaryWorksheet = workbook.Worksheet("Summary");

                using (workbook)
                {
                    var zones = data.Zones;
                    int sourceStartRow = 18; // Starting row of the source (Zone row)
                    int sourceEndRow = 19;   // Ending row of the source (DB/Space row)

                    // Write Default Diversities and Power Factors
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"C35", value: data.DiversityLighting);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"C36", value: data.DiversityPower);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"C37", value: data.DiversityMech);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"C38", value: data.DiversityLumpLoadDefault);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"E35", value: data.PowerFactorLighting);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"E36", value: data.PowerFactorPower);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"E37", value: data.PowerFactorMech);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"E38", value: data.PowerFactorLumpLoad);

                    // Write Job Details
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"C19", value: data.ProjInfo.get_Parameter(BuiltInParameter.PROJECT_NAME)?.AsString() ?? string.Empty);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"C20", value: data.ProjInfo.get_Parameter(BuiltInParameter.PROJECT_NUMBER)?.AsString() ?? string.Empty);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"C21", value: DateTime.Now.ToString("dd/MM/yy"));
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"C22", value: data.Engineer);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"C23", value: data.Verifier);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"C24", value: data.ReferenceDrawing);
                    ClosedXmlUtilities.SetValue(instructionWorksheet, $"C25", value: data.Revision);

                    // Write Site results
                    ClosedXmlUtilities.SetValue(summaryWorksheet, $"E6", value: data.SiteDiversity);
                    ClosedXmlUtilities.SetValue(summaryWorksheet, $"E7", value: data.SiteSpareCapacity);
                    ClosedXmlUtilities.SetValue(summaryWorksheet, $"E10", value: data.TransformerSizekVA);
                    ClosedXmlUtilities.SetValue(summaryWorksheet, $"E11", value: data.GeneratorSizekVA);
                    ClosedXmlUtilities.SetValue(summaryWorksheet, $"E12", value: data.UPSSizekVA);

                    // Get data summaries
                    var summary_MainSupplyDBs = data.Summary_MainSupplyDBs.Where(s => !s.IsTotal).ToList();
                    var summary_EssentialDBs = data.Summary_EssentialDBs.Where(s => !s.IsTotal).ToList();
                    var summary_UninteruptableDBs = data.Summary_UninteruptableDBs.Where(s => !s.IsTotal).ToList();
                    var summary_MainSupplyDBsTotal = data.Summary_MainSupplyDBs.Find(s => s.DBName == "Total :");
                    var summary_EssentialDBsTotal = data.Summary_EssentialDBs.Find(s => s.DBName == "Total :");
                    var summary_UninteruptableDBsTotal = data.Summary_UninteruptableDBs.Find(s => s.DBName == "Total :");

                    // Process SUMMARY OF RESULTS BY DISTRIBUTION BOARD
                    var targetRow = 7; // Row After General Distribution Boards header
                    var firstDBRow = 7;
                    var lastDBRow = 7;

                    // Summary of General DBs
                    var mainSupplyDBsCount = summary_MainSupplyDBs.Count();
                    if (mainSupplyDBsCount > 0)
                    {
                        for (int i = 0; i < mainSupplyDBsCount + 1; i++)
                        {
                            if (i == mainSupplyDBsCount)
                            {
                                lastDBRow = targetRow - 1;
                                continue;
                            }

                            WriteSummaryDBRows(summaryWorksheet, targetRow, summary_MainSupplyDBs[i].DBName, summary_MainSupplyDBs[i].DiversifiedPerPhaseCurrent_A, summary_MainSupplyDBs[i].AveragePowerFactor);
                            targetRow++;
                        }
                        WriteSummaryTotalRow(summaryWorksheet, targetRow, firstDBRow, lastDBRow, "G", false);
                    }
                    else
                    {
                        WriteSummaryTotalRow(summaryWorksheet, targetRow, firstDBRow, lastDBRow, "G", true);
                    }
                    targetRow++;

                    // Summary of Essential DBs
                    WriteDBHeaderMergedRow(summaryWorksheet, targetRow, "Essential Distribution Boards");
                    targetRow++;
                    firstDBRow = targetRow;
                    // Rows
                    var essentialDBsCount = summary_EssentialDBs.Count();
                    if (essentialDBsCount > 0)
                    {
                        for (int i = 0; i < essentialDBsCount + 1; i++)
                        {
                            if (i == essentialDBsCount)
                            {
                                lastDBRow = targetRow - 1;
                                continue;
                            }

                            WriteSummaryDBRows(summaryWorksheet, targetRow, summary_EssentialDBs[i].DBName, summary_EssentialDBs[i].DiversifiedPerPhaseCurrent_A, summary_EssentialDBs[i].AveragePowerFactor);
                            targetRow++;
                        }
                        WriteSummaryTotalRow(summaryWorksheet, targetRow, firstDBRow, lastDBRow, "E", false);
                    }    
                    else
                    {
                        WriteSummaryTotalRow(summaryWorksheet, targetRow, firstDBRow, lastDBRow, "E", true);
                    }
                    targetRow++;

                    // Summary of Uninteruptable DBs
                    WriteDBHeaderMergedRow(summaryWorksheet, targetRow, "Uninterruptable Distribution Boards");
                    targetRow++;
                    firstDBRow = targetRow;
                    // Rows
                    var uninteruptableDBsCount = summary_UninteruptableDBs.Count();
                    if (uninteruptableDBsCount > 0)
                    {
                        for (int i = 0; i < uninteruptableDBsCount + 1; i++)
                        {
                            if (i == uninteruptableDBsCount)
                            {
                                lastDBRow = targetRow - 1;
                                continue;
                            }

                            WriteSummaryDBRows(summaryWorksheet, targetRow, summary_UninteruptableDBs[i].DBName, summary_UninteruptableDBs[i].DiversifiedPerPhaseCurrent_A, summary_UninteruptableDBs[i].AveragePowerFactor);
                            targetRow++;
                        }
                        WriteSummaryTotalRow(summaryWorksheet, targetRow, firstDBRow, lastDBRow, "U", false);
                    }
                    else
                    {
                        WriteSummaryTotalRow(summaryWorksheet, targetRow, firstDBRow, lastDBRow, "U", true);
                    }
                        

                    // Process CALCULATION sheet
                    int currentTargetRow = 0;
                    // Prepare the rows based on the data
                    for (int i = 0; i < zones.Count; i++)
                    {
                        // Add rows for the first zone starting from row 20, which are DB/Space rows, as rows 18 and 19 already exist in the template
                        if (i == 0)
                        {
                            var targetStartRow = 20;

                            // Add rows after the first zone based on the largest count of items from either DBs or Spaces with it's SpatialEquipments
                            var numOfRowsAfterFirstZoneRow = Math.Max(zones[i].DBs.Count(), zones[i].Spaces.Count() + NumberOfSpatialEquipmentsInZone(zones[i]));
                            for (int j = 0; j <= numOfRowsAfterFirstZoneRow - 2; j++)
                            {
                                CopyRowsWithFormulas(calculationWorksheet, sourceEndRow, sourceEndRow, targetStartRow);
                                targetStartRow++;
                                currentTargetRow = targetStartRow;
                            }
                        }
                        // Add the rest of the rows
                        else
                        {
                            // Add empty rows between zones
                            currentTargetRow = currentTargetRow + 2;
                            // Add next zone row
                            CopyRowsWithFormulas(calculationWorksheet, sourceStartRow, sourceStartRow, currentTargetRow);
                            // Increment target row
                            currentTargetRow++;
                            // Add rows after the zone based on the largest count of items from either DBs or Spaces with it's SpatialEquipments
                            var numOfRowsAfterZoneRow = Math.Max(zones[i].DBs.Count(), zones[i].Spaces.Count() + NumberOfSpatialEquipmentsInZone(zones[i]));
                            for (int j = 0; j < numOfRowsAfterZoneRow; j++)
                            {
                                // Add the rest of db/space rows
                                CopyRowsWithFormulas(calculationWorksheet, sourceEndRow, sourceEndRow, currentTargetRow);
                                // Increment target row for the next DB/Space row
                                currentTargetRow++;
                            }
                        }
                    }

                    // Inserting values to the cells
                    var targetSetRow = 18;
                    var targerSpaceRow = 19;
                    var zoneIndex = 1; // This is for MD calc to work (it's a variable in excel formula)
                    foreach (var zone in data.Zones)
                    {
                        // Zone entry
                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"C{targetSetRow}", value: zone.ZoneName);
                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"AR{targetSetRow}", value: (double)zone.EssentialSmallPowerLoadPercentage / 100);
                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"AS{targetSetRow}", value: (double)zone.EssentialLightingLoadPercentage / 100);
                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"AT{targetSetRow}", value: (double)zone.EssentialMechanicalLoadPercentage / 100);
                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"AU{targetSetRow}", value: (double)zone.EssentialLumpLoadPercentage / 100);
                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"AV{targetSetRow}", value: (double)zone.UninteruptableSmallPowerLoadPercentage / 100);
                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"AW{targetSetRow}", value: (double)zone.UninteruptableLightingLoadPercentage / 100);
                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"AX{targetSetRow}", value: (double)zone.UninteruptableMechanicalLoadPercentage / 100);
                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"AY{targetSetRow}", value: (double)zone.UninteruptableLumpLoadPercentage / 100);
                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"CO{targetSetRow}", value: zoneIndex); // Zone number
                        targetSetRow++;

                        var numberOfSpatialEquipment = NumberOfSpatialEquipmentsInZone(zone);
                        var numOfRowsAfterZoneRow = Math.Max(zone.DBs.Count, zone.Spaces.Count() + numberOfSpatialEquipment);

                        for (int i = 0; i <= numOfRowsAfterZoneRow; i++)
                        {
                            // DB entry
                            if (i < zone.DBs.Count)
                            {
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"C{targetSetRow}", value: zone.DBs[i].DB.Name);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"D{targetSetRow}", value: zone.DBs[i].PowerSupplyClass);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"E{targetSetRow}", value: zone.DBs[i].SmallPowerSymbol);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"F{targetSetRow}", value: zone.DBs[i].LightingSymbol);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"G{targetSetRow}", value: zone.DBs[i].MechSymbol);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"H{targetSetRow}", value: zone.DBs[i].LumpSymbol);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"L{targetSetRow}", value: zone.DBs[i].DBCableLength);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"M{targetSetRow}", value: zone.DBs[i].ParentDB?.Name ?? string.Empty);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"CM{targetSetRow}", value: zoneIndex); // DB's Zone number
                            }
                            // Clear the cells if no data is present
                            else
                            {
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"C{targetSetRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"D{targetSetRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"E{targetSetRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"F{targetSetRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"G{targetSetRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"H{targetSetRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"I{targetSetRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"J{targetSetRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"L{targetSetRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"M{targetSetRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"O{targetSetRow}");
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"CM{targetSetRow}", value: 0); // DB's Zone number
                            }

                            //Space entry
                            if (i < zone.Spaces.Count)
                            {
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"R{targerSpaceRow}", value: zone.Spaces[i].SpaceName);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"S{targerSpaceRow}", value: zone.Spaces[i].Level.Name);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"T{targerSpaceRow}", value: zone.Spaces[i].Area);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"U{targerSpaceRow}", value: zone.Spaces[i].SpatialFunction);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"Z{targerSpaceRow}", value: zone.Spaces[i].SinglePhaseLumpLoad);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"AA{targerSpaceRow}", value: zone.Spaces[i].ThreePhaseLumpLoad);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"AB{targerSpaceRow}", value: zone.Spaces[i].LumpLoadDiversity);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"AC{targerSpaceRow}", value: zone.Spaces[i].LumpLoadPowerFactor);
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"CN{targerSpaceRow}", value: zoneIndex); // Space's Zone number

                                // SpatialEquipment entry
                                if (zone.Spaces[i].SpatialEquipments != null && zone.Spaces[i].SpatialEquipments.Count() > 0)
                                {
                                    for (int j = 0; j < zone.Spaces[i].SpatialEquipments.Count(); j++)
                                    {
                                        targerSpaceRow++;

                                        // Set spatial equipment cells to light gray
                                        var range = calculationWorksheet.Range($"Z{targerSpaceRow}:AC{targerSpaceRow}");
                                        range.Style.Font.FontColor = XLColor.LightSlateGray;

                                        // Set values from data
                                        var spatialEquipment = zone.Spaces[i].SpatialEquipments[j];
                                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"R{targerSpaceRow}", value: " > " + spatialEquipment.EquipmentName);
                                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"Z{targerSpaceRow}", value: spatialEquipment.SinglePhaseLumpLoad);
                                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"AA{targerSpaceRow}", value: spatialEquipment.ThreePhaseLumpLoad);
                                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"AB{targerSpaceRow}", value: spatialEquipment.LumpLoadDiversity);
                                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"AC{targerSpaceRow}", value: spatialEquipment.LumpLoadPowerFactor);
                                        ClosedXmlUtilities.SetValue(calculationWorksheet, $"CN{targerSpaceRow}", value: 0); // Space's Zone number 0 because it's not included in the space calculation in excel formula
                                    }
                                }

                            }
                            // Clear the cells if no data is present
                            else if (i > zone.Spaces.Count + numberOfSpatialEquipment)
                            {
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"R{targerSpaceRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"S{targerSpaceRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"T{targerSpaceRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"U{targerSpaceRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"V{targerSpaceRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"W{targerSpaceRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"X{targerSpaceRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"Z{targerSpaceRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"AA{targerSpaceRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"AB{targerSpaceRow}");
                                ClosedXmlUtilities.ClearCell(calculationWorksheet, $"AC{targerSpaceRow}");
                                ClosedXmlUtilities.SetValue(calculationWorksheet, $"CN{targerSpaceRow}", value: 0); // Space's Zone number
                            }
                            // Increment target row for the next DB/Space row
                            targetSetRow++;
                            targerSpaceRow++;
                        }

                        // Add empty row between zones
                        targetSetRow = targetSetRow + 1;
                        targerSpaceRow = targetSetRow + 1;
                        zoneIndex++;
                    }

                    workbook.Save();
                }

                MessageBox.Show("Excel Export Successfull :)", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
                DirectoryUtility.OpenFolder(strOutputDir.Replace(@"\\", @"\").TrimEnd('\\'));
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message, "Excel export error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private static int NumberOfSpatialEquipmentsInZone(MD_ZoneModel zone)
        {
            return zone?.Spaces?.Where(s => s.SpatialEquipments != null)?.SelectMany(s => s.SpatialEquipments)?.Count() ?? 0;
        }

        private static string GenerateOutputFilename(Document doc)
        {
            var strOutputFilename = doc.Title;
            return strOutputFilename += "_" + DateTime.Now.ToString("yyMMdd_HHmmss") + ".xlsx";
        }

        public static void CopyRowsWithFormulas(IXLWorksheet worksheet, int sourceStartRow, int sourceEndRow, int targetStartRow)
        {
            for (int i = 0; i <= (sourceEndRow - sourceStartRow); i++)
            {
                int sourceRow = sourceStartRow + i;
                int targetRow = targetStartRow + i;

                var sourceRowCells = worksheet.Row(sourceRow).Cells();
                foreach (var sourceCell in sourceRowCells)
                {
                    // Get the target cell
                    var targetCell = worksheet.Cell(targetRow, sourceCell.Address.ColumnNumber);

                    if (sourceCell.HasFormula)
                    {
                        // Adjust formula references manually for the new row
                        targetCell.FormulaA1 = AdjustFormulaForRow(sourceCell.FormulaA1, sourceRow, targetRow);
                    }
                    else
                    {
                        // Copy the value (static content)
                        targetCell.Value = sourceCell.Value;
                    }

                    // Copy formatting
                    targetCell.Style = sourceCell.Style;
                }
            }
        }

        public static void WriteDBHeaderMergedRow(IXLWorksheet worksheet, int targetRow, string title)
        {
            worksheet.Range($"G{targetRow}:K{targetRow}").Merge();
            worksheet.Cell($"G{targetRow}").Value = title;
            worksheet.Range($"G{targetRow}:K{targetRow}").Style = worksheet.Cell("G6").Style;
        }

        public static void WriteSummaryTotalRow(IXLWorksheet worksheet, int targetTotalRow, int startRowToCalculate, int endRowToCalculate, string gEU, bool noDbs)
        {
            var workbook = worksheet.Workbook;

            var demand_A_Name = $"demand_a_{gEU}";
            var demand_kVA_Name = $"demand_kva_{gEU}";
            var powerFactor_Name = $"power_factor_{gEU}";
            var demand_kW_Name = $"demand_kw_{gEU}";

            var total_Cell = worksheet.Cell($"G{targetTotalRow}");
            var demand_A_Cell = worksheet.Cell($"H{targetTotalRow}");
            var demand_kVA_Cell = worksheet.Cell($"I{targetTotalRow}");
            var powerFactor_Cell = worksheet.Cell($"J{targetTotalRow}");
            var demand_kW_Cell = worksheet.Cell($"K{targetTotalRow}");

            // Styles
            var range = worksheet.Range($"G{targetTotalRow}:K{targetTotalRow}");
            range.Style.Fill.BackgroundColor = XLColor.White;
            range.Style.Font.FontColor = XLColor.Black;
            range.Style.Font.Bold = true;
            range.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            range.Style.Border.BottomBorder = XLBorderStyleValues.Thin;
            range.Style.Border.TopBorder = XLBorderStyleValues.Thin;
            range.Style.Fill.BackgroundColor = XLColor.FromHtml("#F2F2F2");

            // Total text
            total_Cell.Value = "Total:";
            total_Cell.Style.Border.LeftBorder = XLBorderStyleValues.Thin;

            // Check names, remove when exists because it can't be overwritten in a cell
            if (workbook.DefinedNames.Contains(demand_A_Name))
                workbook.DefinedNames.Delete(demand_A_Name);
            if (workbook.DefinedNames.Contains(demand_kVA_Name))
                workbook.DefinedNames.Delete(demand_kVA_Name);
            if (workbook.DefinedNames.Contains(powerFactor_Name))
                workbook.DefinedNames.Delete(powerFactor_Name);
            if (workbook.DefinedNames.Contains(demand_kW_Name))
                workbook.DefinedNames.Delete(demand_kW_Name);

            // Total Diversified Per Phase Demand (A): Set format, formula, and name
            demand_A_Cell.Style.NumberFormat.Format = @"0.00 ""A"";[Red]-0.00 ""A"";0.00 ""A""";
            demand_A_Cell.AddToNamed(demand_A_Name);

            // Total Diversified Demand (kVA): Set format, formula, style, and name 
            demand_kVA_Cell.Style.NumberFormat.Format = @"0.00 ""kVA"";[Red]-0.00 ""kVA"";0.00 ""kVA""";
            demand_kVA_Cell.AddToNamed(demand_kVA_Name);
            demand_kW_Cell.Style.Border.BottomBorder = XLBorderStyleValues.Thin;

            // Total Average Power Factor: Set format, formula, and name
            powerFactor_Cell.Style.NumberFormat.Format = @"0.00;[Red]-0.00;";
            powerFactor_Cell.AddToNamed(powerFactor_Name);

            // Total Diversified Demand (kW): Set format, formula, style and name
            demand_kW_Cell.Style.NumberFormat.Format = @"0.00 ""kW"";[Red]-0.00 ""kW"";0.00 ""kW""";
            demand_kW_Cell.AddToNamed(demand_kW_Name);
            demand_kW_Cell.Style.Border.RightBorder = XLBorderStyleValues.Thin;

            // Set total formulas when there's any db
            if (noDbs)
            {
                demand_A_Cell.Value = 0;
                demand_kVA_Cell.Value = 0;
                powerFactor_Cell.Value = 0;
                demand_kW_Cell.Value = 0;
            }
            else
            {
                demand_A_Cell.FormulaA1 = $"SUM(H${startRowToCalculate}:H${endRowToCalculate})";
                demand_kVA_Cell.FormulaA1 = $"SUM(I${startRowToCalculate}:I${endRowToCalculate})";
                powerFactor_Cell.FormulaA1 = $"SUMPRODUCT(J${startRowToCalculate}:J${endRowToCalculate},I${startRowToCalculate}:I${endRowToCalculate})/SUM(I${startRowToCalculate}:I${endRowToCalculate})";
                demand_kW_Cell.FormulaA1 = $"SUM(K${startRowToCalculate}:K${endRowToCalculate})";
            }
        }

        public static void WriteSummaryDBRows(IXLWorksheet worksheet, int targetRow, string dBName, double diversifiedPerPhaseDemandA, double averagePowerFactor)
        {
            var dBName_Cell = worksheet.Cell($"G{targetRow}");
            var demand_A_Cell = worksheet.Cell($"H{targetRow}");
            var demand_kVA_Cell = worksheet.Cell($"I{targetRow}");
            var powerFactor_Cell = worksheet.Cell($"J{targetRow}");
            var demand_kW_Cell = worksheet.Cell($"K{targetRow}");

            // Unmerge when it's merged
            if (dBName_Cell.IsMerged())
                worksheet.Range($"G{targetRow}:K{targetRow}").Unmerge();

            // Set Range Styles
            var range = worksheet.Range($"G{targetRow}:K{targetRow}");
            range.Style.Fill.BackgroundColor = XLColor.White;
            range.Style.Font.FontColor = XLColor.Black;
            range.Style.Font.Bold = false;
            range.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

            // DB Name: Set value and style
            dBName_Cell.Value = dBName;
            dBName_Cell.Style.Border.LeftBorder = XLBorderStyleValues.Thin;
            dBName_Cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Left;

            // Diversified Per Phase Demand (A): Set value and format 
            demand_A_Cell.Value = diversifiedPerPhaseDemandA;
            demand_A_Cell.Style.NumberFormat.Format = @"0.00 ""A"";[Red]-0.00 ""A"";0.00 ""A""";

            // Diversified Demand (kVA): Set formula and format 
            demand_kVA_Cell.FormulaA1 = $"H{targetRow} * line_to_neutral_voltage * number_of_phases /1000";
            demand_kVA_Cell.Style.NumberFormat.Format = @"0.00 ""kVA"";[Red]-0.00 ""kVA"";0.00 ""kVA""";

            // Average Power Factor: Set value and format
            powerFactor_Cell.Value = averagePowerFactor;
            powerFactor_Cell.Style.NumberFormat.Format = @"0.00;[Red]-0.00;0.00";

            // Total Diversified Demand (kW): Set formula, style and format
            demand_kW_Cell.FormulaA1 = $"I{targetRow} * J{targetRow}";
            demand_kW_Cell.Style.NumberFormat.Format = @"0.00 ""kW"";[Red]-0.00 ""kW"";0.00 ""kW""";
            demand_kW_Cell.Style.Border.RightBorder = XLBorderStyleValues.Thin;
        }

        private static string AdjustFormulaForRow(string formula, int sourceRow, int targetRow)
        {
            int rowOffset = targetRow - sourceRow;

            // Simple regex to find row numbers in formulas (e.g., $A$1 or A1)
            var regex = new System.Text.RegularExpressions.Regex(@"([A-Z]+)(\d+)");
            return regex.Replace(formula, match =>
            {
                string column = match.Groups[1].Value; // Column part (e.g., A)
                int row = int.Parse(match.Groups[2].Value); // Row number (e.g., 1)
                int adjustedRow = row + rowOffset; // Adjust the row number
                return $"{column}{adjustedRow}"; // Return the adjusted reference
            });
        }

        public static void ExportToPowerCAD(Document doc, MD_DataModel data, string strOutputDir)
        {
            var dBs = data.Zones.SelectMany(z => z.DBs);

            var noParentDBs = dBs.Where(db => db.ParentDB == null).Select(db => db.DBName).ToList();

            if (noParentDBs.Any())
            {
                var sbNoParent = new StringBuilder();
                sbNoParent.AppendLine(string.Join(Environment.NewLine, noParentDBs));

                MessageBox.Show($"The following DB does not have a parent DB assigned.\nPlease circuit it correctly in Revit before running the PowerCAD export.\n\n{sbNoParent}", "Uncircuited DBs");
                return;
            }

            string csvOutputPath = Path.Combine(strOutputDir, $"PowerCAD Export {doc.Title} {DateTime.Now:yyMMdd_HHmmss}.csv");

            try
            {
                using (var writer = new StreamWriter(csvOutputPath, false, Encoding.Default))
                {
                    // Write CSV header
                    writer.WriteLine("Cable Reference,SWB From,SWB To,SWB Type,SWB Load,SWB Load Scope,SWB PF,Cable Length,Cable Size - Active conductors,Cable Size - Neutral conductors,Cable Size - Earthing conductor,Active Conductor material,# of Phases,Cable Type,Cable Insulation,Installation Method,Cable Additional De-rating,Switchgear Trip Unit Type,Switchgear Manufacturer,Bus Type,Bus/Chassis Rating (A),Upstream Diversity,Isolator Type,Isolator Rating (A),Protective Device Rating (A),Protective Device Manufacturer,Protective Device Type,Protective Device Model,Protective Device OCR/Trip Unit,Protective Device Trip Setting (A)");

                    // Check all parent DBs
                    var parentDBNames = MD_Helper.GetAllParentPanelNames(doc);
                    var commonCells = ",,,Accumulate,,1,,,,,,,,,,,,,,,,,,,,,,";
                    if (parentDBNames.Count > 1)
                    {
                        // Write the first row
                        writer.WriteLine($",(T1),MSB{commonCells}");

                        // Write MSB - Parent DB rows
                        foreach (var parentName in MD_Helper.GetAllParentPanelNames(doc))
                        {
                            writer.WriteLine($",MSB,{parentName}{commonCells}");
                        }
                    }
                    else if (parentDBNames.Count == 1)
                    {
                        // Write the first row
                        writer.WriteLine($",(T1),{parentDBNames.FirstOrDefault()}{commonCells}");
                    }

                    // Iterate through DBs and write data
                    foreach (var db in dBs)
                    {
                        var diversifiedPerPhaseCurrent = double.IsNaN(db.DiversifiedPerPhaseCurrent) ? 0 : db.DiversifiedPerPhaseCurrent;
                        var powerFactor = double.IsNaN(db.PowerFactor) ? 0 : db.PowerFactor;
                        var cableLength = db.DBCableLength == 0 ? "1" : db.DBCableLength.ToString("F2");

                        // Create formatted CSV row
                        var values = new List<string>
                        {
                            "",                                                       // Col 1 (Empty)
                            Quote(db.ParentDB?.Name ?? "MSB"),                        // Col 2 = parentDB
                            Quote(db.DB.Name),                                        // Col 3 = dBName
                            "",                                                       // Col 4 (Empty)
                            diversifiedPerPhaseCurrent.ToString("F2"),                // Col 5 = diversifiedCurrent, formatted to 2 decimal places
                            "Accumulate",                                             // Col 6 = "Accumulate"
                            powerFactor.ToString("F2"),                               // Col 7 = powerFactor, formatted to 2 decimal places
                            cableLength,                                              // Col 8 = cableLength, formatted to 2 decimal places
                            "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "" // Remaining empty columns
                        };

                        // Write line to CSV
                        writer.WriteLine(string.Join(",", values));
                    }
                }

                MessageBox.Show("PowerCAD Export Successful :)", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
                DirectoryUtility.OpenFolder(strOutputDir.Replace(@"\\", @"\").TrimEnd('\\'));
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString(), "Failed to export PowerCAD.", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private static string Quote(string value)
        {
            if (value == null) return "";
            // Escape double quotes and wrap in quotes
            return $"\"{value.Replace("\"", "\"\"")}\"";
        }
    }

}
