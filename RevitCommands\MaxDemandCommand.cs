﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.ExtensibleStorage;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaCommand;
using MEP.MaxDemand.CoreLogic;
using MEP.MaxDemand.CoreLogic.Calculation;
using MEP.MaxDemand.CoreLogic.Export;
using MEP.MaxDemand.CoreLogic.MD_DataStorage;
using MEP.MaxDemand.Models;
using MEP.MaxDemand.UI;
using MEP.MaxDemand.UI.View;
using MEP.MaxDemand.UI.View.ViewHandlers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Forms;
using MessageBox = System.Windows.MessageBox;

namespace MEP.MaxDemand.RevitCommands
{
    [Transaction(TransactionMode.Manual)]
    internal class MaxDemandCommand : BecaBaseCommand
    {
        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            var app = uiapp.Application;
            Document doc = uidoc.Document;

            // Important notice message
            using (var startForm = new StartMessageForm())
            {
                startForm.ShowDialog();
            }

            _taskLogger.PreTaskStart();

            #region MD Process
            // Check if DBs or spaces exists in the model
            var familyAndPartTipeDictionary = MD_Helper.GetFamilysAndPartType(doc);
            var dBs = MD_Helper.GetDBs(MD_Helper.GetElectricalEquipments(doc), familyAndPartTipeDictionary);
            var spaces = MD_Helper.GetSpaces(doc);
            if (!MD_Helper.ValidateCollection(dBs, "elctrical equipments") || !MD_Helper.ValidateCollection(spaces, "space"))
            {
                return Result.Cancelled;
            }

            // Check for duplicates
            if (MD_Helper.DuplicateSpacesOrDBsFound(dBs, spaces))
            {
                return Result.Cancelled;
            }

            // Check Parameters
            if (!new ParameterChecker().ParametersIsAllGood(doc, doc.Application, MD_Constants.SharedParameterPath + "\\Beca_77_ELEC_Shared_Parameters.txt", dBs))
                return Result.Failed;

            // Initialize schemas for DB Zone and Space
            DBZoneStorage.InitializeDBZoneStorageInstance(doc);
            SpaceStorage.InitializeSpaceStorageInstance(doc);

            // Initialize PD Lookup
            var lookupData = MD_Helper.GetPDLookUpDataFromCSV(doc);
            if (lookupData.Count == 0)
            {
                MessageBox.Show("PDLookupData is empty", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                _taskLogger.Log("PDLookupData is empty", LogType.Error);
                return Result.Failed;
            }

            // Check NZ Space Types
            if (!MD_Helper.AddSpaceTypesNZ(doc))
            {
                MessageBox.Show("Failed to add NZ Space Types.\nThe MD_SpatialFunction parameter will be used to assign the spatial function property.\nSynchronization with LPD cannot be performed in this session.",
                "Error", MessageBoxButton.OK, MessageBoxImage.Information);
                _taskLogger.Log("Fail to add NZ Space Types.", LogType.Error);
            }

            // Initialize MD_Data
            var data = new MD_DataModel(doc, MD_Helper.GetMD_DBs(dBs, familyAndPartTipeDictionary), spaces, lookupData);

            if (data == null)
            {
                MessageBox.Show("Max Demand data is returning null.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                _taskLogger.Log("Max Demand data is returning null", LogType.Error);
                return Result.Failed; 
            }

            // Calculate data
            MD_CoreCalculation.RecalculateData(data);

            // Open main form
            ModelessMainWindowHandler.ShowForm(uiapp, _taskLogger, data);

            #endregion

            _taskLogger.PostTaskEnd("Summary.");

            return Result.Succeeded;
        }

        public override string GetAddinAuthor()
        {
            return "Firza Utama";
        }

        public override string GetAddinName()
        {
            return AddinNames.MaxDemand.Value;
        }

        public override string GetCommandSubName()
        {
            return string.Empty;
        }
    }
}
