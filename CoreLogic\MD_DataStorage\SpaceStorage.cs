﻿using Autodesk.Revit.DB.ExtensibleStorage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.MaxDemand.CoreLogic.MD_DataStorage
{
    public class SpaceStorage
    {
        #region Fields
        string _spaceSchemaGUID = "2FA35113-EA0B-4DE5-98EE-FCF7C6E12057";
        string _spaceSchemaName = DBZoneManager.SpaceSchemaName;

        Document _doc;
        Schema _spaceSchema;

        #endregion

        #region Singleton
        private static SpaceStorage _instance;

        private SpaceStorage(Document activeDoc) 
        {
            _doc = activeDoc;
            _spaceSchema = DBZoneManager.GetSchemaByName(DBZoneManager.SpaceSchemaName);
            if (_spaceSchema == null)
            {
                CreateSpaceSchema(new Guid(_spaceSchemaGUID), _spaceSchemaName);
            }
        }

        /// <summary>
        /// Must be called outside transaction as if it was not created, it will create transaction and create new datastorage.
        /// </summary>
        /// <param name="activeDoc"></param>
        /// <returns></returns>
        public static SpaceStorage InitializeSpaceStorageInstance(Document activeDoc)
        {
            // Uses lazy initialization.
            // Note: this is not thread safe.
            if (_instance == null || _instance._doc.IsValidObject == false || _instance._doc.PathName != activeDoc.PathName)
            {
                _instance = new SpaceStorage(activeDoc);
            }
            return _instance;
        }

        #endregion

        private void CreateSpaceSchema(Guid guid, string schemaName)
        {
            using (var trans = new Transaction(_doc, "Create Space schema"))
            {
                trans.Start();

                // Build schema
                SchemaBuilder schemaBuilder = new SchemaBuilder(guid);

                // Access level
                schemaBuilder.SetReadAccessLevel(AccessLevel.Public);
                schemaBuilder.SetWriteAccessLevel(AccessLevel.Vendor);

                // Vendor id
                schemaBuilder.SetVendorId("Beca");

                // Schema info
                schemaBuilder.SetDocumentation("Maximum Demand Schema to store spatial equipment data as JSON.");

                // Schema name
                schemaBuilder.SetSchemaName(schemaName);

                // Add a field to hold the JSON string of equipment data
                schemaBuilder.AddSimpleField(DBZoneManager.SpaceEntityFieldName, typeof(string));

                // Add DB Zone fields
                schemaBuilder.AddSimpleField(nameof(DBZoneEntity.Name), typeof(string))
                             .SetDocumentation("Unique Name for the DB Zone");

                // Finish (register) the schema
                _spaceSchema = schemaBuilder.Finish();

                trans.Commit();
            }
        }
    }
}
