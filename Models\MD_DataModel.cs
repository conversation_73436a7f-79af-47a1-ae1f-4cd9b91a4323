using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.DB.Mechanical;
using BecaTransactionsNamesManager;
using Common.UI.Forms;
using System.Collections.Generic;
using System.Linq;
using Excel = Microsoft.Office.Interop.Excel;
using System;
using System.Windows.Forms;
using MEP.MaxDemand.CoreLogic.MD_DataStorage;
using Autodesk.Revit.DB.ExtensibleStorage;
using System.Windows.Controls;
using Nice3point.Revit.Extensions;
using System.Security.Cryptography;
using BecaRevitUtilities.ElementUtilities;
using MEP.MaxDemand.CoreLogic.Calculation;
using MEP.MaxDemand.CoreLogic;
using System.Collections.ObjectModel;
using Egor92.MvvmNavigation;
using Autodesk.Revit.DB.Analysis;
using CommunityToolkit.Mvvm.DependencyInjection;
using System.Security.Policy;
using System.Text;

namespace MEP.MaxDemand.Models
{
    public partial class MD_DataModel : ObservableObject
    {
        #region Fields

        Document _doc;
        List<Space> _spaces;
        List<MD_DBModel> _dBData;

        #endregion

        #region Properties
        public string JobName { get; set; }
        public string JobNumber { get; set; }
        public string Date { get; set; }

        public List<double> SiteSpareCapacityOptions { get; } = new List<double> { 0.1, 0.15, 0.2 };
        public List<double> SiteDiversityOptions { get; } = new List<double> { 0.6, 0.7, 0.8 };
        public List<string> PDLookupDataOptions { get; } = new List<string> { "PD - Beca" };

        public List<HVACLoadSpaceType> HVACSpaceTypes = new List<HVACLoadSpaceType>();

        // Input from Revit Project Info
        [ObservableProperty]
        private ProjectInfo projInfo;
        [ObservableProperty]
        private List<PDLoadRow> pDLookupData;
        [ObservableProperty]
        private LoadDensityValue loadDensityValue;

        [ObservableProperty]
        private bool projInfoIsLocked;
        [ObservableProperty]
        private string projInfoOwner;
        [ObservableProperty]
        private string engineer;
        [ObservableProperty]
        private string verifier;
        [ObservableProperty]
        private string referenceDrawing;
        [ObservableProperty]
        private string revision;
        [ObservableProperty]
        private double siteDiversity;
        [ObservableProperty]
        private double siteSpareCapacity;

        // Input from Revit Project Info
        [ObservableProperty]
        private double diversityLighting;
        [ObservableProperty]
        private double diversityMech;
        [ObservableProperty]
        private double diversityPower;
        [ObservableProperty]
        private double diversityLumpLoadDefault;
        [ObservableProperty]
        private double powerFactorLighting;
        [ObservableProperty]
        private double powerFactorPower;
        [ObservableProperty]
        private double powerFactorMech;
        [ObservableProperty]
        private double powerFactorLumpLoad;

        [ObservableProperty]
        private double lineToNeutralVoltage;
        [ObservableProperty]
        private int numberOfPhases;

        [ObservableProperty]
        private ObservableCollection<MD_ZoneModel> zones;
        [ObservableProperty]
        private ObservableCollection<MD_DBModel> orphanedDBs;
        [ObservableProperty]
        private ObservableCollection<MD_SpaceModel> orphanedSpaces;

        // Summary of results by Distribution Board
        [ObservableProperty]
        private List<Summary> summary_MainSupplyDBs;
        [ObservableProperty]
        private List<Summary> summary_EssentialDBs;
        [ObservableProperty]
        private List<Summary> summary_UninteruptableDBs;

        // Site Summary
        // Summary Values
        [ObservableProperty]
        private double transformerSizekVA;
        [ObservableProperty]
        private double generatorSizekVA;
        [ObservableProperty]
        private double uPSSizekVA;
        [ObservableProperty]
        private double averagePowerFactor;
        [ObservableProperty]
        private double totalArea;
        [ObservableProperty]
        private double averagePowerDensityWm2;
        // Overall Supply
        [ObservableProperty]
        private double overallSupplyTotalSiteDiversifiedLoadkVA;
        [ObservableProperty]
        private double overallSupplyTotalSiteDiversifiedLoadkW;
        [ObservableProperty]
        private double overallSupplyTotalSiteDiversifiedLoadkVAR;
        [ObservableProperty]
        private double overallSupplyTotalDiversifiedPerPhaseLoadA;
        // Mains Supply
        [ObservableProperty]
        private double mainsSupplyTotalSiteDiversifiedLoadkVA;
        [ObservableProperty]
        private double mainsSupplyTotalSiteDiversifiedLoadkW;
        [ObservableProperty]
        private double mainsSupplyTotalSiteDiversifiedLoadkVAR;
        [ObservableProperty]
        private double mainsSupplyTotalDiversifiedPerPhaseLoadA;
        // Generator Supply
        [ObservableProperty]
        private double generatorSupplyTotalSiteDiversifiedLoadkVA;
        [ObservableProperty]
        private double generatorSupplyTotalSiteDiversifiedLoadkW;
        [ObservableProperty]
        private double generatorSupplyTotalSiteDiversifiedLoadkVAR;
        [ObservableProperty]
        private double generatorSupplyTotalDiversifiedPerPhaseLoadA;
        // UPS Supply
        [ObservableProperty]
        private double uPSSupplyTotalSiteDiversifiedLoadkVA;
        [ObservableProperty]
        private double uPSSupplyTotalSiteDiversifiedLoadkW;
        [ObservableProperty]
        private double uPSSupplyTotalSiteDiversifiedLoadkVAR;
        [ObservableProperty]
        private double uPSSupplyTotalDiversifiedPerPhaseLoadA;



        #endregion

        #region Constructors
        public MD_DataModel(Document doc, List<MD_DBModel> dBs, List<Space> spaces, List<PDLoadRow> _pDLookupData)
        {
            _doc = doc;

            ProjInfo = doc.ProjectInformation;
            JobName = ProjInfo.get_Parameter(BuiltInParameter.PROJECT_NAME)?.AsString() ?? string.Empty;
            JobNumber = projInfo.get_Parameter(BuiltInParameter.PROJECT_NUMBER)?.AsString() ?? string.Empty;
            Date = DateTime.Now.ToString("dd/MM/yy");
            Engineer = ProjInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Engineer)?.AsString() ?? string.Empty;
            Verifier = ProjInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Verifier)?.AsString() ?? string.Empty;
            ReferenceDrawing = Properties.Settings.Default.ReferenceDrawing;
            Revision = Properties.Settings.Default.Revision;

            _dBData = dBs;
            _spaces = spaces;

            PDLookupData = _pDLookupData;

            Zones = new ObservableCollection<MD_ZoneModel>();
            OrphanedDBs = new ObservableCollection<MD_DBModel>();
            OrphanedSpaces = new ObservableCollection<MD_SpaceModel>();

            var distributionSystem = (dBs.Select(d => d.DB).FirstOrDefault().MEPModel as ElectricalEquipment).DistributionSystem;
            LineToNeutralVoltage = double.TryParse(distributionSystem.get_Parameter(BuiltInParameter.RBS_DISTRIBUTIONSYS_VLG_PARAM)?.AsValueString(), out double voltage) ? voltage : 0.0;
            NumberOfPhases = ConvertStringToInt(distributionSystem.get_Parameter(BuiltInParameter.RBS_DISTRIBUTIONSYS_PHASE_PARAM)?.AsValueString());

            LoadDiversityAndPowerFactorFromProjectProperties();

            ProcessZones();

            GetHVACSpaceTypes(_doc);
        }

        private void GetHVACSpaceTypes(Document doc)
        {
            // Clearing the list.
            HVACSpaceTypes.Clear();

            // Finding all HVACLoadSpaceTypes in the project and getting iterator.
            var fecSpaceTypes = new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_HVAC_Load_Space_Types);
            var feiSpaceTypes = fecSpaceTypes.GetElementIdIterator();
            feiSpaceTypes.Reset();

            // Iterating through the space types, adding them to the list as we go.
            while (feiSpaceTypes.MoveNext())
            {
                HVACLoadSpaceType h = doc.GetElement(feiSpaceTypes.Current) as HVACLoadSpaceType;
                HVACSpaceTypes.Add(h);
            }

            // Sorting the list.
            HVACSpaceTypes = HVACSpaceTypes.OrderBy(x => x.Name.ToString()).ToList();
        }

        #endregion

        #region Methods
        private void LoadDiversityAndPowerFactorFromProjectProperties()
        {
            var projectInfo = _doc.ProjectInformation;
            ProjInfoIsLocked = projectInfo.IsLocked(_doc);
            ProjInfoOwner = projectInfo.ElementOwner(_doc);
            SiteDiversity = GetProjectInfoParameterDoubleValue(projectInfo, MD_Constants.ProjectInfoParameterName_Site_Diversity);
            SiteSpareCapacity = GetProjectInfoParameterDoubleValue(projectInfo, MD_Constants.ProjectInfoParameterName_Site_Spare_Capacity);
            DiversityLighting = GetProjectInfoParameterDoubleValue(projectInfo, MD_Constants.ProjectInfoParameterName_Diversity_Lighting);
            DiversityMech = GetProjectInfoParameterDoubleValue(projectInfo, MD_Constants.ProjectInfoParameterName_Diversity_Mech);
            DiversityPower = GetProjectInfoParameterDoubleValue(projectInfo, MD_Constants.ProjectInfoParameterName_Diversity_Power);
            DiversityLumpLoadDefault = GetProjectInfoParameterDoubleValue(projectInfo, MD_Constants.ProjectInfoParameterName_Diversity_LumpLoad_Default);
            PowerFactorLighting = GetProjectInfoParameterDoubleValue(projectInfo, MD_Constants.ProjectInfoParameterName_PF_Lighting);
            PowerFactorPower = GetProjectInfoParameterDoubleValue(projectInfo, MD_Constants.ProjectInfoParameterName_PF_Power);
            PowerFactorMech = GetProjectInfoParameterDoubleValue(projectInfo, MD_Constants.ProjectInfoParameterName_PF_Mech);
            PowerFactorLumpLoad = GetProjectInfoParameterDoubleValue(projectInfo, MD_Constants.ProjectInfoParameterName_PF_Lump);
            // HVAC properties are now managed at the zone level, not data level
            // HVACMethodology = projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Beca_MD_HVAC_Method)?.AsInteger() ?? 0;
            // HVACTopology = projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Beca_MD_HVAC_Topology)?.AsInteger() ?? 0;
        }

        private string GetProjectInfoParameterStringValue(ProjectInfo projectInfo, string parameterName)
        {
            return projectInfo.LookupParameter(parameterName).AsValueString();
        }

        private double GetProjectInfoParameterDoubleValue(ProjectInfo projectInfo, string parameterName)
        {
            return projectInfo.LookupParameter(parameterName).AsDouble();
        }

        private void ProcessZones()
        {
            // Process DBs
            foreach (var paramGroup in _dBData.GroupBy(p => MD_Helper.GetDBZoneParameterValue(p.DB)))
            {
                foreach (var dB in paramGroup)
                {
                    var zoneParameterValue = MD_Helper.GetDBZoneParameterValue(dB.DB);
                    if (string.IsNullOrEmpty(zoneParameterValue))
                    {
                        OrphanedDBs.Add(dB);
                        continue;
                    }

                    var dbZone = Zones.ToList().Find(z => z.ZoneName == zoneParameterValue);
                    if (dbZone != null)
                    {
                        dbZone.DBs.Add(dB);
                    }
                    else
                    {
                        CreateZoneDb(dB, zoneParameterValue);
                    }
                }
            }

            // Process Spaces
            foreach (var paramGroup in _spaces.GroupBy(s => MD_Helper.GetDBZoneParameterValue(s)))
            {
                foreach (var space in paramGroup)
                {
                    var zoneParameterValue = MD_Helper.GetDBZoneParameterValue(space as Element);
                    if (string.IsNullOrEmpty(zoneParameterValue))
                    {
                        OrphanedSpaces.Add(new MD_SpaceModel(space, PDLookupData));
                        continue;
                    }

                    var dbZone = Zones.ToList().Find(z => z.ZoneName == zoneParameterValue);
                    if (dbZone != null)
                    {
                        dbZone.Spaces.Add(new MD_SpaceModel(space, PDLookupData));
                    }
                    else
                    {
                        CreateZoneSpace(space, zoneParameterValue);
                    }
                }
            }

            // Set Zone IsLocked
            foreach (MD_ZoneModel zone in Zones)
            {
                // Determine if the entire Zone is locked based on DBs or Spaces
                zone.IsLocked = zone.DBs.Any(db => db.IsLocked) || zone.Spaces.Any(space => space.IsLocked);

                if (!zone.IsLocked)
                {
                    zone.CurrentOwners = _doc.Application.Username;
                    continue;
                }

                // Collect unique owners for locked DBs
                var lockedDBOwners = zone.DBs
                    .Where(db => db.IsLocked)
                    .Select(db => db.CurrentOwner)
                    .Where(owner => !string.IsNullOrEmpty(owner)) // Exclude null or empty owners
                    .Distinct()
                    .ToList();

                // Collect unique owners for locked Spaces
                var lockedSpaceOwners = zone.Spaces
                    .Where(space => space.IsLocked)
                    .Select(space => space.CurrentOwner)
                    .Where(owner => !string.IsNullOrEmpty(owner)) // Exclude null or empty owners
                    .Distinct()
                    .ToList();

                // Format the CurrentOwner string for the Zone
                var currentOwners = new List<string>();

                if (lockedDBOwners.Count > 0)
                {
                    currentOwners.Add($"DBs locked by: {string.Join(", ", lockedDBOwners)}");
                }

                if (lockedSpaceOwners.Count > 0)
                {
                    currentOwners.Add($"Spaces locked by: {string.Join(", ", lockedSpaceOwners)}");
                }

                zone.CurrentOwners = string.Join("\n\n", currentOwners);
            }
               
        

            // Add DataStorage in DBZones if null
            if (Zones.Count > 0)
            {
                var sbFaultyEntity = new StringBuilder();

                // Use ToList() to create a copy of the collection to avoid "Collection was modified" exception
                // This is necessary because MD_Helper.MigrateDataOldSchema might modify the Zones collection
                foreach (var dBZone in Zones.ToList())
                {
                    var ds = DBZoneManager.GetDBZoneDataStorageByName(_doc, dBZone.ZoneName);
                    DataStorage dataStorage;

                    if (ds == null)
                    {
                        dataStorage = DBZoneManager.CreateZoneDataStorage(_doc, dBZone.ZoneName, dBZone);
                    }
                    else
                    {
                        dataStorage = ds;
                    }

                    var entity = DBZoneManager.GetDBZoneEntityByZoneName(_doc, dataStorage, dBZone.ZoneName);

                    if (entity == null)
{
    var dBZoneSchema = DBZoneManager.GetSchemaByName("MD_Zone_Schema");// old schema name
    var oldEntity = dataStorage.GetEntity(dBZoneSchema);

    if (oldEntity != null)
    {
        MD_Helper.MigrateDataOldSchema(Zones, dBZone);
        continue;
    }
    else
    {
        sbFaultyEntity.AppendLine(dBZone.ZoneName);
        continue;
    }
}

                    //dBZone.HVACTopologyLoadFactor = entity.Get<double>(FieldNames.HVACTopologyLoadFactor.ToString(), UnitTypeId.General); 
                    dBZone.DBZoneHVAC = entity.Get<double>(FieldNames.HVACTopologyLoadFactor.ToString(), UnitTypeId.General); // HVACTopologyLoadFactor is updated to store DBZoneHVAC
                    dBZone.HVACMethodology = entity.Get<int>(FieldNames.HVACMethodology.ToString());
                    dBZone.HVACTopology = entity.Get<int>(FieldNames.HVACTopology.ToString());
                    dBZone.EssentialLightingLoadPercentage = entity.Get<int>(FieldNames.GeneratorLighting.ToString());
                    dBZone.EssentialLumpLoadPercentage = entity.Get<int>(FieldNames.GeneratorLump.ToString());
                    dBZone.EssentialMechanicalLoadPercentage = entity.Get<int>(FieldNames.GeneratorMechanical.ToString());
                    dBZone.EssentialSmallPowerLoadPercentage = entity.Get<int>(FieldNames.GeneratorSmallPower.ToString());
                    dBZone.UninteruptableLumpLoadPercentage = entity.Get<int>(FieldNames.UPSLump.ToString());
                    dBZone.UninteruptableMechanicalLoadPercentage = entity.Get<int>(FieldNames.UPSMechanical.ToString());
                    dBZone.UninteruptableLightingLoadPercentage = entity.Get<int>(FieldNames.UPSLighting.ToString());
                    dBZone.UninteruptableSmallPowerLoadPercentage = entity.Get<int>(FieldNames.UPSSmallPower.ToString());

                    // Set Datastorage
                    dBZone.DBZoneDataStorage = dataStorage;
                }

                if (sbFaultyEntity.Length > 0)
                {
                    MessageBox.Show($"Something is wrong with these zones.\nThey will not be able to save their data:\n\nZone name:\n{sbFaultyEntity.ToString()}\n\nPlease recreate the zones.", "Null Entity", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                }
            }

            // Sort Zones
            Zones = new ObservableCollection<MD_ZoneModel>(Zones.OrderBy(z => z.ZoneName));

        }

        private void CreateZoneSpace(Space space, string zoneParameterValue)
        {
            var newZone = new MD_ZoneModel() { ZoneName = zoneParameterValue };
            newZone.Spaces.Add(new MD_SpaceModel(space, PDLookupData));
            Zones.Add(newZone);
        }

        private void CreateZoneDb(MD_DBModel dbData, string zoneParameterValue)
        {
            var newZone = new MD_ZoneModel() { ZoneName = zoneParameterValue };
            newZone.DBs.Add(dbData);
            Zones.Add(newZone);
        }

        private int ConvertStringToInt(string phaseValue)
        {
            if (string.IsNullOrEmpty(phaseValue))
                return 0;

            return phaseValue.ToLower() switch
            {
                "single" => 1,
                "three" => 3,
                _ => 0, // Default to 0 for unrecognized values
            };
        }


        #endregion

    }

    public class PDLoadRow
    {
        public string Code { get; set; }
        public string SpatialFunction { get; set; }
        public double LightingDensity { get; set; }
        public double PowerDensity { get; set; }
        public double MechanicalDensity { get; set; }
    }

    public class DBsAndCircuits
    {
        public Dictionary<string, FamilyInstance> DBNameDictionary { get; set; }
        public List<ElectricalSystem> AllCircuits { get; set; }
    }

    public class Summary
    {
        public string DBName { get; set; }
        public double DiversifiedPerPhaseCurrent_A { get; set; }
        public double TotaldiversifiedLoad_kVA { get; set; }
        public double AveragePowerFactor { get; set; }
        public double TotalDiversifiedLoad_kW { get; set; }
        public bool IsTotal { get; set; }
    }
}