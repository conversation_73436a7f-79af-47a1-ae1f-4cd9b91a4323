﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace MEP.MaxDemand.Converters
{
    public class RoundingConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double doubleValue)
            {
                // Default to 0 decimal places if parameter is not set or invalid
                int decimalPlaces = 0;
                if (parameter != null && int.TryParse(parameter.ToString(), out int parsedDecimalPlaces))
                {
                    decimalPlaces = parsedDecimalPlaces;
                }

                // Round to specified number of decimal places
                return Math.Round(doubleValue, decimalPlaces);
            }

            // If value is not a double, return the original value (or handle as needed)
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
