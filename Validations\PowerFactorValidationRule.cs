﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace MEP.MaxDemand.Validations
{
    public class PowerFactorValidationRule : ValidationRule
    {
        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            if (value is string stringValue && double.TryParse(stringValue, out double powerFactor))
            {
                if (powerFactor >= 0 && powerFactor <= 2)
                {
                    return ValidationResult.ValidResult;
                }
                return new ValidationResult(false, "Value must be between 0 and 2.");
            }
            return new ValidationResult(false, "Invalid input. Please enter a valid number.");
        }
    }
}
