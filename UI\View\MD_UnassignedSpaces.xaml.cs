﻿using BecaRevitUtilities;
using DocumentFormat.OpenXml.Spreadsheet;
using MEP.MaxDemand.Models;
using MEP.MaxDemand.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using DataGrid = System.Windows.Controls.DataGrid;
using MessageBox = System.Windows.MessageBox;

namespace MEP.MaxDemand.UI.View
{
    /// <summary>
    /// Interaction logic for MD_UnassignedSpaces.xaml
    /// </summary>
    public partial class MD_UnassignedSpaces : System.Windows.Controls.Page
    {
        private MD_ViewModel _viewModel;

        public MD_UnassignedSpaces(MD_ViewModel viewModel)
        {
            InitializeComponent();

            _viewModel = viewModel;
            DataContext = viewModel;
        }

        private void BackToZoneEdit_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.Navigate(new MD_ZoneEdit(_viewModel));
        }

        private void AddButton_Click(object sender, RoutedEventArgs e)
        {
            if (DataContext is MD_ViewModel viewModel)
            {
                var sb = new StringBuilder();
                

                var selectedItems = UnassignedSpacesDataGrid.SelectedItems;
                var selectedSpaces = new List<MD_SpaceModel>();
                foreach (var item in selectedItems)
                {
                    if (item is MD_SpaceModel space)
                    {
                        if (space.SpaceStatus == "Placed" && space.Area > 0)
                        {
                            selectedSpaces.Add(space);
                        }
                        else
                        {
                            sb.AppendLine($"Number: {space.SpaceNumber}, " +
                            $"Name: {space.SpaceName}, " +
                            $"Status: {space.SpaceStatus}, " +
                            $"Area: {Math.Round(RevitUnitConvertor.InternalToSquareMeters(space.Area), 2) + " m²"}");
                        }
                    }
                }

                if (sb.Length > 0)
                {
                    MessageBox.Show($"The following spaces will not be added to the list due to their status or area:\n\n{sb}");
                }

                viewModel.SelectedSpaces = selectedSpaces;

                NavigationService.Navigate(new MD_ZoneEdit(_viewModel));
            }
        }
    }
}
