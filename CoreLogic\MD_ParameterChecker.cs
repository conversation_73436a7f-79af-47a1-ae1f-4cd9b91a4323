﻿using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaRevitUtilities.ElementUtilities;
using Nice3point.Revit.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application = Autodesk.Revit.ApplicationServices.Application;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.MaxDemand.CoreLogic
{
    public class ParameterChecker
    {
        private readonly List<string> _mDSpaceAndDBParameterNames = new List<string>()
        {
            MD_Constants.SpaceDBParameterName_DB_Zone
        };

        private readonly List<string> _mDSpaceParameterNames = new List<string>()
        {
            MD_Constants.SpaceParameterName_Lighting_Power_Density,
            MD_Constants.SpaceParameterName_Small_Power_Density,
            MD_Constants.SpaceParameterName_Mech_Power_Density,
            MD_Constants.SpaceParameterName_Lump_Load_1Phase,
            MD_Constants.SpaceParameterName_Lump_Load_3Phase,
            MD_Constants.SpaceParameterName_Lump_Load_Diversity,
            MD_Constants.SpaceParameterName_Lump_Load_Power_Factor,
            MD_Constants.SpaceParameterName_Spatial_Function
        };

        private readonly List<string> _mDDBParameterNames = new List<string>()
        {
            MD_Constants.DBParameterName_MD_Lighting,
            MD_Constants.DBParameterName_MD_Lump,
            MD_Constants.DBParameterName_MD_Mech,
            MD_Constants.DBParameterName_MD_Power_Supply_Class,
            MD_Constants.DBParameterName_MD_Small_Power
        };

        private readonly List<string> _mDProjectInfoParameterNames = new List<string>()
        {
            MD_Constants.ProjectInfoParameterName_Site_Diversity,
            MD_Constants.ProjectInfoParameterName_Site_Spare_Capacity,
            MD_Constants.ProjectInfoParameterName_Diversity_Lighting,
            MD_Constants.ProjectInfoParameterName_Diversity_Mech,
            MD_Constants.ProjectInfoParameterName_Diversity_Power,
            MD_Constants.ProjectInfoParameterName_Diversity_LumpLoad_Default,
            MD_Constants.ProjectInfoParameterName_Load_Density_Data,
            MD_Constants.ProjectInfoParameterName_PF_Lighting,
            MD_Constants.ProjectInfoParameterName_PF_Power,
            MD_Constants.ProjectInfoParameterName_PF_Mech,
            MD_Constants.ProjectInfoParameterName_PF_Lump,
            MD_Constants.ProjectInfoParameterName_Beca_MD_HVAC_Method,
            MD_Constants.ProjectInfoParameterName_Beca_MD_HVAC_Topology
        };

        public bool ParametersIsAllGood(Document doc, Application app, string sharedParameterFilePath, List<FamilyInstance> dBs)
        {
            List<string> missingSpaceParameters = CheckCategoryParameters(doc, BuiltInCategory.OST_MEPSpaces, _mDSpaceParameterNames);
            List<string> missingDBParameters = CheckCategoryParameters(doc, BuiltInCategory.OST_ElectricalEquipment, _mDDBParameterNames);
            List<string> missingProjectInfoParameters = CheckProjectInfoParameters(doc, _mDProjectInfoParameterNames);
            List<string> missingSpaceAndDBParameters = CheckCategoryParameters(doc, BuiltInCategory.OST_MEPSpaces, _mDSpaceAndDBParameterNames)
                .Concat(CheckCategoryParameters(doc, BuiltInCategory.OST_ElectricalEquipment, _mDSpaceAndDBParameterNames)).Distinct().ToList();

            bool anyMissing = missingSpaceParameters.Any() || missingDBParameters.Any() || missingProjectInfoParameters.Any() || missingSpaceAndDBParameters.Any();

#if TargetYear2020 || TargetYear2021 || TargetYear2022
            BuiltInParameterGroup electricalGroup = BuiltInParameterGroup.PG_ELECTRICAL;
#else
            ForgeTypeId electricalGroup = GroupTypeId.Electrical;
#endif

            if (anyMissing)
            {
                TaskDialog mainDialog = new TaskDialog("Missing Parameters")
                {
                    MainInstruction = "The following parameters are missing:",
                    MainContent =
                        "Space Parameters:\n" + string.Join("\n", missingSpaceParameters) + "\n\n" +
                        "DB Parameters:\n" + string.Join("\n", missingDBParameters) + "\n\n" +
                        "Project Info Parameters:\n" + string.Join("\n", missingProjectInfoParameters) + "\n\n" +
                        "Space and DB Parameters:\n" + string.Join("\n", missingSpaceAndDBParameters) + "\n\n" +
                        "Do you want to create these parameters?",
                    CommonButtons = TaskDialogCommonButtons.Yes | TaskDialogCommonButtons.No,
                    DefaultButton = TaskDialogResult.Yes
                };

                TaskDialogResult result = mainDialog.Show();

                if (result == TaskDialogResult.Yes)
                {
                    bool success = true;

                    using (Transaction trans = new Transaction(doc, "Create Missing Parameters"))
                    {
                        trans.Start();

                        if (missingSpaceParameters.Any())
                        {
                            CategorySet categorySet = new CategorySet();
                            categorySet.Insert(doc.Settings.Categories.get_Item(BuiltInCategory.OST_MEPSpaces));

                            foreach (string paramName in missingSpaceParameters)
                            {
                                if (!AddProjectParameter(doc, app, paramName, categorySet, electricalGroup, true, sharedParameterFilePath))
                                {
                                    success = false;
                                    break;
                                }
                            }
                        }

                        if (missingDBParameters.Any())
                        {
                            CategorySet categorySet = new CategorySet();
                            categorySet.Insert(doc.Settings.Categories.get_Item(BuiltInCategory.OST_ElectricalEquipment));

                            foreach (string paramName in missingDBParameters)
                            {
                                if (!AddProjectParameter(doc, app, paramName, categorySet, electricalGroup, true, sharedParameterFilePath))
                                {
                                    success = false;
                                    break;
                                }
                            }
                        }

                        if (missingProjectInfoParameters.Any())
                        {
                            CategorySet categorySet = new CategorySet();
                            categorySet.Insert(doc.Settings.Categories.get_Item(BuiltInCategory.OST_ProjectInformation));

                            foreach (string paramName in missingProjectInfoParameters)
                            {
                                if (!AddProjectParameter(doc, app, paramName, categorySet, electricalGroup, true, sharedParameterFilePath))
                                {
                                    success = false;
                                    break;
                                }
                            }
                        }

                        if (missingSpaceAndDBParameters.Any())
                        {
                            CategorySet categorySet = new CategorySet();
                            categorySet.Insert(doc.Settings.Categories.get_Item(BuiltInCategory.OST_MEPSpaces));
                            categorySet.Insert(doc.Settings.Categories.get_Item(BuiltInCategory.OST_ElectricalEquipment));

                            foreach (string paramName in missingSpaceAndDBParameters)
                            {
                                if (!AddProjectParameter(doc, app, paramName, categorySet, electricalGroup, true, sharedParameterFilePath))
                                {
                                    success = false;
                                    break;
                                }
                            }
                        }

                        if (success)
                        {
                            trans.Commit();

                            SetParameterDefaultValues(doc, dBs);

                            MessageBox.Show("Missing parameters have been successfully created.", "Parameters Created", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return true;
                        }
                        else
                        {
                            trans.RollBack();
                            TaskDialog.Show("Error", "Failed to create some or all parameters.");
                            return false;
                        }
                    }
                }
                else
                {
                    return false; 
                }
            }
            else
            {
                return true; 
            }
        }


        private List<string> CheckCategoryParameters(Document doc, BuiltInCategory category, List<string> parameterNames)
        {
            List<string> missingParameters = new List<string>();

            Category cat = doc.Settings.Categories.get_Item(category);
            if (cat != null)
            {
                foreach (string paramName in parameterNames)
                {
                    bool paramExists = false;
                    foreach (Element element in new FilteredElementCollector(doc).OfCategoryId(cat.Id).WhereElementIsNotElementType())
                    {
                        Parameter param = element.LookupParameter(paramName);
                        if (param != null)
                        {
                            paramExists = true;
                            break;
                        }
                    }
                    if (!paramExists)
                    {
                        missingParameters.Add(paramName);
                    }
                }
            }

            return missingParameters;
        }

        private List<string> CheckProjectInfoParameters(Document doc, List<string> parameterNames)
        {
            List<string> missingParameters = new List<string>();

            ProjectInfo projectInfo = doc.ProjectInformation;
            if (projectInfo != null)
            {
                foreach (string paramName in parameterNames)
                {
                    Parameter param = projectInfo.LookupParameter(paramName);
                    if (param == null)
                    {
                        missingParameters.Add(paramName);
                    }
                }
            }

            return missingParameters;
        }
#if TargetYear2020 || TargetYear2021 || TargetYear2022
        public static bool AddProjectParameter(Document doc, Application app, string parameterName, CategorySet categorySet, BuiltInParameterGroup parameterGroup, bool instanceBinding, string sharedParameterFilePath)
#else
        public static bool AddProjectParameter(Document doc, Application app, string parameterName, CategorySet categorySet, ForgeTypeId parameterGroup, bool instanceBinding, string sharedParameterFilePath)
#endif
        {
            try
            {
                // Set the shared parameters file
                app.SharedParametersFilename = sharedParameterFilePath;
                DefinitionFile defFile = app.OpenSharedParameterFile();
                if (defFile == null)
                {
                    TaskDialog.Show("Project Parameter error", "Missing shared parameter file.");
                    return false;
                }

                // Find the external definition by parameter name
                var externalDefinition = (from DefinitionGroup dg in defFile.Groups
                                          from ExternalDefinition d in dg.Definitions
                                          where d.Name == parameterName
                                          select d).FirstOrDefault();

                if (externalDefinition == null)
                {
                    TaskDialog.Show("Project Parameter error", "Parameter not found in shared parameter file.");
                    return false;
                }

                // Create binding for the parameter
                Autodesk.Revit.DB.Binding binding = instanceBinding
                    ? app.Create.NewInstanceBinding(categorySet)
                    : app.Create.NewTypeBinding(categorySet);

                BindingMap map = doc.ParameterBindings;

                // Insert the parameter into the document
                if (!map.Insert(externalDefinition, binding, parameterGroup))
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                TaskDialog.Show("Error", $"Failed to add project parameter: {ex.Message}");
                return false;
            }
        }

        private void SetParameterDefaultValues(Document doc, List<FamilyInstance> dBs)
        {
            using (var trans = new Transaction(doc, "Set DB default parameters"))
            {
                trans.Start();
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Lighting)?.Set(1.0);
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Mech)?.Set(0.7);
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Power)?.Set(0.7);
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_LumpLoad_Default)?.Set(0.5);
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Lighting)?.Set(0.95);
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Power)?.Set(0.90);
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Mech)?.Set(0.7);
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Lump)?.Set(0.9);
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Site_Diversity)?.Set(0.8);
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Site_Spare_Capacity)?.Set(0.2);
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Beca_MD_HVAC_Method)?.Set(0);
                doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Beca_MD_HVAC_Topology)?.Set(0);

                foreach (var dB in dBs)
                {
                    if (!dB.IsLocked(doc))
                    {
                        dB.LookupParameter(MD_Constants.DBParameterName_MD_Lighting)?.Set(1);
                        dB.LookupParameter(MD_Constants.DBParameterName_MD_Lump)?.Set(1);
                        dB.LookupParameter(MD_Constants.DBParameterName_MD_Mech)?.Set(1);
                        dB.LookupParameter(MD_Constants.DBParameterName_MD_Small_Power)?.Set(1);
                    }
                }
                trans.Commit();

            }
        }
    }
}
