﻿using MEP.MaxDemand.CoreLogic.Calculation;
using MEP.MaxDemand.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Configuration;
using System.Windows.Forms;
using Color = System.Drawing.Color;
using Form = System.Windows.Forms.Form;

namespace MEP.MaxDemand.UI.Forms
{
    public partial class FrmSummary : Form
    {
        public FrmSummary(MD_DataModel data)
        {
            InitializeComponent();

            RecalculateAndPopulateSummary(data);
        }

        private void RecalculateAndPopulateSummary(MD_DataModel data)
        {
            MD_CalculationService.RecalculateData(data);

            foreach (var powerSupplyClassGroup in DBLevelCalculation.GroupDbsByPowerSupplyClass(data.Zones))
            {
                AddHeaderRow(powerSupplyClassGroup.Key);

                if (powerSupplyClassGroup != null || powerSupplyClassGroup.Count() > 0) 
                    PopulatePowerSupplyClassRows(powerSupplyClassGroup);

                AddTotalRow(powerSupplyClassGroup);
            }
        }

        private void AddHeaderRow(string powerSupplyClassGroup)
        {
            var headerRow = new DataGridViewRow();

            // Set the row style for ForeColor and BackColor
            var style = new DataGridViewCellStyle
            {
                ForeColor = Color.White,
                BackColor = Color.FromArgb(18, 168, 178),
                Font = new Font(eadgv_DbSummary.Font, FontStyle.Bold),
                Alignment = DataGridViewContentAlignment.MiddleCenter
            };

            headerRow.DefaultCellStyle = style;

            // Create cells for the row
            switch (powerSupplyClassGroup)
            {
                case "G":
                    headerRow.CreateCells(eadgv_DbSummary, "", "", "Mains Supply", "", "");
                    break;
                case "E":
                    headerRow.CreateCells(eadgv_DbSummary, "", "", "Generator (Essential) Supply", "", "");
                    break;
                case "U":
                    headerRow.CreateCells(eadgv_DbSummary, "", "", "UPS (Uninterruptable) Supply", "", "");
                    break;
                default:
                    break;
            }

            // Make the row cells read-only
            foreach (DataGridViewCell cell in headerRow.Cells)
            {
                cell.ReadOnly = true;
            }

            // Add the row to the DataGridView
            eadgv_DbSummary.Rows.Add(headerRow);
        }

        private void PopulatePowerSupplyClassRows(IGrouping<string, MD_DBModel> powerSupplyClassGroup)
        {
            foreach (var db in powerSupplyClassGroup)
            {
                eadgv_DbSummary.Rows.Add(
                    db.DB.Name,
                    $"{db.DiversifiedPerPhaseCurrent} A",
                    $"{db.DiversifiedLoad_kVA} kVA",
                    db.PowerFactor,
                    $"{db.DiversifiedLoad_kW} kW");
            }
        }

        private void AddTotalRow(IGrouping<string, MD_DBModel> powerSupplyClassGroup)
        {
            var totalDiversifiedCurrent = powerSupplyClassGroup.Sum(db => db.DiversifiedPerPhaseCurrent);
            var totalDiversifiedLoadKVA = powerSupplyClassGroup.Sum(db => db.DiversifiedLoad_kVA);
            var totalPowerFactor = powerSupplyClassGroup.Sum(db => db.PowerFactor);
            var totalDiversifiedLoadKW = powerSupplyClassGroup.Sum(db => db.DiversifiedLoad_kW);

            var newRowIndex = eadgv_DbSummary.Rows.Add(
                "Total:",
                $"{Math.Round(totalDiversifiedCurrent, 2)} A",
                $"{Math.Round(totalDiversifiedLoadKVA, 2)} kVA",
                Math.Round(totalPowerFactor, 2),
                $"{Math.Round(totalDiversifiedLoadKW, 2)} kW");

            eadgv_DbSummary.Rows[newRowIndex].Height = 30;
            eadgv_DbSummary.Rows[newRowIndex].DefaultCellStyle.Font = new Font(eadgv_DbSummary.Font, FontStyle.Bold);
        }
    }
}
