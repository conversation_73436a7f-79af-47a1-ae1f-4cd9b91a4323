﻿using Autodesk.Revit.UI.Selection;
using Autodesk.Revit.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.MaxDemand.UI.View
{
    public class ExternalEventHandler : IExternalEventHandler
    {
        public string Action { get; set; }
        public Document Document { get; set; }

        public void Execute(UIApplication app)
        {
            try
            {
                Document doc = app.ActiveUIDocument.Document;
                UIDocument uidoc = app.ActiveUIDocument;

                switch (Action)
                {
                    case "SelectElement":
                        Reference selectedRef = uidoc.Selection.PickObject(ObjectType.Element, "Select an element");
                        if (selectedRef != null)
                        {
                            Element element = doc.GetElement(selectedRef);
                            TaskDialog.Show("Selected Element", $"Selected Element ID: {element.Id}");
                        }
                        break;

                    case "RunTransaction":
                        using (Transaction t = new Transaction(doc, "Example Transaction"))
                        {
                            t.Start();
                            TaskDialog.Show("Transaction", "Transaction Started and Completed!");
                            t.Commit();
                        }
                        break;
                }
            }
            catch (OperationCanceledException)
            {
                // Handle when user cancels selection
            }
            catch (Exception ex)
            {
                TaskDialog.Show("Error", ex.Message);
            }
        }

        public string GetName()
        {
            return "Modeless WPF External Event Handler";
        }
    }
}
