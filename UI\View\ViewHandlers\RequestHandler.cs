﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using BecaActivityLogger.CoreLogic.Data;
using MEP.MaxDemand.CoreLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.MaxDemand.UI.View.ViewHandlers
{
    public class RequestHandler : IExternalEventHandler
    {
        #region Fields

        BecaActivityLoggerData _logger;

        // The value of the latest request made by the modeless form 
        RequestMaxDemandConfigure _request = new RequestMaxDemandConfigure();

        #endregion

        #region Properties

        /// <summary>
        /// A public property to access the current request value
        /// </summary>
        public RequestMaxDemandConfigure Request
        {
            get { return _request; }
        }

        #endregion

        #region Constructors

        public RequestHandler(BecaActivityLoggerData logger)
        {
            _logger = logger;
        }

        #endregion

        #region Methods

        #region IExternalEventHandler Methods

        /// <summary>
        ///   A method to identify this External Event Handler
        /// </summary>
        public String GetName()
        {
            return "Firza Utama";
        }


        /// <summary>
        ///   The top method of the event handler.
        /// </summary>
        /// <remarks>
        ///   This is called by Revit after the corresponding
        ///   external event was raised (by the modeless form)
        ///   and Revit reached the time at which it could call
        ///   the event's handler (i.e. this object)
        /// </remarks>
        /// <returns>Status</returns>
        public void Execute(UIApplication uiapp)
        {
            var doc = uiapp.ActiveUIDocument.Document;
            try
            {
                switch (Request.Take())
                {
                    case RequestId.SaveDataInMainForm:
                        {
                            ModelessMainWindowHandler.SaveDataInTheMainForm(doc);
                            break;
                        }
                    case RequestId.SaveProjectInfo:
                        {
                            ModelessMainWindowHandler.SaveProjectInfo(doc);
                            break;
                        }
                    case RequestId.SetLoadDensityData:
                        {
                            ModelessMainWindowHandler.SetLoadDensityData(doc);
                            break;
                        }
                    case RequestId.SaveSpaceDBParametersZoneEntities:
                        {
                            ModelessMainWindowHandler.SaveSpaceDBParametersZoneEntities(doc);
                            break;
                        }
                    case RequestId.CreateDataStorage:
                        {
                            ModelessMainWindowHandler.CreateNewDataStorage(doc);
                            break;
                        }
                    case RequestId.RenameDBZoneInDBsAndSpaces:
                        {
                            ModelessMainWindowHandler.RenameDBZoneInDBsAndSpaces(doc);
                            break;
                        }
                    case RequestId.ClearZonePrameterInDB:
                        {
                            ModelessMainWindowHandler.ClearZonePrameterInDB(doc);
                            break;
                        }
                    case RequestId.ClearDBZoneInDBsAndSpaces:
                        {
                            ModelessMainWindowHandler.ClearDBZoneParameterInDBsAndSpaces(doc);
                            break;
                        }
                    default:
                        {
                            // some kind of a warning here should
                            // notify us about an unexpected request 
                            break;
                        }
                }
            }
            finally
            {
                
            }

            return;
        }

        #endregion

        #endregion

    }
}
