﻿using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using CommunityToolkit.Mvvm.Messaging;
using MEP.MaxDemand.Models;
using MEP.MaxDemand.UI.View.ViewHandlers;
using MEP.MaxDemand.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using UIFramework;
using Wpf.Ui.Appearance;
using MessageBox = System.Windows.MessageBox;

namespace MEP.MaxDemand.UI.View
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MD_ViewModel ViewModel { get; private set; }

        public MainWindow(MD_ViewModel viewModel)
        {
            InitializeComponent();

            ViewModel = viewModel;
            MainFrame.Navigate(new MD_DataPage(ViewModel));
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            var childlessZone = ViewModel.Data.Zones.Count(z => z.DBs == null || !z.DBs.Any());
            if (childlessZone > 0)
            {
                // Show a message box to confirm if the user wants to close
                var result = MessageBox.Show(
                    $"There are {childlessZone} zone(s) with no DBs. Are you sure you want to close?",
                    "Confirm Close",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning
                );

                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                }
            }
            else
            {
                // Get the ViewModel from DataContext
                if (ViewModel.HasUnsavedChanges)
                {
                    var result = MessageBox.Show("Closing this window will result in losing all unsaved work.\nAre you sure you want to close?",
                             "Confirm Close",
                             MessageBoxButton.OKCancel,
                             MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Cancel)
                    {
                        e.Cancel = true;
                    }
                    return;
                }
            }
        }
    }
}
