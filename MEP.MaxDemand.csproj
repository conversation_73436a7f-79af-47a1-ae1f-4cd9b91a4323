﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <Configurations>Debug R20;Debug R21;Debug R22;Debug R23;Debug R24;Debug R25;Release R20;Release R21;Release R22;Release R23;Release R24;Release R25;Debug R26;Release R26</Configurations>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="CoreLogic\Export\Parameters\**" />
    <Compile Remove="UI\Forms\**" />
    <Compile Remove="UI\ModelessRevitForm\**" />
    <EmbeddedResource Remove="CoreLogic\Export\Parameters\**" />
    <EmbeddedResource Remove="UI\Forms\**" />
    <EmbeddedResource Remove="UI\ModelessRevitForm\**" />
    <None Remove="CoreLogic\Export\Parameters\**" />
    <None Remove="UI\Forms\**" />
    <None Remove="UI\ModelessRevitForm\**" />
    <Page Remove="CoreLogic\Export\Parameters\**" />
    <Page Remove="UI\Forms\**" />
    <Page Remove="UI\ModelessRevitForm\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="CoreLogic\Export\MacroParameters.cs" />
    <Compile Remove="CoreLogic\Export\RevitToVBA.cs" />
    <Compile Remove="CoreLogic\MD_ParameterCheck.cs" />
    <Compile Remove="UI\UIHelper.cs" />
    <Compile Remove="UI\View\ExternalEventHandler.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Resources\BecaLogoBlack.png" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MahApps.Metro" Version="2.4.10" />
    <PackageReference Include="MaterialDesignThemes.MahApps" Version="0.3.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.VisualBasic" Version="10.3.0" />
  </ItemGroup>
	<ItemGroup>
		<PackageReference Include="Nice3point.Revit.Build.Tasks" Version="2.0.2" />
		<PackageReference Include="Nice3point.Revit.Api.RevitAPI" Version="$(RevitVersion).*" />
		<PackageReference Include="Nice3point.Revit.Api.RevitAPIUI" Version="$(RevitVersion).*" />
		<PackageReference Include="WPF-UI" Version="3.0.5" />
	</ItemGroup>
  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="Properties\Settings.Designer.cs">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Update="UI\StartMessageForm.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\COMMON\BecaActivityLogger\BecaActivityLogger.csproj" />
    <ProjectReference Include="..\..\COMMON\BecaCommand\BecaCommand.csproj" />
    <ProjectReference Include="..\..\COMMON\BecaRevitUtilities\BecaRevitUtilities.csproj" />
    <ProjectReference Include="..\..\COMMON\BecaTransactionsNames\BecaTransactionsNamesManager.csproj" />
    <ProjectReference Include="..\..\COMMON\BecaTrekaHandler\BecaTrekaHandler.csproj" />
    <ProjectReference Include="..\..\COMMON\Common.EnhancedADGV\Common.EnhancedADGV.csproj" />
    <ProjectReference Include="..\..\COMMON\Common.ExcelInterop\Common.ExcelInterop.csproj" />
    <ProjectReference Include="..\..\COMMON\Common.UI.WPF\Common.UI.WPF.csproj" />
    <ProjectReference Include="..\..\COMMON\Common.UI\Common.UI.csproj" />
    <ProjectReference Include="..\..\COMMON\Common.Utilities\Common.Utilities.csproj" />
    <ProjectReference Include="..\..\COMMON\Common.OpenXML\Common.OpenXML.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resources\BecaLogoBlack.png" />
  </ItemGroup>
</Project>