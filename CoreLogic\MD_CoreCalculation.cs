using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using ControlzEx.Standard;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Math;
using DocumentFormat.OpenXml.Wordprocessing;
using MEP.MaxDemand.Models;
using Microsoft.Office.Interop.Excel;

namespace MEP.MaxDemand.CoreLogic.Calculation
{
    public static class MD_CoreCalculation
    {
        public static void RecalculateData(MD_DataModel data)
        {
            // The Maximum Demand will calculate in this order of calculation.
            foreach (var zone in data.Zones)
            {
                foreach (var space in zone.Spaces)
                {
                    UpdateSpaceProperties(data, zone, space);
                }
                UpdateSpaceZoneProperties(zone);
                UpdateDBAndDBZoneProperties(data, zone);
            }
            UpdateDataSummaryProperties(data);
        }

        public static void UpdateSpaceProperties(MD_DataModel data, MD_ZoneModel zone, MD_SpaceModel space)
        {
            // Apply derating to Mech Load
            //DeratedMechLoadDensity = space.MechLoadDensity * zone.HVACTopologyLoadFactor; // old
            if (data.HVACMethodology == (int)HVACMethodology.GenericBecaPD)
                space.AdjustedMechLoadDensity = space.MechLoadDensity;
            if (data.HVACMethodology == (int)HVACMethodology.HVACTopologyModifiedPD)
                space.AdjustedMechLoadDensity = 0;
            else if (data.HVACMethodology == (int)HVACMethodology.UserSpecifiedVAPerM2)
                space.AdjustedMechLoadDensity = zone.DBZoneHVAC;
            else if (data.HVACMethodology == (int)HVACMethodology.UserSpecifiedLumpLoads)
                space.AdjustedMechLoadDensity = 0;
            else if (data.HVACMethodology == (int)HVACMethodology.NoHVAC)
                space.AdjustedMechLoadDensity = 0;

            // Update Single and Three Phase Lump Loads with Spatial Equipments (if exists)
            space.SinglePhaseLumpLoad = space.SpatialEquipments?.Sum(e => e.SinglePhaseLumpLoad) ?? space.SinglePhaseLumpLoad;
            space.ThreePhaseLumpLoad = space.SpatialEquipments?.Sum(e => e.ThreePhaseLumpLoad) ?? space.ThreePhaseLumpLoad;

            // Calculate equipment sums for Single and Three Phase Lump Loads
            var equipmentSinglePhaseLumpLoadSum = space.SpatialEquipments?.Sum(e => e.SinglePhaseLumpLoad) ?? 0;
            var equipmentThreePhaseLumpLoadSum = space.SpatialEquipments?.Sum(e => e.ThreePhaseLumpLoad) ?? 0;
            var equipmentSingleAndThreePhaseLumLoadSum = equipmentSinglePhaseLumpLoadSum + equipmentThreePhaseLumpLoadSum;

            // Calculate sum products for Lump Load Diversity and Power Factor with retained multiplication factor
            var equipmentLumpLoadDiversitySumProduct = space.SpatialEquipments?.Sum(e => ((e.SinglePhaseLumpLoad + e.ThreePhaseLumpLoad) * e.LumpLoadDiversity / 100) * 100) ?? 0;
            var equipmentLumpLoadPowerFactorSumProduct = space.SpatialEquipments?.Sum(e => ((e.SinglePhaseLumpLoad + e.ThreePhaseLumpLoad) * e.LumpLoadPowerFactor / 100) * 100) ?? 0;

            // Calculate weighted average of  Lump Load Diversity and Power Factor
            var equipmentLumpLoadDiversitySum = equipmentSingleAndThreePhaseLumLoadSum != 0
                ? equipmentLumpLoadDiversitySumProduct / equipmentSingleAndThreePhaseLumLoadSum
                : 0;
            var equipmentLumpLoadPowerFactorSum = equipmentSingleAndThreePhaseLumLoadSum != 0
                ? equipmentLumpLoadPowerFactorSumProduct / equipmentSingleAndThreePhaseLumLoadSum
                : 0;

            // Update LumpLoadDiversity and LumpLoadPowerFactor with Spatial Equipments (if exists)
            space.LumpLoadDiversity = equipmentLumpLoadDiversitySum == 0
                ? space.LumpLoadDiversity
                : equipmentLumpLoadDiversitySum;

            space.LumpLoadPowerFactor = equipmentLumpLoadPowerFactorSum == 0
                ? space.LumpLoadPowerFactor
                : equipmentLumpLoadPowerFactorSum;

            // Calculate and Set Space Load Type Totals 
            space.SmallPowerLoad = space.SmallPowerLoadDensity * space.Area / 1000;
            space.LightingLoad = space.LightingLoadDensity * space.Area / 1000;
            space.MechLoad = space.AdjustedMechLoadDensity * space.Area / 1000;
            space.LumpLoad = space.SinglePhaseLumpLoad
                             // Sum of SinglePhaseLumpLoad from each SpatialEquipment; default to 0 if SpatialEquipments is null
                             + (space.SpatialEquipments?.Sum(spatialEquipment => spatialEquipment.SinglePhaseLumpLoad) ?? 0)
                             + space.ThreePhaseLumpLoad
                             // Sum of ThreePhaseLumpLoad from each SpatialEquipment; default to 0 if SpatialEquipments is null
                             + (space.SpatialEquipments?.Sum(spatialEquipment => spatialEquipment.ThreePhaseLumpLoad) ?? 0);

            // Calculate and Set Space Loads
            var totalSpaceLoad = space.SmallPowerLoad + space.LightingLoad + space.MechLoad + space.LumpLoad;
            space.CalculatedDiversity = totalSpaceLoad != 0
                                            ? ((data.DiversityPower * space.SmallPowerLoad) 
                                                + (data.DiversityLighting * space.LightingLoad) 
                                                + (data.DiversityMech * space.MechLoad) 
                                                + (space.LumpLoad * space.LumpLoadDiversity)) / totalSpaceLoad
                                            : 0; 

            space.DiversifiedLoad_kVA = (space.LightingLoad + space.SmallPowerLoad + space.MechLoad + space.LumpLoad) * space.CalculatedDiversity;
            
            var totalSpaceloadDiversity = (space.SmallPowerLoad * data.DiversityPower) + (space.LightingLoad * data.DiversityLighting) +
                                          (space.MechLoad * data.DiversityMech) + (space.LumpLoad * space.LumpLoadDiversity);
            space.CalculatedPowerFactor = totalSpaceloadDiversity != 0
                                          ? ((space.SmallPowerLoad * data.DiversityPower * data.PowerFactorPower) 
                                              + (space.LightingLoad * data.DiversityLighting * data.PowerFactorLighting) 
                                              + (space.MechLoad * data.DiversityMech * data.PowerFactorMech) 
                                              + (space.LumpLoad * space.LumpLoadDiversity * space.LumpLoadPowerFactor)) / totalSpaceloadDiversity
                                          : 0;

            space.DiversifiedLoad_kW = space.DiversifiedLoad_kVA != 0
                                       ? space.DiversifiedLoad_kVA * space.CalculatedPowerFactor
                                       : 0;
            space.DiversifiedCurrent_A = space.DiversifiedLoad_kVA != 0
                                         ? (space.DiversifiedLoad_kVA * 1000 / data.LineToNeutralVoltage) / data.NumberOfPhases
                                         : 0;

            // Calculate and Set Space Sum Product 
            space.PowerLoadDensitySumProduct = space.Area * space.SmallPowerLoadDensity;
            space.LightingLoadDensitySumProduct = space.Area * space.LightingLoadDensity;
            space.MechDensitySumProduct = space.Area * space.AdjustedMechLoadDensity;
            space.LumpLoadDiversitySumProduct = (space.SinglePhaseLumpLoad + space.ThreePhaseLumpLoad) * space.LumpLoadDiversity;
            space.LumpLoadPowerFactorSumProduct = (space.SinglePhaseLumpLoad + space.ThreePhaseLumpLoad) * space.LumpLoadPowerFactor;
            space.ZonePowerFactorSumProduct = (space.SmallPowerLoad + space.LightingLoad + space.MechLoad + space.LumpLoad) * space.CalculatedPowerFactor;
            space.ZoneDiversitySumProduct = space.DiversifiedLoad_kVA;
        }

        public static void UpdateSpaceZoneProperties(MD_ZoneModel zone)
        {
            var spaces = zone.Spaces;
            var totalArea = Math.Round(spaces.Sum(s => s.Area), 1);

            // Total Area (Zone)
            zone.TotalArea = totalArea;

            // Calculate and Set Zone's Load Densities
            zone.LightingLoadDensity = spaces.Sum(s => s.LightingLoadDensitySumProduct) / totalArea;
            zone.SmallPowerLoadDensity = spaces.Sum(s => s.PowerLoadDensitySumProduct) / totalArea;
            zone.MechLoadDensity = spaces.Sum(s => s.MechDensitySumProduct) / totalArea;

            // Calculate and Set Zone's Space Lump Loads 
            zone.SinglePhaseLumpLoadSum = spaces.Sum(s => s.SinglePhaseLumpLoad);
            zone.ThreePhaseLumpLoadSum = spaces.Sum(s => s.ThreePhaseLumpLoad);

            // Times 100 in ( s.LumpLoadDiversitySumProduct * 100 ) to match the value in excel (percentage)
            zone.LumpLoadDiversitySum = (zone.SinglePhaseLumpLoadSum + zone.ThreePhaseLumpLoadSum) != 0
                                            ? spaces.Sum(s => s.LumpLoadDiversitySumProduct * 100) / (zone.SinglePhaseLumpLoadSum + zone.ThreePhaseLumpLoadSum)
                                            : 0;
            zone.LumpLoadPowerFactorSum = (zone.SinglePhaseLumpLoadSum + zone.ThreePhaseLumpLoadSum) != 0
                                            ? spaces.Sum(s => s.LumpLoadPowerFactorSumProduct * 100) / (zone.SinglePhaseLumpLoadSum + zone.ThreePhaseLumpLoadSum)
                                            : 0;

            // Calculate and Set Zone's Load Type Totals 
            zone.SmallPowerLoadSum = spaces.Sum(s => s.SmallPowerLoad);
            zone.LightingLoadSum = spaces.Sum(s => s.LightingLoad);

            zone.MechLoadSum = spaces.Sum(s => s.MechLoad);
            zone.LumpLoadSum = spaces.Sum(s => s.LumpLoad);

            // Calculate and Set Zone's Space Loads
            var totalZoneLoadSum = zone.SmallPowerLoadSum + zone.LightingLoadSum + zone.MechLoadSum + zone.LumpLoadSum;
            zone.CalculatedDiversity = totalZoneLoadSum != 0
                                       ? spaces.Sum(s => s.ZoneDiversitySumProduct) / totalZoneLoadSum
                                       : 0;

            zone.DiversifiedLoad_kVA = spaces.Sum(s => s.DiversifiedLoad_kVA);

            var totalSpaceLoadSum = spaces.Sum(s => s.ZonePowerFactorSumProduct) / (spaces.Sum(s => s.SmallPowerLoad) + spaces.Sum(s => s.LightingLoad) + spaces.Sum(s => s.MechLoad) + spaces.Sum(s => s.LumpLoad));
            zone.CalculatedPowerFactor = !double.IsNaN(totalSpaceLoadSum)  
                                         ? totalSpaceLoadSum
                                         : 0;
            var spaceDiversifiedLoadSum = spaces.Sum(s => s.DiversifiedLoad_kW);
            zone.DiversifiedLoad_kW = spaceDiversifiedLoadSum != 0
                                      ? spaces.Sum(s => s.DiversifiedLoad_kW)
                                      : 0;
            zone.DiversifiedCurrent_A = spaces.Sum(s => s.DiversifiedCurrent_A);
        }

        public static void UpdateDBAndDBZoneProperties(MD_DataModel data, MD_ZoneModel zone)
        {
            var dBs = zone.DBs;

            // Collect General DBs
            var GeneralSmallPowerLoad = dBs.Where(d => d.PowerSupplyClass == "G" && d.IsSmallPower);
            var GeneralLightingLoad = dBs.Where(d => d.PowerSupplyClass == "G" && d.IsLighting);
            var GeneralMechanicalLoad = dBs.Where(d => d.PowerSupplyClass == "G" && d.IsMech);
            var GeneralLumpLoad = dBs.Where(d => d.PowerSupplyClass == "G" && d.IsLump);
            // Collect Essential DBs
            var EssentialSmallPowerLoad = dBs.Where(d => d.PowerSupplyClass == "E" && d.IsSmallPower);
            var EssentialLightingLoad = dBs.Where(d => d.PowerSupplyClass == "E" && d.IsLighting);
            var EssentialMechanicalLoad = dBs.Where(d => d.PowerSupplyClass == "E" && d.IsMech);
            var EssentialLumpLoad = dBs.Where(d => d.PowerSupplyClass == "E" && d.IsLump);
            // Collect Uninteruptable DBs
            var UninteruptableSmallPowerLoad = dBs.Where(d => d.PowerSupplyClass == "U" && d.IsSmallPower);
            var UninteruptableLightingLoad = dBs.Where(d => d.PowerSupplyClass == "U" && d.IsLighting);
            var UninteruptableMechanicalLoad = dBs.Where(d => d.PowerSupplyClass == "U" && d.IsMech);
            var UninteruptableLumpLoad = dBs.Where(d => d.PowerSupplyClass == "U" && d.IsLump);

            // Zone's Load Allocation Counts (General)
            zone.GeneralSmallPowerLoadCount = GeneralSmallPowerLoad.Count();
            zone.GeneralLightingLoadCount = GeneralLightingLoad.Count();
            zone.GeneralMechanicalLoadCount = GeneralMechanicalLoad.Count();
            zone.GeneralLumpLoadCount = GeneralLumpLoad.Count();
            // Zone's Load Allocation Counts (Essential)
            zone.EssentialSmallPowerLoadCount = EssentialSmallPowerLoad.Count();
            zone.EssentialLightingLoadCount = EssentialLightingLoad.Count();
            zone.EssentialMechanicalLoadCount = EssentialMechanicalLoad.Count();
            zone.EssentialLumpLoadCount = EssentialLumpLoad.Count();
            // Zone's Load Allocation Counts (Uninteruptable)
            zone.UninteruptableSmallPowerLoadCount = UninteruptableSmallPowerLoad.Count();
            zone.UninteruptableLightingLoadCount = UninteruptableLightingLoad.Count();
            zone.UninteruptableMechanicalLoadCount = UninteruptableMechanicalLoad.Count();
            zone.UninteruptableLumpLoadCount = UninteruptableLumpLoad.Count();

            // Calculate and set Zone's Diversified Load Values (General)
            zone.GeneralSmallPowerLoad = CalculateGeneralLoad(data.DiversityPower, zone.SmallPowerLoadSum, zone.EssentialSmallPowerLoadPercentage, zone.UninteruptableSmallPowerLoadPercentage);
            zone.GeneralLightingLoad = CalculateGeneralLoad(data.DiversityLighting, zone.LightingLoadSum, zone.EssentialLightingLoadPercentage, zone.UninteruptableLightingLoadPercentage);
            zone.GeneralMechanicalLoad = CalculateGeneralLoad(data.DiversityMech, zone.MechLoadSum, zone.EssentialMechanicalLoadPercentage, zone.UninteruptableMechanicalLoadPercentage);
            // Special case for Lump Load (diversity is divided by 100)
            zone.GeneralLumpLoad = CalculateGeneralLoad(zone.LumpLoadDiversitySum / 100.0, zone.LumpLoadSum, zone.EssentialLumpLoadPercentage, zone.UninteruptableLumpLoadPercentage);


            // Calculate and set Zone's Diversified Load Values (Essential)
            zone.EssentialSmallPowerLoad = data.DiversityPower * zone.SmallPowerLoadSum * (zone.EssentialSmallPowerLoadPercentage / 100.0);
            zone.EssentialLightingLoad = data.DiversityLighting * zone.LightingLoadSum * (zone.EssentialLightingLoadPercentage / 100.0);
            zone.EssentialMechanicalLoad = data.DiversityMech * zone.MechLoadSum * (zone.EssentialMechanicalLoadPercentage / 100.0);
            zone.EssentialLumpLoad = (zone.LumpLoadDiversitySum / 100.0) * zone.LumpLoadSum * (zone.EssentialLumpLoadPercentage / 100.0);
            // Calculate and set Zone's Diversified Load Values (Uninteruptable)
            zone.UninteruptableSmallPowerLoad = data.DiversityPower * zone.SmallPowerLoadSum * (zone.UninteruptableSmallPowerLoadPercentage / 100.0);
            zone.UninteruptableLightingLoad = data.DiversityLighting * zone.LightingLoadSum * (zone.UninteruptableLightingLoadPercentage / 100.0);
            zone.UninteruptableMechanicalLoad = data.DiversityMech * zone.MechLoadSum * (zone.UninteruptableMechanicalLoadPercentage / 100.0);
            zone.UninteruptableLumpLoad = (zone.LumpLoadDiversitySum / 100.0) * zone.LumpLoadSum * (zone.UninteruptableLumpLoadPercentage / 100.0);

            // Calculate Diversified Load per DB (General)
            var GeneralSmallPowerLoadPerDB = zone.GeneralSmallPowerLoad / zone.GeneralSmallPowerLoadCount;
            var GeneralLightingLoadPerDB = zone.GeneralLightingLoad / zone.GeneralLightingLoadCount;
            var GeneralMechanicalLoadPerDB = zone.GeneralMechanicalLoad / zone.GeneralMechanicalLoadCount;
            var GeneralLumpLoadPerDB = zone.GeneralLumpLoad / zone.GeneralLumpLoadCount;
            // Calculate Diversified Load per DB (Essential)
            var EssentialSmallPowerLoadPerDB = zone.EssentialSmallPowerLoad / zone.EssentialSmallPowerLoadCount;
            var EssentialLightingLoadPerDB = zone.EssentialLightingLoad / zone.EssentialLightingLoadCount;
            var EssentialMechanicalLoadPerDB = zone.EssentialMechanicalLoad / zone.EssentialMechanicalLoadCount;
            var EssentialLumpLoadPerDB = zone.EssentialLumpLoad / zone.EssentialLumpLoadCount;
            // Calculate Diversified Load per DB (Uninteruptable)
            var UninteruptableSmallPowerLoadPerDB = zone.UninteruptableSmallPowerLoad / zone.UninteruptableSmallPowerLoadCount;
            var UninteruptableLightingLoadPerDB = zone.UninteruptableLightingLoad / zone.UninteruptableLightingLoadCount;
            var UninteruptableMechanicalLoadPerDB = zone.UninteruptableMechanicalLoad / zone.UninteruptableMechanicalLoadCount;
            var UninteruptableLumpLoadPerDB = zone.UninteruptableLumpLoad / zone.UninteruptableLumpLoadCount;

            foreach (var dB in dBs)
            {
                // Set each DB's diversified load properties (General)
                if (dB.PowerSupplyClass == "G" && dB.IsSmallPower) dB.GeneralSmallPowerLoad = GeneralSmallPowerLoadPerDB; else dB.GeneralSmallPowerLoad = 0;
                if (dB.PowerSupplyClass == "G" && dB.IsLighting) dB.GeneralLightingLoad = GeneralLightingLoadPerDB; else dB.GeneralLightingLoad = 0;
                if (dB.PowerSupplyClass == "G" && dB.IsMech) dB.GeneralMechanicalLoad = GeneralMechanicalLoadPerDB; else dB.GeneralMechanicalLoad = 0;
                if (dB.PowerSupplyClass == "G" && dB.IsLump) dB.GeneralLumpLoad = GeneralLumpLoadPerDB; else dB.GeneralLumpLoad = 0;
                // Set each DB's diversified load properties (Essential)
                if (dB.PowerSupplyClass == "E" && dB.IsSmallPower) dB.EssentialSmallPowerLoad = EssentialSmallPowerLoadPerDB; else dB.EssentialSmallPowerLoad = 0;
                if (dB.PowerSupplyClass == "E" && dB.IsLighting) dB.EssentialLightingLoad = EssentialLightingLoadPerDB; else dB.EssentialLightingLoad = 0;
                if (dB.PowerSupplyClass == "E" && dB.IsMech) dB.EssentialMechanicalLoad = EssentialMechanicalLoadPerDB; else dB.EssentialMechanicalLoad = 0;
                if (dB.PowerSupplyClass == "E" && dB.IsLump) dB.EssentialLumpLoad = EssentialLumpLoadPerDB; else dB.EssentialLumpLoad = 0;
                // Set each DB's diversified load properties (Uninteruptable)
                if (dB.PowerSupplyClass == "U" && dB.IsSmallPower) dB.UninteruptableSmallPowerLoad = UninteruptableSmallPowerLoadPerDB; else dB.UninteruptableSmallPowerLoad = 0;
                if (dB.PowerSupplyClass == "U" && dB.IsLighting) dB.UninteruptableLightingLoad = UninteruptableLightingLoadPerDB; else dB.UninteruptableLightingLoad = 0;
                if (dB.PowerSupplyClass == "U" && dB.IsMech) dB.UninteruptableMechanicalLoad = UninteruptableMechanicalLoadPerDB; else dB.UninteruptableMechanicalLoad = 0;
                if (dB.PowerSupplyClass == "U" && dB.IsLump) dB.UninteruptableLumpLoad = UninteruptableLumpLoadPerDB; else dB.UninteruptableLumpLoad = 0;

                // Calculate G/E/U Load SUM
                var smallPowerLoadsGEU = dB.GeneralSmallPowerLoad + dB.EssentialSmallPowerLoad + dB.UninteruptableSmallPowerLoad;
                var lightingLoadsGEU = dB.GeneralLightingLoad + dB.EssentialLightingLoad + dB.UninteruptableLightingLoad;
                var mechanicalLoadsGEU = dB.GeneralMechanicalLoad + dB.EssentialMechanicalLoad + dB.UninteruptableMechanicalLoad;
                var lumpLoadsGEU = dB.GeneralLumpLoad + dB.EssentialLumpLoad + dB.UninteruptableLumpLoad;
                // Calculate Load SUM and Default Power Factor
                var smallPowerLoadsGEUPowerFactor = smallPowerLoadsGEU * data.PowerFactorPower;
                var lightingLoadsGEUPowerFactor = lightingLoadsGEU * data.PowerFactorLighting;
                var mechanicalLoadsGEUPowerFactor = mechanicalLoadsGEU * data.PowerFactorMech;
                var lumpLoadsGEUPowerFactor = lumpLoadsGEU * data.PowerFactorLumpLoad;

                // Calculate and Set Power Factor 
                var pfNumerator = smallPowerLoadsGEUPowerFactor + lightingLoadsGEUPowerFactor + mechanicalLoadsGEUPowerFactor + lumpLoadsGEUPowerFactor;
                var pfDenominator = smallPowerLoadsGEU + lightingLoadsGEU + mechanicalLoadsGEU + lumpLoadsGEU;
                // Perform the calculation and handle NaN (division by zero)
                dB.PowerFactor = double.IsNaN(pfNumerator / pfDenominator) 
                                    ? 0 
                                    : pfNumerator / pfDenominator;

                // Calculate and Set Diversified per Phase Current
                var totalLoad = dB.GeneralSmallPowerLoad + dB.GeneralLightingLoad + dB.GeneralMechanicalLoad + dB.GeneralLumpLoad +
                   dB.EssentialSmallPowerLoad + dB.EssentialLightingLoad + dB.EssentialMechanicalLoad + dB.EssentialLumpLoad +
                   dB.UninteruptableSmallPowerLoad + dB.UninteruptableLightingLoad + dB.UninteruptableMechanicalLoad + dB.UninteruptableLumpLoad;
                var dppcDenominator = data.LineToNeutralVoltage * data.NumberOfPhases;
                // Perform the calculation and handle NaN (division by zero)
                dB.DiversifiedPerPhaseCurrent = double.IsNaN((1000 / dppcDenominator) * totalLoad) 
                                                    ? 0 
                                                    : (1000 / dppcDenominator) * totalLoad;

                // Calculate DB Summary
                dB.TotalDiversifiedLoad_kVA = (dB.DiversifiedPerPhaseCurrent * data.LineToNeutralVoltage * data.NumberOfPhases) / 1000;
                dB.TotalDiversifiedLoad_kW = dB.TotalDiversifiedLoad_kVA * dB.PowerFactor;
            }
            // Calculate and Set Zone's Diversified Per Phase Current
            zone.DiversifiedPerPhaseCurrent = dBs.Sum(d => d.DiversifiedPerPhaseCurrent);
        }

        private static double CalculateGeneralLoad(double diversityFactor, double loadSum, int essentialPercentage, int uninteruptablePercentage)
        {
            if (double.IsNaN(diversityFactor) || double.IsNaN(loadSum))
            {
                return 0; // Default value if input is NaN
            }
            double factor = 1 - (essentialPercentage / 100.0) - (uninteruptablePercentage / 100.0);
            if (double.IsNaN(factor) || factor < 0)
            {
                factor = 0; // Prevent NaN and negative values
            }
            return diversityFactor * loadSum * factor;
        }

        private static double CalculateSummaryTotalAveragePowerFactor(IEnumerable<MD_DBModel> dBs)
        {
            var powerFactors = dBs.Select(d => d.PowerFactor);
            var diversifiedPerPhaseCurrentkVA = dBs.Select(d => d.DiversifiedPerPhaseCurrent);

            // Calculate the sumproduct of powerFactors and diversifiedPerPhaseCurrentkVA
            double sumProduct = diversifiedPerPhaseCurrentkVA.Zip(powerFactors, (dpp, pf) => dpp * pf).Sum();

            // Calculate the sum of diversifiedPerPhaseCurrentkVA
            double sumDiversifiedPerPhaseCurrentkVA = diversifiedPerPhaseCurrentkVA.Sum();

            // Divide the sumproduct by the sum of dpps, safely handle 0 div error
            return sumDiversifiedPerPhaseCurrentkVA > 0 
                ? sumProduct / sumDiversifiedPerPhaseCurrentkVA
                : 0;
        }


        public static void UpdateDataSummaryProperties(MD_DataModel data)
        {
            if (data.Zones == null || data.Zones.Count == 0)
                return;

            var totalCellName = "Total :";
            var dBs = data.Zones.Where(z => z?.DBs != null).SelectMany(z => z.DBs);

            if (!dBs.Any()) return;

            // Main Board Summary
            var mainBoardsummaries = new List<Summary>();
            var mainDBs = dBs.Where(ps => ps.PowerSupplyClass == "G");
            foreach (var mainDB in mainDBs)
            {
                mainBoardsummaries.Add(new Summary
                {
                    DBName = mainDB.DB.Name,
                    DiversifiedPerPhaseCurrent_A = mainDB?.DiversifiedPerPhaseCurrent ?? 0,
                    TotaldiversifiedLoad_kVA = mainDB?.TotalDiversifiedLoad_kVA ?? 0,
                    AveragePowerFactor = mainDB?.PowerFactor ?? 0,
                    TotalDiversifiedLoad_kW = mainDB?.TotalDiversifiedLoad_kW ?? 0,
                    IsTotal = false
                });
            }
            if (mainDBs.Any())
            {
                mainBoardsummaries.Add(new Summary
                {
                    DBName = totalCellName,
                    DiversifiedPerPhaseCurrent_A = mainDBs.Sum(db => db.DiversifiedPerPhaseCurrent),
                    TotaldiversifiedLoad_kVA = (mainDBs.Sum(db => db.TotalDiversifiedLoad_kVA)),
                    AveragePowerFactor = CalculateSummaryTotalAveragePowerFactor(mainDBs),
                    TotalDiversifiedLoad_kW = mainDBs.Sum(db => db.TotalDiversifiedLoad_kW),
                    IsTotal = true
                });
            }
            else
            {
                mainBoardsummaries.Add(new Summary
                {
                    DBName = totalCellName,
                    DiversifiedPerPhaseCurrent_A =0,
                    TotaldiversifiedLoad_kVA = 0,
                    AveragePowerFactor = 0,
                    TotalDiversifiedLoad_kW = 0,
                    IsTotal = true
                });
            }
            
            data.Summary_MainSupplyDBs = mainBoardsummaries;

            // Essential Summary
            var essentialSummaries = new List<Summary>();
            var essentialDBs = dBs.Where(ps => ps.PowerSupplyClass == "E");
            foreach (var essentialDB in essentialDBs)
            {
                essentialSummaries.Add(new Summary
                {
                    DBName = essentialDB.DB.Name,
                    DiversifiedPerPhaseCurrent_A = essentialDB?.DiversifiedPerPhaseCurrent ?? 0,
                    TotaldiversifiedLoad_kVA = essentialDB?.TotalDiversifiedLoad_kVA ?? 0,
                    AveragePowerFactor = essentialDB?.PowerFactor ?? 0,
                    TotalDiversifiedLoad_kW = essentialDB?.TotalDiversifiedLoad_kW ?? 0,
                    IsTotal = false
                });
            }
            if (essentialDBs.Any())
            {
                essentialSummaries.Add(new Summary
                {
                    DBName = totalCellName,
                    DiversifiedPerPhaseCurrent_A = essentialDBs.Sum(db => db.DiversifiedPerPhaseCurrent),
                    TotaldiversifiedLoad_kVA = essentialDBs.Sum(db => db.TotalDiversifiedLoad_kVA),
                    AveragePowerFactor = CalculateSummaryTotalAveragePowerFactor(essentialDBs),
                    TotalDiversifiedLoad_kW = essentialDBs.Sum(db => db.TotalDiversifiedLoad_kW),
                    IsTotal = true
                });
            }
            else
            {
                // If there are no essentialDBs, add a summary with default values
                essentialSummaries.Add(new Summary
                {
                    DBName = totalCellName,
                    DiversifiedPerPhaseCurrent_A = 0,
                    TotaldiversifiedLoad_kVA = 0,
                    AveragePowerFactor = 0,
                    TotalDiversifiedLoad_kW = 0,
                    IsTotal = true
                });
            }
            data.Summary_EssentialDBs = essentialSummaries;

            // Uninteruptable Summary
            var uninteruptableSummaries = new List<Summary>();
            var uninteruptableDBs = dBs.Where(ps => ps.PowerSupplyClass == "U");
            foreach (var uninteruptableDB in uninteruptableDBs)
            {
                uninteruptableSummaries.Add(new Summary
                {
                    DBName = uninteruptableDB.DB.Name,
                    DiversifiedPerPhaseCurrent_A = uninteruptableDB?.DiversifiedPerPhaseCurrent ?? 0,
                    TotaldiversifiedLoad_kVA = uninteruptableDB?.TotalDiversifiedLoad_kVA ?? 0,
                    AveragePowerFactor = uninteruptableDB?.PowerFactor ?? 0,
                    TotalDiversifiedLoad_kW = uninteruptableDB?.TotalDiversifiedLoad_kW ?? 0,
                    IsTotal = false
                });
            }
            if (uninteruptableDBs.Any())
            {
                uninteruptableSummaries.Add(new Summary
                {
                    DBName = totalCellName,
                    DiversifiedPerPhaseCurrent_A = uninteruptableDBs.Sum(db => db.DiversifiedPerPhaseCurrent),
                    TotaldiversifiedLoad_kVA = uninteruptableDBs.Sum(db => db.TotalDiversifiedLoad_kVA),
                    AveragePowerFactor = CalculateSummaryTotalAveragePowerFactor(uninteruptableDBs),
                    TotalDiversifiedLoad_kW = uninteruptableDBs.Sum(db => db.TotalDiversifiedLoad_kW),
                    IsTotal = true
                });
            }
            else
            {
                uninteruptableSummaries.Add(new Summary
                {
                    DBName = totalCellName,
                    DiversifiedPerPhaseCurrent_A = 0,
                    TotaldiversifiedLoad_kVA = 0,
                    AveragePowerFactor = 0,
                    TotalDiversifiedLoad_kW = 0,
                    IsTotal = true
                });
            }
            
            data.Summary_UninteruptableDBs = uninteruptableSummaries;

            // Site Summary (have to bein this order)
            // Summary Values
            var mainsSupplyTotal = data.Summary_MainSupplyDBs.Find(s => s.DBName == totalCellName);
            var generatorSupplyTotal = data.Summary_EssentialDBs.Find(s => s.DBName == totalCellName);
            var uPSSupplyTotal = data.Summary_UninteruptableDBs.Find(s => s.DBName == totalCellName);
          
            data.AveragePowerFactor = ((mainsSupplyTotal.TotaldiversifiedLoad_kVA * mainsSupplyTotal.AveragePowerFactor) + 
                                      (generatorSupplyTotal.TotaldiversifiedLoad_kVA * generatorSupplyTotal.AveragePowerFactor) +
                                      (uPSSupplyTotal.TotaldiversifiedLoad_kVA * uPSSupplyTotal.AveragePowerFactor)) 
                                      / (mainsSupplyTotal.TotaldiversifiedLoad_kVA + generatorSupplyTotal.TotaldiversifiedLoad_kVA + uPSSupplyTotal.TotaldiversifiedLoad_kVA);
            if (double.IsNaN(data.AveragePowerFactor)) data.AveragePowerFactor = 0;

            data.TotalArea = data.Zones.Sum(z => z.TotalArea);

            // Mains Supply
            data.MainsSupplyTotalSiteDiversifiedLoadkVA = mainsSupplyTotal.TotaldiversifiedLoad_kVA * data.SiteDiversity * (1 + data.SiteSpareCapacity);
            data.MainsSupplyTotalSiteDiversifiedLoadkW = mainsSupplyTotal.TotalDiversifiedLoad_kW * data.SiteDiversity * (1 + data.SiteSpareCapacity);
            data.MainsSupplyTotalSiteDiversifiedLoadkVAR = Math.Sqrt(Math.Pow(data.MainsSupplyTotalSiteDiversifiedLoadkVA, 2) - Math.Pow(data.MainsSupplyTotalSiteDiversifiedLoadkW, 2));
            data.MainsSupplyTotalDiversifiedPerPhaseLoadA = mainsSupplyTotal.DiversifiedPerPhaseCurrent_A * data.SiteDiversity * (1 + data.SiteSpareCapacity);
            // Generator Supply
            data.GeneratorSupplyTotalSiteDiversifiedLoadkVA = generatorSupplyTotal.TotaldiversifiedLoad_kVA * data.SiteDiversity * (1 + data.SiteSpareCapacity);
            data.GeneratorSupplyTotalSiteDiversifiedLoadkW = generatorSupplyTotal.TotalDiversifiedLoad_kW * data.SiteDiversity * (1 + data.SiteSpareCapacity);
            data.GeneratorSupplyTotalSiteDiversifiedLoadkVAR = Math.Sqrt(Math.Pow(data.GeneratorSupplyTotalSiteDiversifiedLoadkVA, 2) - Math.Pow(data.GeneratorSupplyTotalSiteDiversifiedLoadkW, 2));
            data.GeneratorSupplyTotalDiversifiedPerPhaseLoadA = generatorSupplyTotal.DiversifiedPerPhaseCurrent_A * data.SiteDiversity * (1 + data.SiteSpareCapacity);
            // UPS Supply
            data.UPSSupplyTotalSiteDiversifiedLoadkVA = uPSSupplyTotal.TotaldiversifiedLoad_kVA * data.SiteDiversity * (1 + data.SiteSpareCapacity);
            data.UPSSupplyTotalSiteDiversifiedLoadkW = uPSSupplyTotal.TotalDiversifiedLoad_kW * data.SiteDiversity * (1 + data.SiteSpareCapacity);
            data.UPSSupplyTotalSiteDiversifiedLoadkVAR = Math.Sqrt(Math.Pow(data.UPSSupplyTotalSiteDiversifiedLoadkVA, 2) - Math.Pow(data.UPSSupplyTotalSiteDiversifiedLoadkW, 2));
            data.UPSSupplyTotalDiversifiedPerPhaseLoadA = uPSSupplyTotal.DiversifiedPerPhaseCurrent_A * data.SiteDiversity * (1 + data.SiteSpareCapacity);
            // Overall Supply
            data.OverallSupplyTotalSiteDiversifiedLoadkVA = data.MainsSupplyTotalSiteDiversifiedLoadkVA + data.GeneratorSupplyTotalSiteDiversifiedLoadkVA 
                                                                + data.UPSSupplyTotalSiteDiversifiedLoadkVA;
            data.OverallSupplyTotalSiteDiversifiedLoadkW = data.MainsSupplyTotalSiteDiversifiedLoadkW + data.GeneratorSupplyTotalSiteDiversifiedLoadkW 
                                                                + data.UPSSupplyTotalSiteDiversifiedLoadkW;
            data.OverallSupplyTotalSiteDiversifiedLoadkVAR = data.MainsSupplyTotalSiteDiversifiedLoadkVAR + data.GeneratorSupplyTotalSiteDiversifiedLoadkVAR 
                                                                + data.UPSSupplyTotalSiteDiversifiedLoadkVAR;
            data.OverallSupplyTotalDiversifiedPerPhaseLoadA = data.MainsSupplyTotalDiversifiedPerPhaseLoadA + data.GeneratorSupplyTotalDiversifiedPerPhaseLoadA 
                                                                + data.UPSSupplyTotalDiversifiedPerPhaseLoadA;

            // Summary Value
            data.AveragePowerDensityWm2 = data.OverallSupplyTotalSiteDiversifiedLoadkW / data.TotalArea * 1000;

            // Transformer, Generator, UPS Sizes
            data.TransformerSizekVA = data.MainsSupplyTotalSiteDiversifiedLoadkVA == 0 
                ? 0 
                : MD_Constants.TransformerSizes.FirstOrDefault(size => size >= data.MainsSupplyTotalSiteDiversifiedLoadkVA);
            data.GeneratorSizekVA = data.GeneratorSupplyTotalSiteDiversifiedLoadkVA == 0
                ? 0 
                : MD_Constants.GeneratorSizes.FirstOrDefault(size => size >= data.GeneratorSupplyTotalSiteDiversifiedLoadkVA);
            data.UPSSizekVA = data.UPSSupplyTotalSiteDiversifiedLoadkVA == 0
                ? 0 
                : MD_Constants.UPSSizes.FirstOrDefault(size => size >= data.UPSSupplyTotalSiteDiversifiedLoadkVA);
        }
    }
}
