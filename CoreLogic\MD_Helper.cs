﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Analysis;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.UI;
using BecaRevitUtilities;
using BecaRevitUtilities.SharedParametersUtilities;
using Common.UI.Forms;
using MEP.MaxDemand.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using MessageBox = System.Windows.MessageBox;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.MaxDemand.CoreLogic
{
    public static class MD_Helper
    {
        /// <summary>
        /// This is to get the dictionary of PartType from each DB Family
        /// to get the PartType in MD_DB class
        /// </summary>
        /// <param name="doc"></param>
        /// <returns></returns>
        public static Dictionary<ElementId, PartType> GetFamilysAndPartType(Document doc)
        {
            var familyPartTypeDict = new Dictionary<ElementId, PartType>();

            foreach (Family familySymbol in new FilteredElementCollector(doc).OfClass(typeof(Family)))
            {
                if (familySymbol != null)
                {
                    var partTypeParam = familySymbol.get_Parameter(BuiltInParameter.FAMILY_CONTENT_PART_TYPE);
                    if (partTypeParam != null && partTypeParam.HasValue)
                    {
                        var partType = (PartType)partTypeParam.AsInteger();
                        if (partType == PartType.PanelBoard || partType == PartType.SwitchBoard || partType == PartType.OtherPanel)
                        {
                            familyPartTypeDict[familySymbol.Id] = partType;
                        }
                    }
                }
            }
            return familyPartTypeDict;
        }

        public static List<FamilyInstance> GetElectricalEquipments(Document doc)
        {
            return new FilteredElementCollector(doc)
                .OfCategory(BuiltInCategory.OST_ElectricalEquipment)
                .WhereElementIsNotElementType()
                .OfType<FamilyInstance>()
                .ToList();
        }

        public static List<Space> GetSpaces(Document doc)
        {
            return new FilteredElementCollector(doc)
                .OfCategory(BuiltInCategory.OST_MEPSpaces)
                .Cast<Space>()
                .OrderBy(s => s.Number)
                .ToList();
        }

        public static List<MD_DBModel> GetMD_DBs(List<FamilyInstance> electricalEquipments, Dictionary<ElementId, PartType> familyPartTypeDict)
        {
            var filteredDBs = FilterDBs(electricalEquipments, familyPartTypeDict, out var partTypes);
            var dBsAndCircuits = new DBsAndCircuits() 
            { 
                DBNameDictionary = filteredDBs.ToDictionary(fi => fi.Name), 
                AllCircuits = new FilteredElementCollector(electricalEquipments.FirstOrDefault().Document)
                .OfClass(typeof(ElectricalSystem))
                .Cast<ElectricalSystem>().ToList()
            };
            var mD_DBs = new List<MD_DBModel>();

            for (int i = 0; i < filteredDBs.Count; i++)
            {
                mD_DBs.Add(new MD_DBModel(filteredDBs[i], partTypes[i], dBsAndCircuits));
            }

            return mD_DBs;
        }

        public static List<FamilyInstance> GetDBs(List<FamilyInstance> electricalEquipments, Dictionary<ElementId, PartType> familyPartTypeDict)
        {
            return FilterDBs(electricalEquipments, familyPartTypeDict, out _);
        }

        private static List<FamilyInstance> FilterDBs(List<FamilyInstance> electricalEquipments, Dictionary<ElementId, PartType> familyPartTypeDict, out List<PartType> partTypes)
        {
            var filteredDBs = new List<FamilyInstance>();
            partTypes = new List<PartType>();

            foreach (var el in electricalEquipments)
            {
                var familySymbol = el.Document.GetElement(el.GetTypeId()) as FamilySymbol;
                var family = familySymbol?.Family;

                if (family != null && familyPartTypeDict.ContainsKey(family.Id))
                {
                    PartType partType = familyPartTypeDict[family.Id];

                    if (partType == PartType.PanelBoard || partType == PartType.SwitchBoard || partType == PartType.OtherPanel)
                    {
                        filteredDBs.Add(el);
                        partTypes.Add(partType);
                    }
                }
            }

            return filteredDBs;
        }


        public static bool DuplicateSpacesOrDBsFound(List<FamilyInstance> dBs, List<Space> spaces)
        {
            var duplicateOrphanedDbNames = dBs
                .GroupBy(db => db.Name)
                .Where(g => g.Count() > 1)
                .SelectMany(g => g.Select(db => new { Name = db.Name, ElementId = db.Id }))
                .ToList();

            var duplicateSpaceNames = spaces
                .GroupBy(s => s.Name)
                .Where(g => g.Count() > 1)
                .SelectMany(g => g.Select(s => new { ElementId = s.Id, Name = s.Name, Number = s.Number }))
                .ToList();

            if (!duplicateOrphanedDbNames.Any() && !duplicateSpaceNames.Any())
            {
                return false; 
            }

            var message = new StringBuilder("Duplicate entries found!\n\n");
            message.AppendLine($"Duplicate DB Names: {duplicateOrphanedDbNames.Count}");
            message.AppendLine($"Duplicate Space Names: {duplicateSpaceNames.Count}");
            message.AppendLine("\nWould you like to export the detailed duplicate information to a CSV file?");

            var result = MessageBox.Show(message.ToString(), "Duplicate Entries Warning",
                                 MessageBoxButton.YesNo, MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                ExportDuplicatesToCsv(duplicateOrphanedDbNames, duplicateSpaceNames);
            }

            return true;
        }

        private static void ExportDuplicatesToCsv(IEnumerable<dynamic> duplicateDBs, IEnumerable<dynamic> duplicateSpaces)
        {
            var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "DuplicateEntries.csv");

            using (var writer = new StreamWriter(filePath))
            {
                writer.WriteLine("Type,ElementId,Panel Name");

                foreach (var db in duplicateDBs)
                {
                    writer.WriteLine($"DB,{db.ElementId},{db.Name}");
                }

                writer.WriteLine("Type,ElementId,Space Name,Space Number");

                foreach (var space in duplicateSpaces)
                {
                    writer.WriteLine($"Space,{space.ElementId},{space.Name}, {space.Number}");
                }
            }

            var exportMessage = $"Duplicate details exported to: {filePath}";
            MessageBox.Show(exportMessage, "Export Successful", MessageBoxButton.OK, MessageBoxImage.Information);

            var openFileResult = MessageBox.Show("Would you like to open the exported file?", "Open File",
                                                 MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (openFileResult == MessageBoxResult.Yes)
            {
                try
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Failed to open the file: {ex.Message}", "Error",
                                    MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        public static bool DuplicateSpacesOrDBsFound(MD_DataModel data)
        {
            var duplicateOrphanedDbNames = data.OrphanedDBs
                .GroupBy(db => db.DB.Name)
                .Where(g => g.Count() > 1)
                .SelectMany(g => g.Select(db => new { DBName = db.DB.Name, ElementId = db.DB.Id }))
                .ToList();

            var duplicateSpaceNames = data.OrphanedSpaces
                .GroupBy(s => s.SpaceName)
                .Where(g => g.Count() > 1)
                .SelectMany(g => g.Select(s => new { ElementId = s.Id, SpaceName = s.SpaceName }))
                .ToList();

            if (duplicateOrphanedDbNames.Any() || duplicateSpaceNames.Any())
            {
                var message = new StringBuilder();

                message.AppendLine("Please set up unique names for DBs and Spaces");
                message.AppendLine();

                if (duplicateOrphanedDbNames.Any())
                {
                    message.AppendLine("Duplicate DB Names:");
                    foreach (var entry in duplicateOrphanedDbNames)
                    {
                        message.AppendLine($"ElementId: {entry.ElementId}, Name: {entry.DBName}");
                    }
                    message.AppendLine(); 
                }

                if (duplicateSpaceNames.Any())
                {
                    message.AppendLine("Duplicate Space Names:");
                    foreach (var entry in duplicateSpaceNames)
                    {
                        message.AppendLine($"ElementId: {entry.ElementId}, Name: {entry.SpaceName}");
                    }
                }

                MessageBox.Show(message.ToString(), "Duplicate entries found", MessageBoxButton.OK, MessageBoxImage.Warning);
                return true;
            }
            else
            {
                return false;
            }
        }

        public static bool ValidateCollection<T>(ICollection<T> collection, string entityName)
        {
            if (collection == null || collection.Count == 0)
            {
                MessageBox.Show($"No {entityName} were found in the model, please set them up before running this tool.", "Element validation",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            return true;
        }

        public static string CopyTemplateToDocuments(Document doc, string templateFilePath)
        {
            try
            {
                var modelName = doc.Title;

                // Format the current date and time in a way that is acceptable for file names
                var formattedDate = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");

                // Create a valid file name by combining the model name and the formatted date
                var fileName = $"{modelName}_{formattedDate}.txt"; // Adjust extension if needed

                // Get the path to the user's Documents folder
                string documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);

                // Define the target subfolder path inside the Documents folder
                string targetFolder = Path.Combine(documentsPath, "Beca", "MaxDemand", modelName);

                // Ensure the target directory exists (create if not exists)
                if (!Directory.Exists(targetFolder))
                {
                    Directory.CreateDirectory(targetFolder);
                }

                // Combine the target folder with the file name to form the destination file path
                string destinationFilePath = Path.Combine(targetFolder, fileName);

                // Copy the file to the target folder
                File.Copy(templateFilePath, destinationFilePath, true); // 'true' to overwrite if file exists

                // Return the destination file path
                return destinationFilePath;
            }
            catch (Exception ex)
            {
                // Handle any exceptions (optional logging or re-throwing the exception)
                MessageBox.Show("Error copying file: " + ex.Message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
        }

        public static List<PDLoadRow> GetPDLookUpDataFromCSV(Document doc)
        {
            var pDLookupData = new List<PDLoadRow>();

            var lines = GetTheLoadDensityData(doc);

            if (lines != null)
            {
                try
                {
                    // Reading the CSV file
                    // Skipping the header and processing the rows
                    foreach (var line in lines.Take(69))
                    {
                        var columns = line.Split(',');

                        // Check if the expected number of columns is present
                        if (columns.Length < 7)
                        {
                            continue; // Skip this row if not enough columns
                        }

                        // Safely convert each column and handle potential conversion issues
                        double lighting = 0, power = 0, mechanical = 0;

                        double.TryParse(columns[4], out lighting);
                        double.TryParse(columns[5], out power);
                        double.TryParse(columns[6], out mechanical);

                        var pDLoadRow = new PDLoadRow
                        {
                            Code = columns[0]?.Trim(),
                            SpatialFunction = columns[3]?.Trim(),
                            LightingDensity = lighting,
                            PowerDensity = power,
                            MechanicalDensity = mechanical
                        };

                        pDLookupData.Add(pDLoadRow);
                    }
                }
                catch (Exception ex)
                {
                    // Handle exceptions, e.g., log them or show a message
                    MessageBox.Show($"Error reading the CSV file: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }

            return pDLookupData;
        }

        private static string[] GetTheLoadDensityData(Document doc)
        {
            string[] lines = null;

            try
            {
                // Check what's in Project Info
                var loadDensityParam = doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Load_Density_Data);

                if (loadDensityParam != null)
                {
                    if (!Enum.IsDefined(typeof(LoadDensityValue), loadDensityParam.AsInteger()))
                    {
                        MessageBox.Show("Beca_MD_Load_Density_Data parameter value in Project Information doesn't match with any of the available data.\nDefault PD_Beca will be used.",
                            "Info", MessageBoxButton.OK, MessageBoxImage.Information);
                        using (Transaction trans = new Transaction(doc, "Set Load Density Data"))
                        {
                            trans.Start();
                            // Set the value of the parameter to 1 (default PD_Beca)
                            loadDensityParam.Set(1);
                            trans.Commit();
                        }
                    }

                    // Execute switch only if loadDensityParam is not null
                    switch (loadDensityParam.AsInteger())
                    {
                        case 1:
                            lines = File.ReadAllLines(MD_Constants.PD_BecaPath);
                            break;
                        case 2:
                            lines = File.ReadAllLines(MD_Constants.PD_AS_NZA_3000Path);
                            break;
                        default:
                            // Handle unexpected values
                            lines = new string[0]; // Empty array as a fallback
                            break;
                    }
                }
                else
                {
                    throw new InvalidOperationException("Parameter 'Beca_MD_Load_Density_Data' not found in Project Information.");
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            return lines;
        }

        public static Parameter GetDBZoneParameter(Element e)
        {
            return e.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone);
        }

        public static string GetDBZoneParameterValue(Element e)
        {
            return GetDBZoneParameter(e)?.AsString() ?? string.Empty;
        }

        public static double StringToDoubleForDiversities(string input, double defaultValue = 0)
        {
            input = input.TrimEnd('%');
            if (double.TryParse(input, out double result))
            {
                return result / 100; // Convert to fraction for MD_Data
            }
            return defaultValue;
        }

        public static double StringToDoubleForPF(string input, double defaultValue = 0)
        {
            if (double.TryParse(input, out double result))
            {
                if (result >= 0 && result <= 1)
                {
                    return result;
                }
            }
            return defaultValue;
        }

        public static RoomSpaceState GetSpaceStatus(Space space)
        {
            RoomSpaceState res;
            res = RoomSpaceState.Unknown;

            if (RevitUnitConvertor.InternalToSquareMeters(space.Area) > 0)
            {
                res = RoomSpaceState.Placed;
            }
            else if (space.Location == null)
            {
                res = RoomSpaceState.Unplaced;
            }
            else
            {
                SpatialElementBoundaryOptions options = new SpatialElementBoundaryOptions();
                IList<IList<BoundarySegment>> boundarySegments = space.GetBoundarySegments(options);

                res = (boundarySegments == null || boundarySegments.Count == 0)
                    ? RoomSpaceState.Unbounded
                    : RoomSpaceState.Redundant;
            }

            return res;
        }

        public static IEnumerable<IGrouping<string, MD_DBModel>> GroupDbsByPowerSupplyClass(ObservableCollection<MD_ZoneModel> zones)
        {
            return zones.SelectMany(z => z.DBs)
                               .GroupBy(db => db.PowerSupplyClass)
                               .OrderBy(group => CustomOrderIndex(group.Key)); ;
        }

        private static int CustomOrderIndex(string powerSupplyClass)
        {
            return powerSupplyClass switch
            {
                "G" => 0,
                "E" => 1,
                "U" => 2,
                _ => 3 // Default case for unexpected values 
            };
        }

        public static bool AddSpaceTypesNZ(Document doc)
        {
            // AddSpaceTypesNZ
            // Adds any space types that are currently missing from the model.

            using (var trans = new Transaction(doc, "Adding Space Types NZ"))
            {
                trans.Start();
                for (int i = 1; i < MD_Constants.NZSpaceTypes.Length; i++)
                {
                    for (int j = 0; j < MD_Constants.NZSpaceTypes[i].Length; j++)
                    {
                        string spaceTypeName = MD_Constants.SpacePrefixNZ + MD_Constants.NZSpaceTypes[i][j];
                        if (!SpaceTypeExists(doc, spaceTypeName))
                        {
                            HVACLoadSpaceType.Create(doc, MD_Constants.SpacePrefixNZ + MD_Constants.NZSpaceTypes[i][j]);
                        }
                    }
                }
                if (trans.Commit() == TransactionStatus.Committed)
                {
                    return true;
                }
                else
                {
                    MessageBox.Show("Fail to add NZ Space Types", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    trans.Dispose();
                    return false;
                }
            }
        }

        private static bool SpaceTypeExists(Document doc, string s)
        {
            //
            // SpaceTypeExists
            //
            // Tests if the passed string matches a HVACLoadSpaceType name in the model. It covers the case it is a valid string but not in the model. Note
            // that you must give the fill string including locale constant (eg "Au - " or "NZ - ") to avoid any confusion over selecting HVACLoadSpaceTypes.
            //

            // We assume the HVACLoadTypes are already loaded at this point.
            foreach (HVACLoadSpaceType h in HVACSpaceTypesInModel(doc))
                if (s.CompareTo(h.Name) == 0)
                    return true;

            return false;
        }

        public static List<HVACLoadSpaceType> HVACSpaceTypesInModel(Document doc)
        {
            // Clearing the list.
            var HVACSpaceTypes = new List<HVACLoadSpaceType>();

            // Finding all HVACLoadSpaceTypes in the project and getting iterator.
            var feiSpaceTypes = new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_HVAC_Load_Space_Types).GetElementIdIterator();
            feiSpaceTypes.Reset();

            // Iterating through the space types, adding them to the list as we go.
            while (feiSpaceTypes.MoveNext())
            {
                HVACLoadSpaceType h = doc.GetElement(feiSpaceTypes.Current) as HVACLoadSpaceType;
                HVACSpaceTypes.Add(h);
            }

            // Sorting the list.
            return HVACSpaceTypes.OrderBy(x => x.Name.ToString()).ToList();
        }

        /// <summary>
        /// Returns a list of panel names for all "parent" distribution boards in the model.
        /// A board is considered a parent if at least one other board's "Supply From" 
        /// parameter matches its panel name.
        /// </summary>
        public static List<string> GetAllParentPanelNames(Document doc)
        {
            // Collect all FamilyInstances in ElectricalEquipment
            var dBInstances = new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_ElectricalEquipment)
                .OfClass(typeof(FamilyInstance)).Cast<FamilyInstance>().ToList();

            // Build a list of objects containing:
            // - The DB instance
            // - Its Panel Name (RBS_ELEC_PANEL_NAME)
            // - Its Supply From (RBS_ELEC_PANEL_SUPPLY_FROM_PARAM)
            var boardData = dBInstances.Select(fi => new
            {
                Element = fi,
                PanelName = fi.get_Parameter(BuiltInParameter.RBS_ELEC_PANEL_NAME)?.AsString(),
                SupplyFrom = fi.get_Parameter(BuiltInParameter.RBS_ELEC_PANEL_SUPPLY_FROM_PARAM)?.AsString()
            }).ToList();

            // Identify all panel names that appear in a DB's "Supply From".
            // Any db whose PanelName is in this set is, by definition, a "parent" DB.
            HashSet<string> distinctParentNames = new HashSet<string>( boardData.Where(x => !string.IsNullOrWhiteSpace(x.SupplyFrom))
                    .Select(x => x.SupplyFrom), StringComparer.OrdinalIgnoreCase );

            // Gather the unique PanelNames of all Dbs that match any name in 'distinctParentNames'.
            var parentPanelNames = boardData.Where(x => !string.IsNullOrWhiteSpace(x.PanelName) && distinctParentNames.Contains(x.PanelName))
                .Select(x => x.PanelName).Distinct(StringComparer.OrdinalIgnoreCase).ToList();

            // These are all DBs that feed at least one child board
            return parentPanelNames;
        }

    }
}
