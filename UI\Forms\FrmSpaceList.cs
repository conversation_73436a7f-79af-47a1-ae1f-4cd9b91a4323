﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using BecaRevitUtilities;
using BecaRevitUtilities.ElementUtilities;
using MEP.MaxDemand.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.MaxDemand.UI.Forms
{
    public partial class FrmSpaceList : System.Windows.Forms.Form
    {
        List<Space> _spaces;

        public List<Space> SelectedSpaces;

        public FrmSpaceList(MD_DataModel data)
        {
            InitializeComponent();

            _spaces = data.OrphanedSpaces
                .GroupBy(s => new { s.Number, s.Name }) 
                .Select(g => g.First())                 
                .OrderBy(s => s.Number)                 
                .ToList();

            SelectedSpaces = new List<Space>();

            foreach (var s in _spaces)
            {
                int rowIndex = dgv_Spaces.Rows.Add(
                    s.GetElementIdValue().ToString(), 
                    GetSpaceState(s).ToString(), 
                    s.Level.Name, 
                    s.Number, 
                    s.Name, 
                    Math.Round(RevitUnitConvertor.InternalToSquareMeters(s.Area), 2) + " m²");

                DataGridViewRow row = dgv_Spaces.Rows[rowIndex];

                RoomSpaceState spaceState = GetSpaceState(s);
                if (spaceState != RoomSpaceState.Placed)
                {
                    DataGridViewCell lastCell = row.Cells[Status.Index];
                    lastCell.Style.BackColor = System.Drawing.Color.Red;
                    lastCell.Style.ForeColor = System.Drawing.Color.White;
                }
            }
            
        }

        private void btn_Add_Click(object sender, EventArgs e)
        {
            var sb = new StringBuilder();
            foreach (DataGridViewRow row in dgv_Spaces.SelectedRows)
            {
                var space = _spaces.Find(s => s.Name == row.Cells[SpaceName.Index].Value.ToString());
                if (row.Cells[Status.Index].Value.ToString() == "Placed" || space.Area > 0)
                {
                    SelectedSpaces.Add(_spaces.Find(s => s.Name == row.Cells[SpaceName.Index].Value.ToString()));
                }
                else
                {
                    sb.AppendLine($"Number: {space.Number}, " +
                        $"Name: {space.Name}, " +
                        $"Status: {row.Cells[Status.Index].Value.ToString()}, " +
                        $"Area: {Math.Round(RevitUnitConvertor.InternalToSquareMeters(space.Area), 2) + " m²"}");
                }
            }

            if (sb.Length > 0)
            {
                MessageBox.Show($"The following spaces will not be added to the list due to their status or area:\n\n{sb}");
            }
        }

        private RoomSpaceState GetSpaceState(Space space)
        {
            RoomSpaceState res;
            res = RoomSpaceState.Unknown;

            if (RevitUnitConvertor.InternalToSquareMeters(space.Area) > 0)
            {
                res = RoomSpaceState.Placed;
            }
            else if (space.Location == null)
            {
                res = RoomSpaceState.Unplaced;
            }
            else
            {
                SpatialElementBoundaryOptions options = new SpatialElementBoundaryOptions();
                IList<IList<BoundarySegment>> boundarySegments = space.GetBoundarySegments(options);

                res = (boundarySegments == null || boundarySegments.Count == 0)
                    ? RoomSpaceState.Unbounded
                    : RoomSpaceState.Redundant;
            }

            return res;
        }
    }
}
