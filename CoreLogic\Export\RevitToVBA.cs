﻿using BecaActivityLogger.CoreLogic.Data;
using MEP.MaxDemand.CoreLogic.Export.Parameters;
using MEP.MaxDemand.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;
using MessageBox = System.Windows.MessageBox;

namespace MEP.MaxDemand.CoreLogic.Export
{
    public enum MacroNames
    {
        TestFlow,
        AddZone,
        AddSpace,
        AddDB,
        ClearCalculation
    }

    public enum DBClass
    {
        G,
        E,
        U
    }

    public static class RevitToVBA
    {
        public static void ExportMD(string workbookPath, MD_DataModel data, BecaActivityLoggerData logger)
        {
            int zoneIndex = 17;
            int spaceIndex = zoneIndex + 2;
            int dbIndex = zoneIndex + 2;

            // Initialize Excel application
            Excel.Application excelApp = null;
            Excel.Workbook workbook = null;

            try
            {
                excelApp = new Excel.Application();
                excelApp.Visible = true;

                // Open the workbook (change the path to your workbook)
                workbook = excelApp.Workbooks.Open(workbookPath);

                foreach (var dBZone in data.Zones)
                {
                    // 1. Export Zone
                    CallVbaSubroutine(excelApp, MacroNames.AddZone, new AddZoneParameters 
                    { 
                        TargetRow = zoneIndex, 
                        ZoneName = dBZone.ZoneName, 
                        EssentialPower = dBZone.EssentialSmallPowerLoadPercentage, 
                        EssentialLighting = dBZone.EssentialLightingLoadPercentage, 
                        EssentialMech = dBZone.EssentialMechanicalLoadPercentage, 
                        EssentialLump = dBZone.EssentialLumpLoadPercentage, 
                        UninterruptablePower = dBZone.UninteruptableSmallPowerLoadPercentage, 
                        UninterruptableLighting = dBZone.UninteruptableLightingLoadPercentage, 
                        UninterruptableMech = dBZone.UninteruptableMechanicalLoadPercentage, 
                        UninterruptableLump = dBZone.UninteruptableLumpLoadPercentage
                    });

                    // 2. Export Spaces
                    foreach (var mD_Space in dBZone.Spaces)
                    {
                        CallVbaSubroutine(excelApp, MacroNames.AddSpace, new AddSpaceParameters 
                        { 
                            TargetRow = spaceIndex, 
                            SpaceName = mD_Space.SpaceName, 
                            SpaceLevel = mD_Space.Level.Name, 
                            SpaceArea = mD_Space.Area, 
                            SpaceFunction = mD_Space.SpatialFunction, 
                            LumpSingle = mD_Space.SinglePhaseLumpLoad, 
                            LumpThree = mD_Space.ThreePhaseLumpLoad, 
                            LumpDiversity = mD_Space.LumpLoadDiversity 
                        });

                        spaceIndex++;
                    }
                    
                    // 3. Export DBs
                    foreach (var mD_DB in dBZone.DBs)
                    {
                        CallVbaSubroutine(excelApp, MacroNames.AddDB, new AddDBParameters 
                        { TargetRow = dbIndex, 
                            DbName = mD_DB.DB.Name, 
                            DbClass = mD_DB.PowerSupplyClass, 
                            DbPower = mD_DB.IsSmallPower, 
                            DbLighting = mD_DB.IsLighting, 
                            DbMech = mD_DB.IsMech, 
                            DbLump = mD_DB.IsLump,
                            DbCableLength = mD_DB.DBCableLength,
                            DbParent = mD_DB.ParentDB.Name
                        });

                        dbIndex++;
                    }

                     //CallVbaSubroutine(excelApp, MacroNames.ClearCalculation, null);
                }

                // Save and close the workbook
                workbook.Save();

                // Open the folder containing the workbook
                string folderPath = Path.GetDirectoryName(workbookPath);
                if (Directory.Exists(folderPath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = folderPath,
                        UseShellExecute = true,
                        Verb = "open"
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("An error occurred: " + ex.Message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                logger.Log($"An error occurred: " + ex.Message, LogType.Error);
            }
            finally
            {
                try
                {
                    // Cleanup workbook
                    if (workbook != null)
                    {
                        workbook.Close(false);
                        Marshal.ReleaseComObject(workbook);
                        workbook = null;
                    }
                }
                catch (Exception ex)
                {
                    // Log or handle exception, but don't throw
                    logger.Log($"Error closing workbook: {ex.Message}", LogType.Error);
                }

                try
                {
                    // Quit and cleanup Excel app
                    if (excelApp != null)
                    {
                        excelApp.Quit();
                        Marshal.ReleaseComObject(excelApp);
                        excelApp = null;
                    }
                }
                catch (Exception ex)
                {
                    // Log or handle exception, but don't throw
                    logger.Log($"Error quitting Excel: {ex.Message}", LogType.Error);
                }

                // Avoid forcing GC.Collect and WaitForPendingFinalizers manually
            }
        }

        private static void CallVbaSubroutine(Excel.Application excelApp, MacroNames macroName, MacroParameters parameters = null)
        {
            if (parameters == null || parameters.Parameters.Length == 0)
            {
                excelApp.GetType().InvokeMember("Run",
                    System.Reflection.BindingFlags.Default |
                    System.Reflection.BindingFlags.InvokeMethod,
                    null, excelApp, new object[] { macroName.ToString() });
            }
            else
            {
                object[] args = new object[parameters.Parameters.Length + 1];
                args[0] = macroName.ToString();
                Array.Copy(parameters.Parameters, 0, args, 1, parameters.Parameters.Length);

                excelApp.GetType().InvokeMember("Run",
                    System.Reflection.BindingFlags.Default |
                    System.Reflection.BindingFlags.InvokeMethod,
                    null, excelApp, args);
            }
        }
    }
}
