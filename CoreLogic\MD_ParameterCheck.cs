﻿using Autodesk.Revit.DB;
using BecaRevitUtilities.SharedParametersUtilities;
using BecaRevitUtilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.UI;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.MaxDemand.CoreLogic
{
    public class MD_ParameterCheck
    {
        private Document _doc;
        private List<ProjectParameterData> _projectParametersData;

        private readonly List<string> _mDSpaceParameterNames = new List<string>()
        {
            MD_Constants.SpaceParameterName_Lighting_Power_Density,
            MD_Constants.SpaceParameterName_Small_Power_Density,
            MD_Constants.SpaceParameterName_Mech_Power_Density,
            MD_Constants.SpaceParameterName_Lump_Load_1Phase,
            MD_Constants.SpaceParameterName_Lump_Load_3Phase,
            MD_Constants.SpaceParameterName_Lump_Load_Diversity,
            MD_Constants.SpaceParameterName_Spatial_Function
        };
         
        private readonly List<string> _mDDBParameterNames = new List<string>()
        {
            MD_Constants.DBParameterName_MD_Lighting,
            MD_Constants.DBParameterName_MD_Lump,
            MD_Constants.DBParameterName_MD_Mech,
            MD_Constants.DBParameterName_MD_Power_Supply_Class,
            MD_Constants.DBParameterName_MD_Small_Power
        };

        private readonly List<string> _mDProjectInfoParameterNames = new List<string>()
        {
            MD_Constants.ProjectInfoParameterName_Site_Diversity,
            MD_Constants.ProjectInfoParameterName_Site_Spare_Capacity,
            MD_Constants.ProjectInfoParameterName_Diversity_Lighting,
            MD_Constants.ProjectInfoParameterName_Diversity_Mech,
            MD_Constants.ProjectInfoParameterName_Diversity_Power,
            MD_Constants.ProjectInfoParameterName_Load_Density_Data,
            MD_Constants.ProjectInfoParameterName_PF_Lighting,
            MD_Constants.ProjectInfoParameterName_PF_Power,
            MD_Constants.ProjectInfoParameterName_PF_Mech,
            MD_Constants.ProjectInfoParameterName_PF_Lump,
            MD_Constants.ProjectInfoParameterName_Engineer,

        };

        public MD_ParameterCheck(Document doc)
        {
            _doc = doc;
            _projectParametersData = GetProjectParameterData();
#if TargetYear2021 || TargetYear2022 || TargetYear2023 || TargetYear2024
            BuiltInParameterGroup group = BuiltInParameterGroup.PG_ELECTRICAL;
#else
            ForgeTypeId group = GroupTypeId.Electrical;
#endif

            // Space parameter check
            CheckSingleCategoryParameters(BuiltInCategory.OST_MEPSpaces, group, _mDSpaceParameterNames);
            // DB parameter check
            CheckSingleCategoryParameters(BuiltInCategory.OST_ElectricalEquipment, group, _mDDBParameterNames);
            // Project info parameter check
            CheckSingleCategoryParameters(BuiltInCategory.OST_ProjectInformation, group, _mDProjectInfoParameterNames);
        }
#if TargetYear2021 || TargetYear2022 || TargetYear2023 || TargetYear2024
        private void CheckSingleCategoryParameters(BuiltInCategory bic, BuiltInParameterGroup group, List<string> parameterNames)
#else
        private void CheckSingleCategoryParameters(BuiltInCategory bic, ForgeTypeId group, List<string> parameterNames)

#endif
        {
            CategorySet categorySet = new CategorySet();
            categorySet.Insert(Category.GetCategory(_doc, bic));

            var parametersInCategory = _projectParametersData.Where(p => p.Binding.Categories.Contains(Category.GetCategory(_doc, bic))).Select(pd => pd.Name).ToList();

            CheckParameters(parametersInCategory, parameterNames, categorySet, group);
        }
#if TargetYear2021 || TargetYear2022 || TargetYear2023 || TargetYear2024
        private void CheckParameters(List<string> parametersInCategory, List<string> parameterNames, CategorySet categorySet, BuiltInParameterGroup group)
#else
        private void CheckParameters(List<string> parametersInCategory, List<string> parameterNames, CategorySet categorySet, ForgeTypeId group)
#endif
        {
            foreach (var parameterName in parameterNames)
            {
                if (!parametersInCategory.Contains(parameterName))
                {
                    if (TaskDialogUtility.ParameterTaskDialog(parameterName))
                    {
                        try
                        {
                            using (var trans = new Transaction(_doc, "Add parameter"))
                            {
                                trans.Start();
                                SharedParameterUtility.AddProjectParameter(_doc, _doc.Application, parameterName, categorySet, group, true, MD_Constants.SharedParameterPath + "\\Beca_77_ELEC_Shared_Parameters.txt");
                                trans.Commit();
                            }
                        }
                        catch (Exception e)
                        {
                            TaskDialog.Show("Parameter creation exception", e.Message);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Returns a list of the objects containing 
        /// references to the project parameter definitions
        /// </summary>
        /// <param name="doc">The project document being quereied</param>
        /// <returns></returns>
        private List<ProjectParameterData> GetProjectParameterData()
        {
            List<ProjectParameterData> result = new List<ProjectParameterData>();

            BindingMap map = _doc.ParameterBindings;
            DefinitionBindingMapIterator it = map.ForwardIterator();
            it.Reset();
            while (it.MoveNext())
            {
                ProjectParameterData newProjectParameterData
                  = new ProjectParameterData();

                newProjectParameterData.Definition = it.Key;
                newProjectParameterData.Name = it.Key.Name;
                newProjectParameterData.Binding = it.Current
                  as ElementBinding;

                result.Add(newProjectParameterData);
            }
            return result;
        }
    }

    /// <summary>
    /// This class contains information discovered 
    /// about a (shared or non-shared) project parameter 
    /// </summary>
    public class ProjectParameterData
    {
        public Definition Definition = null;
        public ElementBinding Binding = null;
        public string Name = null;                // Needed because accsessing the Definition later may produce an error.
        public bool IsSharedStatusKnown = false;  // Will probably always be true when the data is gathered
        public bool IsShared = false;
        public string GUID = null;
    }
}
