﻿<Page
    x:Class="MEP.MaxDemand.UI.View.MD_ProjectInfo"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.MaxDemand.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.MaxDemand.UI.View"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:validations="clr-namespace:MEP.MaxDemand.Validations"
    Title="MD_ProjectInfo"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="White"
    mc:Ignorable="d">

    <!--  Resources  -->
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <!--  Page-specific resources  -->
            <converters:PercentageConverter x:Key="PercentageConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="400" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  Border  -->
        <Rectangle Grid.Column="1" Fill="DimGray" />
        <Rectangle Grid.RowSpan="5" Fill="DimGray" />
        <Rectangle
            Grid.RowSpan="5"
            Grid.Column="2"
            Fill="DimGray" />
        <Rectangle
            Grid.Row="4"
            Grid.Column="1"
            Fill="DimGray" />

        <!--  Header  -->
        <TextBlock
            Grid.Row="1"
            Grid.ColumnSpan="3"
            Margin="10,5,10,10"
            HorizontalAlignment="Center"
            Style="{StaticResource MaterialDesignHeadline4TextBlock}"
            Text="PROJECT INFORMATION" />
        <Separator
            Grid.Row="1"
            Grid.Column="1"
            Margin="0,55,0,0"
            Background="#FFCE00">
            <Separator.LayoutTransform>
                <ScaleTransform ScaleY="3.5" />
            </Separator.LayoutTransform>
        </Separator>

        <!--  Project Info  -->
        <materialDesign:Card
            Grid.Row="2"
            Grid.Column="1"
            Margin="0,20,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="145" />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <TextBlock
                    Grid.Row="0"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    Margin="10,10,10,10"
                    HorizontalAlignment="Center"
                    FontStyle="Italic"
                    Foreground="Red"
                    Style="{StaticResource MaterialDesignBody2TextBlock}"
                    Text="NB: Changes here will update the Revit Project Information" />
                <TextBlock
                    Grid.Row="1"
                    Grid.Column="0"
                    Margin="20,10,0,0"
                    VerticalAlignment="Bottom"
                    FontWeight="Bold"
                    Text="Job Name :" />
                <TextBlock
                    Grid.Row="1"
                    Grid.Column="1"
                    Margin="0,10,20,0"
                    Text="{Binding Data.JobName}" />
                <TextBlock
                    Grid.Row="2"
                    Grid.Column="0"
                    Margin="20,10,0,0"
                    VerticalAlignment="Bottom"
                    FontWeight="Bold"
                    Text="Job Number :" />
                <TextBlock
                    Grid.Row="2"
                    Grid.Column="1"
                    Margin="0,10,20,0"
                    Text="{Binding Data.JobNumber}" />
                <TextBlock
                    Grid.Row="3"
                    Grid.Column="0"
                    Margin="20,10,0,0"
                    VerticalAlignment="Bottom"
                    FontWeight="Bold"
                    Text="Date :" />
                <TextBlock
                    Grid.Row="3"
                    Grid.Column="1"
                    Margin="0,10,20,0"
                    Text="{Binding Data.Date}" />
                <TextBlock
                    Grid.Row="4"
                    Grid.Column="0"
                    Margin="20,10,0,0"
                    VerticalAlignment="Bottom"
                    FontWeight="Bold"
                    Text="Drawing Revision :" />
                <TextBox
                    Grid.Row="4"
                    Grid.Column="1"
                    Margin="0,10,20,0"
                    Text="{Binding Data.Revision, Mode=TwoWay}" />
                <TextBlock
                    Grid.Row="5"
                    Grid.Column="0"
                    Margin="20,10,0,0"
                    VerticalAlignment="Bottom"
                    FontWeight="Bold"
                    Text="Engineer's Name :" />
                <TextBox
                    Grid.Row="5"
                    Grid.Column="1"
                    Margin="0,10,20,0"
                    Text="{Binding Data.Engineer, Mode=TwoWay}" />
                <TextBlock
                    Grid.Row="6"
                    Grid.Column="0"
                    Margin="20,10,0,0"
                    VerticalAlignment="Bottom"
                    FontWeight="Bold"
                    Text="Verifier's Name :" />
                <TextBox
                    Grid.Row="6"
                    Grid.Column="1"
                    Margin="0,10,20,0"
                    Text="{Binding Data.Verifier, Mode=TwoWay}" />
                <TextBlock
                    Grid.Row="7"
                    Grid.Column="0"
                    Margin="20,10,0,0"
                    VerticalAlignment="Bottom"
                    FontWeight="Bold"
                    Text="Reference Drawing :" />
                <TextBox
                    Grid.Row="7"
                    Grid.Column="1"
                    Margin="0,10,20,0"
                    Text="{Binding Data.ReferenceDrawing, Mode=TwoWay}" />
                <TextBlock
                    Grid.Row="8"
                    Grid.Column="0"
                    Margin="20,10,0,0"
                    VerticalAlignment="Bottom"
                    FontWeight="Bold"
                    Text="Site Spare Capacity :" />
                <ComboBox
                    Grid.Row="8"
                    Grid.Column="1"
                    Margin="0,10,20,0"
                    ItemsSource="{Binding Data.SiteSpareCapacityOptions}"
                    SelectedItem="{Binding Data.SiteSpareCapacity, UpdateSourceTrigger=PropertyChanged}">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Converter={StaticResource PercentageConverter}}" />
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>
                <TextBlock
                    Grid.Row="9"
                    Grid.Column="0"
                    Margin="20,10,0,20"
                    VerticalAlignment="Bottom"
                    FontWeight="Bold"
                    Text="Site Diversity :" />
                <ComboBox
                    Grid.Row="9"
                    Grid.Column="1"
                    Margin="0,10,20,20"
                    ItemsSource="{Binding Data.SiteDiversityOptions}"
                    SelectedItem="{Binding Data.SiteDiversity, UpdateSourceTrigger=PropertyChanged}">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Converter={StaticResource PercentageConverter}}" />
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>
            </Grid>
        </materialDesign:Card>

        <!--  Under Button  -->
        <Button
            Grid.Row="3"
            Grid.Column="1"
            Width="100"
            Margin="60,20,0,20"
            HorizontalAlignment="Left"
            Background="#12A8B2"
            BorderBrush="#12A8B2"
            Click="BackToZones_Click"
            Content="Back"
            Foreground="White" />
        <Button
            Grid.Row="3"
            Grid.Column="1"
            Width="100"
            Margin="140,20,60,20"
            HorizontalAlignment="Right"
            Background="#FFCE00"
            BorderBrush="#FFCE00"
            Command="{Binding SaveProjectInfoCommand}"
            Content="Save"
            Foreground="Black" />

        <!--  Footer  -->
        <TextBlock
            Grid.Row="4"
            Grid.Column="1"
            Margin="10,5,0,10"
            HorizontalAlignment="Right"
            VerticalAlignment="Bottom"
            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
            Text="Make Everyday Better" />
        <TextBlock
            Grid.Row="4"
            Grid.Column="1"
            Margin="0,5,10,10"
            VerticalAlignment="Bottom"
            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
            Text="Beca" />
    </Grid>
</Page>
