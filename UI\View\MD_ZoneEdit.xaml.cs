﻿using CommunityToolkit.Mvvm.Messaging;
using MEP.MaxDemand.CoreLogic;
using MEP.MaxDemand.Models;
using MEP.MaxDemand.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using DataGrid = System.Windows.Controls.DataGrid;

namespace MEP.MaxDemand.UI.View
{
    /// <summary>
    /// Interaction logic for MD_ZoneEdit.xaml
    /// </summary>
    public partial class MD_ZoneEdit : Page
    {
        private MD_ViewModel _viewModel;
        private bool _isDragging = false;

        public MD_ZoneEdit(MD_ViewModel viewModel)
        {
            InitializeComponent();

            _viewModel = viewModel;
            DataContext = viewModel;

            //_viewModel.SelectedHVACTopology = MD_Constants.HVACTopologyDictionary
            //        .FirstOrDefault(x => x.Value == _viewModel.SelectedZone?.HVACTopologyLoadFactor).Key ?? _viewModel.HVACTopologyOptions[0];

        }

        private void BackToZones_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.Navigate(new MD_DataPage(_viewModel));
        }

        private void UnassignedSpaces_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.Navigate(new MD_UnassignedSpaces(_viewModel));
        }

        private void RemoveSpacesButton_Click(object sender, RoutedEventArgs e)
        {
            //if (DataContext is MD_ViewModel viewModel)
            //{
            //    var selectedItems = dataGrid_Spaces.SelectedItems;
            //    var selectedSpaces = new List<MD_SpaceModel>();
            //    foreach (var item in selectedItems)
            //    {
            //        if (item is MD_SpaceModel space)
            //        {
            //            selectedSpaces.Add(space);
            //        }
            //    }
            //    viewModel.SelectedSpaces = selectedSpaces;
            //}
        }

        private void ListBox_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            //if (DataContext is MD_ViewModel viewModel)
            //{
            //    var selectedItems = dataGrid_Spaces.SelectedItems;
            //    var selectedSpaces = new List<MD_SpaceModel>();
            //    foreach (var item in selectedItems)
            //    {
            //        if (item is MD_SpaceModel space)
            //        {
            //            selectedSpaces.Add(space);
            //        }
            //    }
            //    viewModel.SelectedSpaces = selectedSpaces;

            //    var selectedPDLoadRow = (sender as System.Windows.Controls.ListBox)?.SelectedItem as PDLoadRow;
            //    viewModel.SelectedPDLoadRow = selectedPDLoadRow;
            //}
        }

        private void dataGrid_Spaces_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is DataGrid dataGrid && dataGrid.SelectedItem is MD_SpaceModel selectedSpace)
            {
                _viewModel.SelectedParentSpace = selectedSpace;
            }
        }
    }
}
