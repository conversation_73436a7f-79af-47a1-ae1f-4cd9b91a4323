﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.ExtensibleStorage;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using Common.UI.Forms;
using MEP.MaxDemand.CoreLogic;
using MEP.MaxDemand.CoreLogic.Calculation;
using MEP.MaxDemand.CoreLogic.Export;
using MEP.MaxDemand.CoreLogic.MD_DataStorage;
using MEP.MaxDemand.Models;
using MEP.MaxDemand.UI.Forms;
using MEP.MaxDemand.UI.View;
using System.Data;
using System.IO;
using System.Text;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.MaxDemand.UI.ModelessRevitForm
{
    public partial class FrmModelessMaxDemandMain : BecaBaseForm
    {

        #region Fields

        RequestHandler _handler;
        ExternalEvent _externalEvent;
        BecaActivityLoggerData _logger;
        
        UIApplication _uiapp;

        ContextMenuStrip _contextMenu;
        string _zoneNamePlaceholderText = "Enter zone name ...";

        bool isFormLoading = true;

        public MD_ZoneModel SelectedDBZoneToEdit;
        public LoadDensityValue SelectedDensityValue;

        #endregion

        #region Properties
        public MD_DataModel Data;

        public string DBLighting { get; set; }
        public string DBMech { get; set; }
        public string DBSmallPower { get; set; }
        public string DBLump { get; set; }
        public string DBPowerSupplyClass { get; set; }

        #endregion

        #region Constructors

        public FrmModelessMaxDemandMain(ExternalEvent exEvent, RequestHandler handler, BecaActivityLoggerData logger, UIApplication uiapp, MD_DataModel data)
        {
            InitializeComponent();

            _handler = handler;
            _externalEvent = exEvent;
            _logger = logger;

            _uiapp = uiapp;
            Data = data;

            tb_DBZone.Text = _zoneNamePlaceholderText;

            LoadDensityValueLookupData();

            PopulateDBZoneDataGridView(data);

            PopulateDBZoneTreeView();

            PopulateOrphanedDBsList(Data);

            ReadProjectInfoParameters();

            InitializeContextMenu();

            isFormLoading = false;
        }


        #endregion

        #region Methods
        private void InitializeContextMenu()
        {
            // Initialize the context menu
            _contextMenu = new ContextMenuStrip();
            ToolStripMenuItem renameMenuItem = new ToolStripMenuItem("Rename");
            ToolStripMenuItem deleteMenuItem = new ToolStripMenuItem("Delete");

            _contextMenu.Items.AddRange(new ToolStripMenuItem[] { renameMenuItem, deleteMenuItem });

            // Add event handlers for the context menu items
            renameMenuItem.Click += RenameDBZoneMenuItem_Click;
            deleteMenuItem.Click += DeletedbZoneMenuItem_Click;

        }

        private void RenameDBZoneMenuItem_Click(object sender, EventArgs e)
        {
            TreeNode selectedNode = tv_DBZones.SelectedNode;
            if (selectedNode?.Text.Contains("locked", StringComparison.OrdinalIgnoreCase) == false)
            {
                RenameZone(selectedNode.Text, selectedNode);
            }
            else if (selectedNode != null)
            {
                MessageBox.Show("Locked zone cannot be edited.");
            }
        }

        private void RenameZone(string dBZoneName, TreeNode selectedNode)
        {
            string newDBZoneName = Microsoft.VisualBasic.Interaction.InputBox("Enter new name:", "Rename Node", selectedNode.Text);
            if (!string.IsNullOrEmpty(newDBZoneName))
            {
                selectedNode.Text = newDBZoneName;

                // Rename DB Zone
                var dbZone = Data.Zones.Find(z => z.ZoneName == dBZoneName);
                if (dbZone != null)
                {
                    dbZone.ZoneName = newDBZoneName;

                    PopulateDBZoneDataGridView(Data);

                    SelectedDBZoneToEdit = dbZone;

                    MakeRequest(RequestId.RenameDBZoneInDBsAndSpaces);
                }
            }
        }

        private void DeletedbZoneMenuItem_Click(object sender, EventArgs e)
        {
            TreeNode selectedNode = tv_DBZones.SelectedNode;
            if (selectedNode?.Text.Contains("locked", StringComparison.OrdinalIgnoreCase) == false)
            {
                var result = MessageBox.Show($"Are you sure you want to delete {selectedNode.Text}?", "Delete Node", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (result == DialogResult.Yes)
                {
                    selectedNode.Remove();

                    // Add code to remove DBZone
                    var dbZone = Data.Zones.Find(z => z.ZoneName == selectedNode.Text);
                    if (dbZone != null)
                    {
                        SelectedDBZoneToEdit = dbZone;

                        MakeRequest(RequestId.ClearDBZoneInDBsAndSpaces);
                    }
                }
            }
            else if (selectedNode != null)
            {
                MessageBox.Show("Locked zone cannot be edited.");
            }
        }

        private void tv_DBZones_KeyDown(object sender, KeyEventArgs e)
        {
            // Check if the F2 key was pressed
            if (e.KeyCode == Keys.F2)
            {
                TreeNode selectedNode = tv_DBZones.SelectedNode;
                if (selectedNode != null)
                {
                    RenameZone(selectedNode.Text, selectedNode);
                }

                // Mark the event as handled
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        private void ReadProjectInfoParameters()
        {
            var projectInfo = _uiapp.ActiveUIDocument.Document.ProjectInformation;
            tb_DiversityLighting.Text = (projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Lighting).AsDouble() * 100).ToString() + "%";
            tb_DiversityPower.Text = (projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Power).AsDouble() * 100).ToString() + "%";
            tb_DiversityMech.Text = (projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Mech).AsDouble() * 100).ToString() + "%";
            tb_PFLighting.Text = (projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Lighting).AsDouble()).ToString();
            tb_PFPower.Text = (projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Power).AsDouble()).ToString();
            tb_PFMech.Text = (projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Mech).AsDouble()).ToString();
            tb_PFLump.Text = (projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Lump).AsDouble()).ToString();
        }

        private void LoadDensityValueLookupData()
        {
            cb_DensityValues.Items.Clear();
            foreach (var value in Enum.GetValues(typeof(LoadDensityValue)))
            {
                cb_DensityValues.Items.Add(value.ToString());
            }

            var currentDensityValue = (LoadDensityValue)_uiapp.ActiveUIDocument.Document.ProjectInformation
                .LookupParameter(MD_Constants.ProjectInfoParameterName_Load_Density_Data).AsInteger();

            // Select the item from project information
            cb_DensityValues.SelectedItem = currentDensityValue.ToString();

            SelectedDensityValue = currentDensityValue;
        }

        private void PopulateDBZoneTreeView()
        {
            foreach (MD_ZoneModel dbZone in Data.Zones)
            {
                var node = tv_DBZones.Nodes.Add(dbZone.ZoneName + (dbZone.IsLocked ? $" (Locked)" : ""));
                foreach (var db in dbZone.DBs)
                {
                    node.Nodes.Add(db.DB.Name + (db.IsLocked ? $" (Locked by {db.CurrentOwner}" : ""));
                }
                node.Expand();
            }
        }

        public void PopulateDBZoneDataGridView(MD_DataModel data)
        {
            eadgv_DbZone.Rows.Clear();

            foreach (var dBZone in data.Zones)
            {
                if (dBZone.DBs.Count > 0)
                {
                    var status = dBZone.IsLocked ? "LOCKED" : "EDIT";
                    // Calculate total properties
                    var rowIndex = eadgv_DbZone.Rows.Add(
                        status,
                        dBZone.ZoneName,
                        Math.Round(dBZone.Spaces.Sum(x => x.Area), 1) + " m²",
                        Math.Round(dBZone.Spaces.Sum(x => x.SmallPowerLoad), 2) + " kVA",
                        Math.Round(dBZone.Spaces.Sum(x => x.LightingLoad), 2) + " kVA",
                        Math.Round(dBZone.Spaces.Sum(x => x.MechLoad), 2) + " kVA",
                        Math.Round(dBZone.Spaces.Sum(x => x.LumpLoad), 2) + " kVA",
                        Math.Round(dBZone.CalculatedDiversityWeightedAverage, 2) + " %",
                        Math.Round(dBZone.Spaces.Sum(x => x.DiversifiedLoad_kVA), 2) + " kVA",
                        Math.Round(dBZone.TotalPowerFactorWeightedAverage, 2),
                        Math.Round(dBZone.Spaces.Sum(x => x.DiversifiedLoad_kW), 2) + " kW",
                        Math.Round(dBZone.Spaces.Sum(x => x.DiversifiedCurrent_A), 2) + " A");

                    // Disable the row if it is locked
                    if (status == "LOCKED")
                    {
                        var row = eadgv_DbZone.Rows[rowIndex];
                        row.ReadOnly = true; 
                        row.DefaultCellStyle.BackColor = System.Drawing.Color.LightGray; 
                    }
                }
            }
        }

        #region Modeless Functionality

        /// <summary>
        ///   A private helper method to make a request
        ///   and put the dialog to sleep at the same time.
        /// </summary>
        /// <remarks>
        ///   It is expected that the process which executes the request 
        ///   (the Idling helper in this particular case) will also
        ///   wake the dialog up after finishing the execution.
        /// </remarks>
        ///
        private void MakeRequest(RequestId request)
        {
            _handler.Request.Make(request);
            _externalEvent.Raise();
            DozeOff();

        }

        /// <summary>
        ///   Control enabler / disabler 
        /// </summary>
        ///
        private void EnableCommands(bool status)
        {
            foreach (System.Windows.Forms.Control ctrl in this.Controls)
            {
                ctrl.Enabled = status;
            }
            if (!status)
            {
                this.btn_Cancel.Enabled = true;
            }
        }

        /// <summary>
        ///   DozeOff -> disable all controls (but the Exit button)
        /// </summary>
        /// 
        private void DozeOff()
        {
            EnableCommands(false);
        }

        /// <summary>
        ///   WakeUp -> enable all controls
        /// </summary>
        /// 
        public void WakeUp()
        {
            EnableCommands(true);
        }


        #endregion

        #region UI

        #region UI Helpers



        #endregion

        #region Button Clicks
        private void tv_DBZones_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                if (e.Node != null && e.Node.Parent == null) // Only show context menu for parent nodes
                {
                    tv_DBZones.SelectedNode = e.Node; // Select the node under the mouse

                    // Manually show the context menu at the mouse location
                    _contextMenu.Show(tv_DBZones, e.Location);
                }
            }
        }

        private void OpenEditForm(bool openSelectedZone)
        {

            FrmModelessMaxDemand frmEditMaxDemand = new FrmModelessMaxDemand(_externalEvent, _handler, _uiapp, this);
         
            EditForm_ModelessHandler._frmEditMaxDemand = frmEditMaxDemand;
            EditForm_ModelessHandler.FrmEditMaxDemand.Show();
            frmEditMaxDemand.FormClosed += FrmEditMaxDemand_Closed;

            frmEditMaxDemand.BringToFront();
            this.SetTopLevel(false);
        }

        private void FrmEditMaxDemand_Closed(object sender, FormClosedEventArgs e)
        {
            this.SetTopLevel(true);
            PopulateDBZoneDataGridView(Data);
            this.BringToFront();

        }

        private void btn_Cancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btn_Save_Click(object sender, EventArgs e)
        {
            // Update data
            Data.DiversityLighting = MD_Helper.StringToDoubleForDiversities(tb_DiversityLighting.Text);
            Data.DiversityMech = MD_Helper.StringToDoubleForDiversities(tb_DiversityMech.Text);
            Data.DiversityPower = MD_Helper.StringToDoubleForDiversities(tb_DiversityPower.Text);
            Data.PowerFactorLighting = MD_Helper.StringToDoubleForPF(tb_PFLighting.Text);
            Data.PowerFactorMech = MD_Helper.StringToDoubleForPF(tb_PFMech.Text);
            Data.PowerFactorPower = MD_Helper.StringToDoubleForPF(tb_PFPower.Text);
            Data.PowerFactorLumpLoad = MD_Helper.StringToDoubleForPF(tb_PFLump.Text);

            // Test this if thi updates the diversity calculation % fraction after clicking save
            foreach (var s in Data.Zones.SelectMany(z => z.Spaces))
            {
                s.RunCalculation(Data);
            }

            // Update data in Revit
            MakeRequest(RequestId.SaveDataInMainForm);
        }

        //private void btn_SelectElements_Click(object sender, EventArgs e)
        //{
        //    MakeRequest(RequestId.SelectElements);
        //}

        //private void dgv_DbZone_CellContentClick(object sender, DataGridViewCellEventArgs e)
        //{
        //    if (e.ColumnIndex == eadgv_DbZone.Columns[Edit.Index].Index && eadgv_DbZone.Rows[e.RowIndex].Cells[Edit.Index] is DataGridViewButtonCell)
        //    {
        //        SelectedDBZoneToEdit = Data.DBZones.Find(z => z.Name == eadgv_DbZone.Rows[e.RowIndex].Cells[DBZone.Index].Value.ToString());
        //        if (SelectedDBZoneToEdit != null)
        //        {
        //            OpenEditForm(true);
        //        }
        //    }
        //}



        #endregion

        #endregion

        #endregion

        private void btn_ProjectSettings_Click(object sender, EventArgs e)
        {
            using (var frm = new FrmProjectInfoSettings(Data))
            {
                frm.ShowDialog();
                if (frm.ParameterValueChanged)
                {
                    Data.ProjInfo = frm.ProjInfo;
                    Data.Engineer = frm.Engineer;
                    Data.Verifier = frm.Verifier;
                    Data.ReferenceDrawing = frm.ReferenceDrawing;
                    Data.Revision = frm.Revision;
                    Data.SiteDiversity = frm.SiteDiversity;
                    Data.SiteSpareCapacity = frm.SiteSpareCapacity;
                    MakeRequest(RequestId.SaveProjectInfo);
                }
            }

        }

        private void btn_SetDBZone_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(tb_DBZone.Text))
            {
                MessageBox.Show("Zone name cannot be an empty string");
                return;
            }

            foreach (var item in Data.Zones.Select(z => z.ZoneName))
            {
                if (item == tb_DBZone.Text)
                {
                    MessageBox.Show("This DB Zone already exists.\nPlease create different name.");
                    return;
                }
            }

            Data.Zones.Add(new MD_ZoneModel { ZoneName = tb_DBZone.Text });

            tv_DBZones.Nodes.Add(tb_DBZone.Text);

            var dataStorage = DBZoneManager.GetDBZoneDataStorageByName(_uiapp.ActiveUIDocument.Document, tb_DBZone.Text);
            if (dataStorage == null)
            {
                MakeRequest(RequestId.CreateDataStorage);
            }

        }

        private void btn_RemoveDB_Click(object sender, EventArgs e)
        {
            if (tv_DBZones.SelectedNode?.Level != 1)
            {
                MessageBox.Show("Select DB to remove in DB Zone");
            }
            else
            {

                // Remove db from db zone
                var dbzone = Data.Zones.Find(z => z.ZoneName == tv_DBZones.SelectedNode.Parent.Text);
                if (dbzone != null && !dbzone.IsLocked)
                {
                    var selectedDb = dbzone.DBs.Find(d => d.DB.Name == tv_DBZones.SelectedNode.Text);
                    if (selectedDb != null)
                        Data.Zones.Find(z => z.ZoneName == tv_DBZones.SelectedNode.Parent.Text).DBs.Remove(selectedDb);

                    // Clear parameter
                    MD_Helper.GetDBZoneParameter(selectedDb.DB).Set(String.Empty);

                    // Add to orphaned DB
                    Data.OrphanedDBs.Add(selectedDb);

                    // Remove DB from tree view
                    tv_DBZones.Nodes.Remove(tv_DBZones.SelectedNode);

                    // Refresh orphaned list box
                    PopulateOrphanedDBsList(Data);
                }
                else
                {
                    MessageBox.Show("Locked zone cannot be removed.");
                }
            }

            MD_CalculationService.RecalculateData(Data);
            PopulateDBZoneDataGridView(Data);
        }

        public void PopulateOrphanedDBsList(MD_DataModel data)
        {
            lb_OrphanedDB.Items.Clear();

            foreach (var dB_Data in data.OrphanedDBs.OrderBy(db => db.DB.Name))
            {
                lb_OrphanedDB.Items.Add(dB_Data.DB.Name + (dB_Data.IsLocked ? $" (Locked by {dB_Data.CurrentOwner})" : ""));
            }
        }

        private void btn_AddDB_Click(object sender, EventArgs e)
        {
            if (tv_DBZones.SelectedNode == null || tv_DBZones.SelectedNode.Level != 0)
            {
                MessageBox.Show("Please select a DB Zone");
            }
            else
            {
                try
                {
                    var dbZone = Data.Zones.Find(z => z.ZoneName == tv_DBZones.SelectedNode.Text);
                    foreach (var item in lb_OrphanedDB.SelectedItems)
                    {
                        var db = Data.OrphanedDBs.Find(d => d.DB.Name == item.ToString());
                        if (db != null && !db.IsLocked)
                        {
                            // Add to DB Zone tree view
                            tv_DBZones.SelectedNode.Nodes.Add(item.ToString());
                            // Add to DB Zone data
                            dbZone.DBs.Add(db);
                            // Set parameter
                            MD_Helper.GetDBZoneParameter(db.DB).Set(dbZone.ZoneName);

                            // Remove item from Orphaned dbs
                            var dbToRemove = Data.OrphanedDBs.SingleOrDefault(r => r.DB.Name == item.ToString());
                            if (dbToRemove != null)
                                Data.OrphanedDBs.Remove(dbToRemove);
                        }
                    }

                    // Refresh orphaned list box
                    PopulateOrphanedDBsList(Data);

                    tv_DBZones.SelectedNode.Expand();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }

            MD_CalculationService.RecalculateData(Data);
            PopulateDBZoneDataGridView(Data);
        }

        private void tb_DBZone_Enter(object sender, EventArgs e)
        {
            TextboxEnter();
        }

        private void tb_DBZone_Leave(object sender, EventArgs e)
        {
            TextBoxLeave();
        }

        private void TextboxEnter()
        {
            if (tb_DBZone.Text == _zoneNamePlaceholderText)
            {
                tb_DBZone.Text = string.Empty;
                tb_DBZone.ForeColor = System.Drawing.Color.Black;
            }
        }

        private void TextBoxLeave()
        {
            //if (string.IsNullOrWhiteSpace(tb_DBZone.Text) || tb_DBZone..Focused)
            //{
            //    tb_DBZone.Text = _zoneNamePlaceholderText;
            //    tb_DBZone.ForeColor = System.Drawing.Color.DimGray;
            //}
        }

        private void btn_Export_Click(object sender, EventArgs e)
        {
            Autodesk.Revit.UI.TaskDialog mainDialog = new Autodesk.Revit.UI.TaskDialog("Maximum Demand");
            mainDialog.MainContent = "Where do you want to export?";
            mainDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink1, "Excel");
            mainDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink2, "Power CAD");
            mainDialog.CommonButtons = TaskDialogCommonButtons.Cancel;
            TaskDialogResult tResult = mainDialog.Show();

            var doc = _uiapp.ActiveUIDocument.Document;
            string strOutputDir = MD_Constants.DefaultOutputPath;
            if (!Directory.Exists(strOutputDir += @"\Beca MEP Tools\")) Directory.CreateDirectory(strOutputDir);
            if (!Directory.Exists(strOutputDir += @"MD Reports\")) Directory.CreateDirectory(strOutputDir);
            if (!Directory.Exists(strOutputDir += doc.Title + @"\")) Directory.CreateDirectory(strOutputDir);

            switch (tResult)
            {
                case TaskDialogResult.Cancel:
                    return;
                case TaskDialogResult.CommandLink1:
                    MD_ClosedXmlExport.ExportToExcel(doc, Data, strOutputDir);
                    break;
                case TaskDialogResult.CommandLink2:
                    MD_ClosedXmlExport.ExportToPowerCAD(doc, Data, strOutputDir);
                    break;
            }

        }

        private void cb_DensityValues_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selectedValue = cb_DensityValues.SelectedItem.ToString();

            // Convert the selected string value back to the enum
            if (Enum.TryParse(selectedValue, out LoadDensityValue selectedDensityValue))
            {
                // Get the current value from Project Information
                var loadDensityValue = _uiapp.ActiveUIDocument.Document.ProjectInformation
                    .LookupParameter(MD_Constants.ProjectInfoParameterName_Load_Density_Data).AsInteger();

                // Compare the selected value with the value in Project Information
                if ((int)selectedDensityValue != loadDensityValue)
                {
                    SelectedDensityValue = (LoadDensityValue)(int)selectedDensityValue;

                    // The selected value does not match the value in Project Information.
                    DialogResult result = MessageBox.Show(
                        "The selected Load Density data does not match the value in Project Information. Proceeding will update the Project Information. Do you want to continue?",
                        "Confirm Update",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        MakeRequest(RequestId.SetLoadDensityData);

                    }
                    else if (result == DialogResult.No)
                    {
                        // If No selected, set the selected ComboBox item back to the same one before
                        cb_DensityValues.SelectedItem = ((LoadDensityValue)loadDensityValue).ToString();
                    }
                }
            }
            else
            {
                MessageBox.Show("Invalid selection. Please select a valid load density value.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void tb_DiversityPFCapacity_TextChanged(object sender, EventArgs e)
        {
            if (isFormLoading) return;

            if (sender is System.Windows.Forms.TextBox textBox)
            {
                string inputText = textBox.Text.TrimEnd('%');

                // Normalize input to handle cases like ".5" as "0.5"
                if (inputText.StartsWith("."))
                {
                    inputText = "0."; // Add a leading zero to ".5" to make it "0.5"
                }
                textBox.SelectionStart = textBox.Text.Length;

                double parsedValue;

                // Attempt to parse the input value
                if (double.TryParse(inputText, out parsedValue))
                {
                    if (textBox == tb_DiversityLighting || textBox == tb_DiversityPower || textBox == tb_DiversityMech)
                    {
                        // Check if the parsed value is within the acceptable range (0 to 1)
                        if (parsedValue >= 0 && parsedValue <= 100)
                        {
                            // Assign the value to the appropriate property based on the TextBox
                            if (textBox == tb_DiversityLighting)
                            {
                                tb_DiversityLighting.Text = $"{parsedValue.ToString()}%";
                            }
                            else if (textBox == tb_DiversityPower)
                            {
                                tb_DiversityPower.Text = $"{parsedValue.ToString()}%";
                            }
                            else if (textBox == tb_DiversityMech)
                            {
                                tb_DiversityMech.Text = $"{parsedValue.ToString()}%";
                            }
                            
                            textBox.BackColor = SystemColors.Window;
                        }
                        else
                        {
                            textBox.BackColor = System.Drawing.Color.LightCoral;
                            MessageBox.Show("Please enter a value between 0 and 100.", "Invalid Input", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                    else
                    {
                        // Check if the parsed value is within the acceptable range(0 to 1)
                        if (parsedValue >= 0 && parsedValue <= 1)
                        {
                            if (textBox == tb_PFLighting)
                            {
                                tb_PFLighting.Text = parsedValue.ToString();
                            }
                            else if (textBox == tb_PFPower)
                            {
                                tb_PFPower.Text = parsedValue.ToString();
                            }
                            else if (textBox == tb_PFMech)
                            {
                                tb_PFMech.Text = parsedValue.ToString();
                            }
                            else if (textBox == tb_PFLump)
                            {
                                tb_PFLump.Text = parsedValue.ToString();
                            }

                            textBox.BackColor = SystemColors.Window;
                        }
                        else
                        {
                            textBox.BackColor = System.Drawing.Color.LightCoral;
                            MessageBox.Show("Please enter a value between 0 and 1.", "Invalid Input", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }
                else
                {
                    textBox.BackColor = System.Drawing.Color.LightCoral;
                    MessageBox.Show("Please enter the correct numerical value.", "Invalid Input", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }

        private void eadgv_DbZone_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (eadgv_DbZone.Columns[e.ColumnIndex].Name == Edit.Name && eadgv_DbZone.Rows[e.RowIndex].Cells[e.ColumnIndex] is DataGridViewButtonCell)
            {
                // Check if the cell is a button cell
                e.CellStyle.ForeColor = System.Drawing.Color.FromArgb(141, 14, 132); 
                e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold); 
            }
        }

        private void eadgv_DbZone_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == eadgv_DbZone.Columns[Edit.Index].Index && 
                eadgv_DbZone.Rows[e.RowIndex].Cells[Edit.Index] is DataGridViewButtonCell &&
                !eadgv_DbZone.Rows[e.RowIndex].Cells[Edit.Index].Value.ToString().Contains("locked", StringComparison.OrdinalIgnoreCase))
            {
                SelectedDBZoneToEdit = Data.Zones.Find(z => z.ZoneName == eadgv_DbZone.Rows[e.RowIndex].Cells[DBZone.Index].Value.ToString());
                if (SelectedDBZoneToEdit != null)
                {
                    OpenEditForm(true);
                }
            }
            else if (eadgv_DbZone.Rows[e.RowIndex].Cells[Edit.Index].Value.ToString().Contains("locked", StringComparison.OrdinalIgnoreCase))
            {
                var zoneName = eadgv_DbZone.Rows[e.RowIndex].Cells[DBZone.Index].Value.ToString();
                var zone = Data.Zones.Find(z => z.ZoneName == zoneName);

                if (zone == null)
                    return;

                // Build locked DBs and Spaces message
                var lockedDBs = zone.DBs.Where(db => db.IsLocked).Select(db => $"{db.DB.Name} (Locked by {db.CurrentOwner})").ToList();
                var lockedSpaces = zone.Spaces.Where(s => s.IsLocked).Select(s => $"{s.SpaceName} (Locked by {s.CurrentOwner})").ToList();

                var message = new StringBuilder();
                message.AppendLine("Elements in this zone are locked:");
                if (lockedDBs.Any())
                {
                    message.AppendLine("\nDB:");
                    lockedDBs.ForEach(db => message.AppendLine(db));
                }
                if (lockedSpaces.Any())
                {
                    message.AppendLine("\nSpaces:");
                    lockedSpaces.ForEach(space => message.AppendLine(space));
                }

                MessageBox.Show(message.ToString(), "Locked Elements");
            }
        }

        private void btn_Summary_Click(object sender, EventArgs e)
        {
            using (var frmSummary = new FrmSummary(Data))
            {
                frmSummary.StartPosition = FormStartPosition.CenterScreen;
                frmSummary.ShowDialog();
            }
        }
    }

    #region Classes to be Created

    #region Modeless Form Handler 

    public class MainFormModelessHandler
    {
        #region Fields

        static FrmModelessMaxDemandMain _frmModelessMaxDemandMain;

        #endregion

        #region Properties

        public static FrmModelessMaxDemandMain FrmModelessSelection { get => _frmModelessMaxDemandMain; }

        #endregion

        #region Methods

        /// <summary>
        ///   This method creates and shows a modeless dialog, unless it already exists.
        /// </summary>
        /// <remarks>
        ///   The external command invokes this on the end-user's request
        /// </remarks>
        /// 
        public static void ShowForm(UIApplication uiapp, BecaActivityLoggerData logger, MD_DataModel data)
        {
            try
            {
                // If we do not have a dialog yet, create and show it
                if (_frmModelessMaxDemandMain == null || _frmModelessMaxDemandMain.IsDisposed)
                {

                    // A new handler to handle request posting by the dialog
                    RequestHandler handler = new RequestHandler(logger);

                    // External Event for the dialog to use (to post requests)
                    ExternalEvent exEvent = ExternalEvent.Create(handler);

                    // We give the objects to the new dialog;
                    // The dialog becomes the owner responsible fore disposing them, eventually.
                    _frmModelessMaxDemandMain = new FrmModelessMaxDemandMain(exEvent, handler, logger, uiapp, data);
                    _frmModelessMaxDemandMain.Show();
                }
                else
                    _frmModelessMaxDemandMain.Activate();
                //m_MyForm.BringToFront();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            
        }

        public static void SaveProjectInfo(Document doc)
        {
            if (_frmModelessMaxDemandMain != null)
            {
                using (Transaction t = new Transaction(doc))
                {
                    t.Start("Save project info");
                    doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Engineer)?.Set(_frmModelessMaxDemandMain.Data.Engineer);
                    doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Verifier)?.Set(_frmModelessMaxDemandMain.Data.Verifier);

                    doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Site_Spare_Diversity)?.Set(_frmModelessMaxDemandMain.Data.SiteSpareCapacity);
                    doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Site_Diversity)?.Set(_frmModelessMaxDemandMain.Data.SiteDiversity);
                    t.Commit();
                }
            }

        }
        public static void SetLoadDensityData(Document doc)
        {
            if (_frmModelessMaxDemandMain != null)
            {
                using (Transaction trans = new Transaction(doc, "Update Load Density Data"))
                {
                    trans.Start();

                    var param = doc.ProjectInformation.LookupParameter(MD_Constants.ProjectInfoParameterName_Load_Density_Data);
                    if (param != null)
                    {
                        param.Set((int)_frmModelessMaxDemandMain.SelectedDensityValue);
                    }

                    trans.Commit();
                }

                var lookupData = MD_Helper.GetPDLookUpDataFromCSV(doc);
                if (lookupData.Count == 0)
                {
                    MessageBox.Show("PDLookupData is empty");
                }
                else
                {
                    _frmModelessMaxDemandMain.Data.PDLookupData = lookupData;
                }
            }

        }

        public static void CreateNewDataStorage(Document doc)
        {
            var mD_DBZone = _frmModelessMaxDemandMain.Data.Zones.Find(x => x.ZoneName.Equals(_frmModelessMaxDemandMain.tb_DBZone.Text));
            if (mD_DBZone == null)
            {
                return;
            }
            using (Transaction trans = new Transaction(doc, "Create DataStorage"))
            {
                trans.Start();
                DataStorage dataStorage = DataStorage.Create(doc);
                dataStorage.Name = mD_DBZone?.ZoneName;

                Entity entity = new Entity(DBZoneManager.GetSchemaByName(DBZoneManager.SchemaName));

                //double areaInInternalUnits = UnitUtils.Convert(mD_DBZone.ZoneArea, UnitTypeId.SquareMeters, UnitTypeId.SquareMeters);

                entity.Set<string>(FieldNames.Name.ToString(), mD_DBZone.ZoneName);

                entity.Set<int>(FieldNames.GeneratorLighting.ToString(), 0);
                entity.Set<int>(FieldNames.GeneratorLump.ToString(), 0);
                entity.Set<int>(FieldNames.GeneratorMechanical.ToString(), 0);
                entity.Set<int>(FieldNames.GeneratorSmallPower.ToString(), 0);
                entity.Set<int>(FieldNames.UPSLump.ToString(), 0);
                entity.Set<int>(FieldNames.UPSMechanical.ToString(), 0);
                entity.Set<int>(FieldNames.UPSLighting.ToString(), 0);
                entity.Set<int>(FieldNames.UPSSmallPower.ToString(), 0);

                dataStorage.SetEntity(entity);

                mD_DBZone.DBZoneDataStorage = dataStorage;

                trans.Commit();
            }
        }
        public static void SaveDataInTheMainForm(Document doc)
        {
            if (_frmModelessMaxDemandMain != null)
            {
                var data = _frmModelessMaxDemandMain.Data;
                var projectInfo = doc.ProjectInformation;

                // Save data to Revit
                using (var trans = new Transaction(doc, "Set Project info params"))
                {
                    trans.Start();
                    projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Site_Diversity).Set(_frmModelessMaxDemandMain.Data.SiteDiversity);
                    projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Site_Spare_Diversity).Set(_frmModelessMaxDemandMain.Data.SiteSpareCapacity);
                    projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Lighting).Set(_frmModelessMaxDemandMain.Data.DiversityLighting);
                    projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Mech).Set(_frmModelessMaxDemandMain.Data.DiversityMech);
                    projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_Diversity_Power).Set(_frmModelessMaxDemandMain.Data.DiversityPower);
                    projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Lighting).Set(_frmModelessMaxDemandMain.Data.PowerFactorLighting);
                    projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Power).Set(_frmModelessMaxDemandMain.Data.PowerFactorPower);
                    projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Mech).Set(_frmModelessMaxDemandMain.Data.PowerFactorMech);
                    projectInfo.LookupParameter(MD_Constants.ProjectInfoParameterName_PF_Lump).Set(_frmModelessMaxDemandMain.Data.PowerFactorLumpLoad);

                    foreach (var dBZone in data.Zones)
                    {
                        foreach (var dB in dBZone.DBs)
                        {
                            dB.DB.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).SetValueString(dBZone.ZoneName);
                        }
                    }
                    trans.Commit();
                }
            }

        }

        public static void RenameDBZoneInDBsAndSpaces(Document doc)
        {
            using (var trans = new Transaction(doc, "Rename DB Zone"))
            {
                trans.Start();
                var mD_DBZone = _frmModelessMaxDemandMain.SelectedDBZoneToEdit;

                if (mD_DBZone != null)
                {
                    foreach (var dB in mD_DBZone.DBs)
                    {
                        dB.DB.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(mD_DBZone.ZoneName);
                    }

                    foreach (var mDSpace in mD_DBZone.Spaces)
                    {
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(mD_DBZone.ZoneName);
                    }

                    // Rename DB Zone in data storage
                    mD_DBZone.DBZoneDataStorage.Name = mD_DBZone.ZoneName;
                }

                trans.Commit();
            }
        }

        public static void ClearDBZoneParameterInDBsAndSpaces(Document doc)
        {
            using (var trans = new Transaction(doc, "Clear DB Zone"))
            {
                trans.Start();
                var data = _frmModelessMaxDemandMain.Data;
                var mD_DBZone = _frmModelessMaxDemandMain.SelectedDBZoneToEdit;

                // Delete this DB Zone from data storage
                doc.Delete(mD_DBZone.DBZoneDataStorage?.Id);

                if (mD_DBZone != null)
                {
                    foreach (var dB in mD_DBZone.DBs)
                    {
                        // Clear DB Parameters
                        dB.DB.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(string.Empty);
                        dB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Lighting).Set(string.Empty);
                        dB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Lump).Set(string.Empty);
                        dB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Mech).Set(string.Empty);
                        dB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Power_Supply_Class).Set(string.Empty);
                        dB.DB.LookupParameter(MD_Constants.DBParameterName_MD_Small_Power).Set(string.Empty);

                        // Add to orphaned DBs
                        data.OrphanedDBs.Add(dB);
                    }

                    foreach (var mDSpace in mD_DBZone.Spaces)
                    {
                        // Clear Space Parameters
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceDBParameterName_DB_Zone).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Lighting_Power_Density).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_1Phase).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_3Phase).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Lump_Load_Diversity).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Mech_Power_Density).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Small_Power_Density).Set(string.Empty);
                        mDSpace.Space.LookupParameter(MD_Constants.SpaceParameterName_Spatial_Function).Set(string.Empty);

                        // Add to orphaned Spaces
                        data.OrphanedSpaces.Add(mDSpace.Space);
                    }

                    data.Zones.Remove(mD_DBZone);

                    MD_CalculationService.RecalculateData(data);

                    _frmModelessMaxDemandMain.PopulateDBZoneDataGridView(data);
                    _frmModelessMaxDemandMain.PopulateOrphanedDBsList(data);
                }

                trans.Commit();
            }
        }

        /// <summary>
        ///   Waking up the dialog from its waiting state.
        /// </summary>
        /// 
        public static void WakeFormUp()
        {
            if (_frmModelessMaxDemandMain != null)
            {
                _frmModelessMaxDemandMain.WakeUp();
            }
        }

        /// <summary>
        /// Must be called in OnShutdown(UIControlledApplication a) Event of the App command.
        /// </summary>
        public static void OnRevitShutDown()
        {
            if (_frmModelessMaxDemandMain != null)
            {
                _frmModelessMaxDemandMain.Close();
            }
        }

        #endregion

    }

    #endregion

    #endregion

}