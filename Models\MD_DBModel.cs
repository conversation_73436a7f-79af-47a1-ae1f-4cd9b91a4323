﻿using Autodesk.Revit.DB.Electrical;
using BecaRevitUtilities;
using BecaRevitUtilities.ElementUtilities;
using MEP.MaxDemand.CoreLogic;

namespace MEP.MaxDemand.Models
{
    public partial class MD_DBModel : ObservableObject
    {
        #region Fields
        List<ElectricalSystem> _circuits;
        #endregion

        #region Properties
        // Default selection for Power Supply Class dropdown control
        public List<string> PowerSupplyOptions { get; } = new List<string> { "G", "E", "U" };

        // DB Input: Distribution Board Information (From Revit)
        [ObservableProperty]
        private FamilyInstance dB;
        [ObservableProperty]
        private string dBName;
        [ObservableProperty]
        private PartType partType;
        [ObservableProperty]
        private Level level;

        // DB Input: Distribution Board Information (User selection)
        [ObservableProperty]
        private string powerSupplyClass;
        [ObservableProperty]
        private bool isLighting;
        [ObservableProperty]
        private bool isLump;
        [ObservableProperty]
        private bool isMech;
        [ObservableProperty]
        private bool isSmallPower;

        // DB Input: Distribution Board Information (Calculated on property change)
        [ObservableProperty]
        private double powerFactor;
        [ObservableProperty]
        private double diversifiedPerPhaseCurrent;
        [ObservableProperty]
        private double dBCableLength;
        [ObservableProperty]
        private FamilyInstance parentDB;
        [ObservableProperty]
        private string parentDBName;

        // Additinal Info
        [ObservableProperty]
        private string notes;
        [ObservableProperty]
        private bool isLocked;
        [ObservableProperty]
        private string currentOwner;

        // Diversified Load Values: General (Calculated on property change)
        [ObservableProperty]
        private double generalSmallPowerLoad;
        [ObservableProperty]
        private double generalLightingLoad;
        [ObservableProperty]
        private double generalMechanicalLoad;
        [ObservableProperty]
        private double generalLumpLoad;

        // Diversified Load Values: Essential (Calculated on property change)
        [ObservableProperty]
        private double essentialSmallPowerLoad;
        [ObservableProperty]
        private double essentialLightingLoad;
        [ObservableProperty]
        private double essentialMechanicalLoad;
        [ObservableProperty]
        private double essentialLumpLoad;

        // Diversified Load Values: Uninteruptable (Calculated on property change)
        [ObservableProperty]
        private double uninteruptableSmallPowerLoad;
        [ObservableProperty]
        private double uninteruptableLightingLoad;
        [ObservableProperty]
        private double uninteruptableMechanicalLoad;
        [ObservableProperty]
        private double uninteruptableLumpLoad;

        // Summary
        [ObservableProperty]
        private double totalDiversifiedLoad_kVA;
        [ObservableProperty]
        private double totalDiversifiedLoad_kW;

        // Read-only properties for checkbox symbols for Excel input
        public string SmallPowerSymbol => IsSmallPower ? char.ConvertFromUtf32(9745) : char.ConvertFromUtf32(9744);
        public string LightingSymbol => IsLighting ? char.ConvertFromUtf32(9745) : char.ConvertFromUtf32(9744);
        public string MechSymbol => IsMech ? char.ConvertFromUtf32(9745) : char.ConvertFromUtf32(9744);
        public string LumpSymbol => IsLump ? char.ConvertFromUtf32(9745) : char.ConvertFromUtf32(9744);

        // Remove properties below after migrating to MVVM
        public double DiversifiedLoad_kVA { get; set; }
        public double DiversifiedLoad_kW { get; set; }
        #endregion

        #region Constructor
        public MD_DBModel(FamilyInstance dB, PartType partType, DBsAndCircuits dBsAndCircuits)
        {
            DB = dB;
            DBName = dB.Name;
            Level = dB.Document.GetElement(dB.LevelId) as Level;
            IsLighting = dB.LookupParameter(MD_Constants.DBParameterName_MD_Lighting)?.AsInteger() == 1;
            IsLump = dB.LookupParameter(MD_Constants.DBParameterName_MD_Lump)?.AsInteger() == 1;
            IsMech = dB.LookupParameter(MD_Constants.DBParameterName_MD_Mech)?.AsInteger() == 1;
            IsSmallPower = dB.LookupParameter(MD_Constants.DBParameterName_MD_Small_Power)?.AsInteger() == 1;

            var powerSupplyClassValue = dB.LookupParameter(MD_Constants.DBParameterName_MD_Power_Supply_Class).AsString();
            PowerSupplyClass = string.IsNullOrEmpty(powerSupplyClassValue) ? "G" : powerSupplyClassValue;

            PartType = partType;

            _circuits = dBsAndCircuits.AllCircuits;
            ParentDB = dBsAndCircuits.DBNameDictionary.TryGetValue(DB.get_Parameter(BuiltInParameter.RBS_ELEC_PANEL_SUPPLY_FROM_PARAM)?.AsString(), out var parentDB) ? parentDB : null;
            ParentDBName = ParentDB?.Name;
            DBCableLength = ParentDB != null ? GetTotalCircuitLengthFromDB() : 0.0; 

            var doc = DB.Document;
            IsLocked = DB.IsLocked(doc);
            CurrentOwner = DB.ElementOwner(doc);

            Notes = dB.LookupParameter("Beca Comments").AsValueString();
        }
        #endregion

        #region Methods
        private double GetTotalCircuitLengthFromDB()
        {
            return _circuits.Where(circuit => circuit.Elements.OfType<FamilyInstance>().Any(fi => fi.GetElementIdValue() == DB.GetElementIdValue()))
                .Select(circuit => RevitUnitConvertor.InternalToMeters(circuit.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_LENGTH_PARAM)?.AsDouble() ?? 0.0))
                .FirstOrDefault();
        }
        #endregion
    }
}
