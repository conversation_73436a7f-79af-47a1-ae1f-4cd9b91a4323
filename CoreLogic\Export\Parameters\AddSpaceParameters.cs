﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.MaxDemand.CoreLogic.Export
{
    public class AddSpaceParameters : MacroParameters
    {
        public int TargetRow { get; set; }
        public string SpaceName { get; set; } = "";
        public string SpaceLevel { get; set; } = "";
        public double SpaceArea { get; set; } = 0;
        public string SpaceFunction { get; set; } = "";
        public double LumpSingle { get; set; } = 0;
        public double LumpThree { get; set; } = 0;
        public double LumpDiversity { get; set; } = 0.5;

        public override object[] Parameters => new object[] { TargetRow, SpaceName, SpaceLevel, SpaceArea, SpaceFunction, LumpSingle, LumpThree, LumpDiversity };
    }
}
